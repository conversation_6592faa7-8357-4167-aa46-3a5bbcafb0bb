import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';

import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

extension UInt8ListConvert on ByteData {
  Uint8List toUInt8List() => buffer.asUint8List(offsetInBytes, lengthInBytes);
}

extension SavingImage on Image {
  Future<File?> saveTemporaryPng({required String filename}) async {
    assert(filename.endsWith('.png'));

    final pngData = await toByteData(format: ImageByteFormat.png);

    if (pngData == null) {
      return null;
    }

    final dir = await getTemporaryDirectory();
    final file = File(join(dir.path, filename));
    file.writeAsBytesSync(pngData.toUInt8List());

    return file;
  }
}
