import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';

class SliverSubHeadingTile extends StatelessWidget {
  const SliverSubHeadingTile({
    super.key,
    required this.title,
    required this.sliver,
  });

  final String title;
  final Widget sliver;

  @override
  Widget build(BuildContext context) {
    return SliverStickyHeader(
      sliver: sliver,
      header: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Padding(
          padding: const EdgeInsets.only(top: 32, left: 16, bottom: 8),
          child: Text(
            title,
            style: SliverSubHeadingTile.style(context),
          ),
        ),
      ),
    );
  }

  static TextStyle style(BuildContext context) => Theme.of(context)
      .textTheme
      .titleMedium!
      .copyWith(fontWeight: FontWeight.w600);
}
