import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/helper_tooltip.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/rank.dart';
import 'graphs/counter_graph.dart';
import 'graphs/graph_theme.dart';
import 'graphs/listening_percent_graph.dart';

class CountryStatsBar extends StatelessWidget with GraphTheme {
  const CountryStatsBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Row(
              children: [
                Expanded(flex: 4, child: _buildCountryCount(context)),
                buildVerticalDivider(),
                Expanded(flex: 5, child: _buildRankBadge(context)),
                buildVerticalDivider(),
                Expanded(flex: 4, child: _buildPercentOfWorldGraph(context)),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildRankBadge(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return HelperTooltip(
      message: localizations.progressTooltipRank,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: SvgPicture.asset(
              'assets/images/ranking_decoration.svg',
              colorFilter: ColorFilter.mode(
                Theme.of(context).colorScheme.primary,
                BlendMode.srcIn,
              ),
              height: 90,
            ),
          ),
          Positioned(
              top: 5,
              child: Text(
                localizations.top,
                style: subtitleStyle(context),
              )),
          StreamBuilder<Rank>(
            stream: DependencyInjector.areaSelectionBloc.rank,
            builder: (context, snapshot) {
              final rank = snapshot.data;
              if (rank == null) {
                return const SizedBox();
              }

              return Text(
                '${rank.percentage}%',
                style: graphStyle(context),
              );
            },
          ),
          _buildTooltipIcon(context)
        ],
      ),
    );
  }

  Widget _buildTooltipIcon(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: Icon(
        Icons.help_outline,
        size: 15,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildCountryCount(BuildContext context) {
    return CounterGraph(
      count: DependencyInjector.areaSelectionBloc.numberOfCountriesBeenTo,
      label: AppLocalizations.of(context)!.countries,
    );
  }

  Widget _buildPercentOfWorldGraph(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return HelperTooltip(
      message: localizations.progressTooltipPercentageOfWorld,
      child: Stack(
        children: [
          ListeningPercentGraph(
            percentage: DependencyInjector.areaSelectionBloc.percentOfWorldSeen,
            label: localizations.ofTheWorld,
          ),
          _buildTooltipIcon(context)
        ],
      ),
    );
  }
}
