import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/tutorial_dialog.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import 'inspiration_bloc.dart';
import 'inspiration_card_deck.dart';
import 'inspiration_history_screen.dart';
import 'inspiration_out_of_card.dart';

class InspirationScreen extends StatefulWidget {
  const InspirationScreen({
    super.key,
    this.area,
    this.showHistory = true,
  });

  final GeoArea? area;
  final bool showHistory;

  @override
  State<InspirationScreen> createState() => _InspirationScreenState();
}

class _InspirationScreenState extends State<InspirationScreen> {
  late final bloc = InspirationBloc(
    selectionBloc: DependencyInjector.areaSelectionBloc,
    area: widget.area,
    delegate: DependencyInjector.itineraryBloc,
  );

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final tutorial = TutorialDialog(
        items: [
          TutorialItem(
            label: AppLocalizations.of(context)!.tutorialInspirations,
            icon: SvgPicture.asset(
              'assets/images/swipe.svg',
              width: 100,
              colorFilter: ColorFilter.mode(
                Theme.of(context).colorScheme.primary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      );

      tutorial.showOnlyOnce(
        context: context,
        hasShownKey: 'com.visited.hasHShownInspirationTutorial',
      );
    });
  }

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context, bloc),
      body: SafeArea(
        child: Stack(
          alignment: Alignment.center,
          children: [
            InspirationOutOfCards(inspirationBloc: bloc),
            InspirationCardDeck(
              area: widget.area,
              inspirationBloc: bloc,
            ),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context, InspirationBloc bloc) {
    return AppBar(
      title: Text(
        AppLocalizations.of(context)!.inspirations,
        style: TextStyle(color: Theme.of(context).primaryColor),
      ),
      foregroundColor: Theme.of(context).primaryColor,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      elevation: 0,
      actions: [
        if (widget.showHistory)
          IconButton(
            icon: const Icon(Icons.collections),
            onPressed: () => Navigator.of(context).pushMaterialRoute(
              name: InspirationHistoryScreen.routeName,
              builder: (context) => InspirationHistoryScreen(
                inspirationBloc: bloc,
              ),
            ),
            color: Theme.of(context).primaryColor,
          )
      ],
    );
  }
}
