import 'dart:async';

import 'package:rxdart/subjects.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/geo_area.dart';
import '../selection/area_selection_bloc.dart';
import 'inspiration.dart';
import 'inspiration_selection_type.dart';
import 'inspiration_service.dart';

abstract class OnInspirationUpdatedDelegate {
  void onInspirationSelected(
    Inspiration inspiration,
    InspirationSelectionType selection,
  );
}

class InspirationBloc implements Bloc {
  InspirationBloc({
    required AreaSelectionBloc selectionBloc,
    this.area,
    this.delegate,
  })  : _selectionBloc = selectionBloc,
        _service = DependencyInjector.inspirationService {
    _feedController.onListen = fetchMoreInspirations;
    _languageSubscription =
        DependencyInjector.settingsBloc.language.listen((_) {
      _service.clearCache();
      _selectionController.add({});
      fetchMoreInspirations();
      _hasFetchedHistorical = false;
      feedSelections();
    });
  }

  late StreamSubscription _languageSubscription;
  final OnInspirationUpdatedDelegate? delegate;
  final GeoArea? area;
  final AreaSelectionBloc _selectionBloc;
  var _hasFetchedHistorical = false;
  final InspirationService _service;
  final _feedController =
      BehaviorSubject<InspirationEvent>.seeded(InspirationLoadingEvent());

  final _selectionController =
      BehaviorSubject<Map<InspirationSelectionType, Set<Inspiration>>>();

  Stream<InspirationEvent> get events => _feedController.stream;

  Stream<Map<InspirationSelectionType, Set<Inspiration>>>
      get selectionsStream => _selectionController.stream;

  void fetchMoreInspirations() async {
    _feedController.add(InspirationLoadingEvent());
    final inspirations = await _service.fetchInspirations(area: area);
    _feedController.sink.add(InspirationCardsEvent(inspirations));
  }

  void select(
    Inspiration inspiration,
    InspirationSelectionType type,
  ) async {
    final selections = _cachedSelections();
    final existingSelection = selection(inspiration);

    // First just update the UI as fast as possible
    if (existingSelection != null) {
      selections[existingSelection]?.remove(inspiration);
    }

    if (type != InspirationSelectionType.delete) {
      selections.containsKey(type)
          ? selections[type]?.add(inspiration)
          : selections[type] = {inspiration};
    }

    _selectionController.sink.add(selections);
    delegate?.onInspirationSelected(inspiration, type);

    // Now update the backend
    final updatedAreaSelections = await _service.select(inspiration, type);
    if (updatedAreaSelections != null) {
      _selectionBloc.acceptUpdates(updatedAreaSelections);
    }
  }

  Future<void> feedSelections() async {
    if (_hasFetchedHistorical) {
      return;
    }

    final selections = await _service.fetchSelections();
    _hasFetchedHistorical = true;

    final existing = _cachedSelections();

    for (final selection in selections.entries) {
      existing.containsKey(selection.key)
          ? existing[selection.key]?.addAll(selection.value)
          : existing[selection.key] = selection.value;
    }

    _selectionController.sink.add(existing);
  }

  Map<InspirationSelectionType, Set<Inspiration>> _cachedSelections() =>
      _selectionController.valueOrNull ??
      <InspirationSelectionType, Set<Inspiration>>{};

  InspirationSelectionType? selection(Inspiration inspiration) {
    final currentSelections = _cachedSelections();
    for (final type in InspirationSelectionType.values) {
      if (currentSelections[type]?.contains(inspiration) ?? false) {
        return type;
      }
    }
    return null;
  }

  @override
  void clear() {
    _selectionController.add({});
  }

  @override
  void dispose() {
    _feedController.close();
    _selectionController.close();
    _languageSubscription.cancel();
  }
}

sealed class InspirationEvent {}

class InspirationLoadingEvent implements InspirationEvent {}

class InspirationCardsEvent implements InspirationEvent {
  final List<Inspiration> inspirations;
  const InspirationCardsEvent(this.inspirations);
}
