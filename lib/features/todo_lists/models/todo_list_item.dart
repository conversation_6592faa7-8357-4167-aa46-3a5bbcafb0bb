import '../../../generic_widgets/selectable_item.dart';
import '../../../models/coordinate.dart';
import 'todo_list.dart';

class TodoAreaListItem extends TodoListItem {
  final TodoList parentList;

  TodoAreaListItem({
    required TodoListItem item,
    required this.parentList,
  }) : super(
          id: item.id,
          name: item.name,
          isoCode: item.isoCode,
          thumbnailUrl: item.thumbnailUrl,
          coordinate: item.coordinate,
          listIds: item.listIds,
          popularity: item.popularity,
        );

  TodoAreaListItem.fromJson(
      super.json, {
      required this.parentList,
  }) : super.fromJson();

  static int? listId(Map json) => json['listIds']?.cast<int>().first;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is TodoAreaListItem &&
          runtimeType == other.runtimeType &&
          parentList == other.parentList;

  @override
  int get hashCode => Object.hash(super.hashCode, parentList);
}

class TodoListItem implements SelectableItem, MapDisplayable {
  @override
  final int id;

  @override
  final String name;

  @override
  final Coordinate coordinate;

  final String? thumbnailUrl;
  final String? isoCode;
  final Set<int>? listIds;
  final int popularity;
  final int? cityId;

  const TodoListItem({
    required this.id,
    required this.name,
    required this.coordinate,
    required this.popularity,
    this.thumbnailUrl,
    this.isoCode,
    this.listIds,
    this.cityId,
  });

  TodoListItem.fromJson(Map json)
      : id = json['id'],
        name = json['name'],
        thumbnailUrl = json['imageUrl'],
        isoCode = json['isoCode'],
        coordinate = Coordinate.fromList(json['coordinate']),
        listIds = json['listIds']?.cast<int>().toSet(),
        popularity = json['popularity'],
        cityId = json['cityId'];

  @override
  String toString() {
    return 'TodoListItem{id: $id, name: $name}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TodoListItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  List<String> get searchKeywords => [
        name,
        if (isoCode != null) isoCode!,
      ];

  TodoListItem copy({Set<int>? listIds}) => TodoListItem(
        id: id,
        name: name,
        thumbnailUrl: thumbnailUrl,
        isoCode: isoCode,
        coordinate: coordinate,
        popularity: popularity,
        listIds: listIds ?? this.listIds,
      );

  bool get belongsToMultipleLists => (listIds?.length ?? 0) > 1;

  List<TodoListItem> expand() {
    final listIds = this.listIds;
    if (listIds == null || !belongsToMultipleLists) {
      return [this];
    }

    return [
      for (final id in listIds) copy(listIds: {id})
    ];
  }

  String userFacingName() {
    final item = this;
    final buffer = StringBuffer();

    if (item is TodoAreaListItem) {
      buffer.writeln('${item.parentList.name}:');
    }

    buffer.writeln(item.name.trim());
    return buffer.toString();
  }
}
