import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:rxdart/rxdart.dart';
import 'package:share_plus/share_plus.dart';

import '../../caching/resettable_behaviour_subject.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../books/book_link.dart';
import '../experiences/experience_bloc.dart';
import '../experiences/models/experience.dart';
import '../inspiration/inspiration.dart';
import '../inspiration/inspiration_bloc.dart';
import '../inspiration/inspiration_selection_type.dart';
import '../todo_lists/models/todo_list.dart';
import '../todo_lists/models/todo_list_item.dart';
import '../todo_lists/models/todo_list_type.dart';
import '../todo_lists/todo_list_bloc.dart';
import 'itinerary.dart';
import 'itinerary_notes.dart';
import 'itinerary_service.dart';
import 'itinerary_summary.dart';

class ItineraryBloc
    implements
        Bloc,
        OnListUpdatedDelegate,
        OnInspirationUpdatedDelegate,
        OnExperiencesSelectedDelegate {
  ItineraryBloc() {
    DependencyInjector.settingsBloc.language.listen((_) {
      _updateToCurrentLanguage();
    });
  }

  final _service = ItineraryService(
    DependencyInjector.client,
    DependencyInjector.areaRepository,
  );

  late final _summaryController =
      ResettableBehaviorSubject<List<ItinerarySummary>>()
        ..onListen = _fetchSummaries;

  late final _itineraries =
      ResettableBehaviorSubject<Map<GeoArea, Itinerary>>();

  final _notes = <GeoArea, ItineraryNotes>{};

  Stream<List<ItinerarySummary>> get summaries {
    if (!_summaryController.hasValue) {
      _fetchSummaries();
    }

    return _summaryController.stream;
  }

  void _fetchSummaries({bool force = false}) async {
    if (!force && _summaryController.hasValue) {
      return;
    }

    final summaries = await _service.fetchSummaries();
    _summaryController.add(summaries);
  }

  Stream<Itinerary> fetch(GeoArea area) async* {
    final current = _itineraries.valueOrNull?[area];
    if (current == null) {
      final itinerary = await _service.fetchItinerary(area);
      final cached = _itineraries.valueOrNull ?? {};
      cached[area] = itinerary;
      _itineraries.add(cached);
    }

    yield* _itineraries.map((event) => event[area]).whereNotNull();
  }

  Future<ItineraryNotes> fetchNotes(GeoArea area) async {
    final current = _notes[area];
    if (current != null) {
      return current;
    }

    final notes = await _service.fetchNotes(area) ?? ItineraryNotes.emtpy();
    _notes[area] = notes;
    return notes;
  }

  Future<bool> saveNotes(GeoArea area, ItineraryNotes notes) {
    _notes[area] = notes;
    return _service.saveItineraryNotes(area, notes);
  }

  @override
  void onListItemUpdated(
    TodoListItem item,
    TodoList list,
    Selection selection,
  ) async {
    final area = await _findArea(item.isoCode);
    if (area == null) {
      return;
    }

    final insert = selection == Selection.want;
    _updateSummary(area, insert);

    final record = _findItinerariesRecord(area);
    if (record == null) {
      return;
    }
    final (itineraries, itinerary) = record;

    // Find the type associated with this list item
    final list = (await DependencyInjector.todoListBloc.fetchLists())
        .firstWhereOrNull((list) => item.listIds?.contains(list.id) ?? false);

    if (list == null) {
      return;
    }

    var updated = itinerary;
    if (list.type == TodoListType.place) {
      final places = itinerary.places ?? [];
      insert ? places.add(item) : places.remove(item);
      updated = itinerary.copyWith(places: places);
    } else if (list.type == TodoListType.food) {
      final food = itinerary.food ?? [];
      insert ? food.add(item) : food.remove(item);
      updated = itinerary.copyWith(food: food);
    } else if (list.type == TodoListType.cities) {
      final cityId = item.cityId;
      if (cityId == null) {
        return;
      }

      final city = await DependencyInjector.cityBloc.fetchCity(cityId);
      if (city == null) {
        return;
      }

      final cities = itinerary.cities ?? [];
      insert ? cities.add(city) : cities.remove(city);
      updated = itinerary.copyWith(cities: cities);
    }

    itineraries[area] = updated;
    _itineraries.add(itineraries);
  }

  @override
  void onInspirationSelected(
    Inspiration inspiration,
    InspirationSelectionType selection,
  ) async {
    final area = await _findArea(inspiration.area.isoCode);
    if (area == null) {
      return;
    }

    final insert = selection == InspirationSelectionType.want;
    _updateSummary(area, insert);

    final record = _findItinerariesRecord(area);
    if (record == null) {
      return;
    }
    final (itineraries, itinerary) = record;

    final inspirations = itinerary.inspirations ?? [];
    insert ? inspirations.add(inspiration) : inspirations.remove(inspiration);
    final updated = itinerary.copyWith(inspirations: inspirations);
    itineraries[area] = updated;
    _itineraries.add(itineraries);
  }

  @override
  void onExperienceSelected(
    Experience experience,
    GeoArea area,
    Selection selection,
  ) async {
    final insert = selection == Selection.want;
    _updateSummary(area, insert);

    final record = _findItinerariesRecord(area);
    if (record == null) {
      return;
    }
    final (itineraries, itinerary) = record;

    final experiences = itinerary.experiences ?? [];
    insert ? experiences.add(experience) : experiences.remove(experience);
    final updated = itinerary.copyWith(experiences: experiences.sorted());
    itineraries[area] = updated;
    _itineraries.add(itineraries);
  }

  (Map<GeoArea, Itinerary>, Itinerary)? _findItinerariesRecord(GeoArea area) {
    final itineraries = _itineraries.valueOrNull;
    final itinerary = itineraries?[area];
    if (itineraries == null || itinerary == null) {
      return null;
    }

    return (itineraries, itinerary);
  }

  Future<GeoArea?> _findArea(String? isoCode) async {
    if (isoCode == null) {
      return Future.value(null);
    }

    final repo = DependencyInjector.areaRepository;
    final area = await repo.fetchByIsoCode(isoCode);
    if (area == null) {
      return null;
    }

    if (area.parentId != null) {
      return repo.fetchById(area.parentId!);
    }

    return area;
  }

  void _updateSummary(GeoArea area, bool insert) {
    final summaries = _summaryController.valueOrNull;
    if (summaries == null) {
      return;
    }

    final record = summaries.firstWhereOrNullWithIndex(
      (summary) => summary.area == area,
    );

    if (record == null) {
      _createNewSummary(area, summaries);
      return;
    }

    final (summary, index) = record;

    final count = summary.amount + (insert ? 1 : -1);
    final updated = summary.copyWith(amount: count);
    summaries[index] = updated;

    _summaryController.add(summaries);
  }

  Future<void> _createNewSummary(
    GeoArea area,
    List<ItinerarySummary> summaries,
  ) async {
    final details = await DependencyInjector.areaService.fetchDetails(
      area,
      resolution: DependencyInjector.environment.pixelDensity,
    );
    final summary = ItinerarySummary(
      area: area,
      amount: 1,
      thumbnailUrl: details.thumbnailUrl,
    );
    summaries.add(summary);
    _summaryController.add(summaries);
  }

  Future<List<(TodoListItem, TodoList?)>?> pairListItemsWithParentList(
    List<TodoListItem>? items,
    TodoListBloc listBloc,
  ) async {
    if (items == null || items.isEmpty) {
      return null;
    }
    final lists = await listBloc.fetchLists();

    return items
        .map(
          (item) => (
            item,
            lists.firstWhereOrNull((list) => item.listIds?.first == list.id),
          ),
        )
        .toList();
  }

  void share({
    required Itinerary itinerary,
    required TodoListBloc listBloc,
    required AppLocalizations localizations,
  }) async {
    final buffer = StringBuffer();
    buffer.writeln('${localizations.appName} ${localizations.itinerary}');
    buffer.writeln(itinerary.area.name);
    buffer.writeln();

    final notes = await fetchNotes(itinerary.area);
    final dateFormatter = DateFormat.yMMMd();
    final start = notes.startDate;
    if (start != null) {
      buffer.writeln(localizations.departureDate);
      buffer.writeln(dateFormatter.format(start));
      buffer.writeln();
    }

    final end = notes.endDate;
    if (end != null) {
      buffer.writeln(localizations.returnDate);
      buffer.writeln(dateFormatter.format(end));
      buffer.writeln();
    }

    final hotels = notes.hotels;
    if (hotels.isNotEmpty) {
      buffer.writeln(localizations.hotels);
      for (final hotel in hotels) {
        buffer.writeln('- $hotel');
      }
      buffer.writeln();
    }

    final actualNotes = notes.notes;
    if (actualNotes.isNotEmpty) {
      buffer.writeln(localizations.notes);
      buffer.writeAll(actualNotes, '\n\n');
      buffer.writeln();
    }

    final cities = itinerary.cities;
    if (cities != null && cities.isNotEmpty) {
      buffer.writeln(localizations.cities);
      for (final city in cities) {
        buffer.writeln('- ${city.name}');
      }
    }

    await printListItemsToBuffer(
      itinerary.places,
      localizations.places,
      listBloc,
      buffer,
      localizations,
    );

    await printListItemsToBuffer(
      itinerary.food,
      localizations.food,
      listBloc,
      buffer,
      localizations,
    );

    final experiences = itinerary.experiences;
    if (experiences != null && experiences.isNotEmpty) {
      buffer.writeln(localizations.experiences);
      for (final experience in experiences) {
        buffer.writeln('- ${experience.name}');
      }
    }

    SharePlus.instance.share(
      ShareParams(
        text: buffer.toString(),
        subject:
            '${localizations.appName} ${localizations.itinerary}: ${itinerary.area.name}',
      ),
    );
  }

  Future<void> printListItemsToBuffer(
    List<TodoListItem>? items,
    String title,
    TodoListBloc listBloc,
    StringBuffer buffer,
    AppLocalizations localizations,
  ) async {
    final places = await pairListItemsWithParentList(items, listBloc);
    if (places != null && places.isNotEmpty) {
      buffer.writeln(title);
      for (final place in places) {
        final (item, list) = place;
        final bulletPoint = StringBuffer();
        bulletPoint.write('- ');
        bulletPoint.write(item.name);
        if (list != null) {
          bulletPoint.write(' (');
          bulletPoint.write(list.name);
          bulletPoint.write(')');
        }

        buffer.writeln(bulletPoint.toString());
      }
      buffer.writeln();
    }
  }

  Future<BookLink?> fetchLink(Itinerary itinerary) {
    return DependencyInjector.bookService.fetchAreaBookLink(itinerary.area);
  }

  @override
  void clear() {
    _summaryController.reset();
    _itineraries.reset();
    _notes.clear();
  }

  @override
  void dispose() {
    _itineraries.close();
    _summaryController.close();
    _notes.clear();
  }

  void _updateToCurrentLanguage() async {
    final summaries = _summaryController.valueOrNull;
    if (summaries != null) {
      await Future.delayed(const Duration(milliseconds: 50));
      final updated = <ItinerarySummary>[];
      for (final summary in summaries) {
        final area = await DependencyInjector.areaBloc.areaByIsoCode(
          summary.area.isoCode,
        );
        if (area != null) {
          updated.add(summary.copyWith(area: area));
        }
      }

      _summaryController.add(updated);
    }

    final itineraries = _itineraries.valueOrNull;
    if (itineraries != null) {
      await Future.delayed(const Duration(milliseconds: 50));
      final updated = <GeoArea, Itinerary>{};
      for (final entry in itineraries.entries) {
        final area = await DependencyInjector.areaBloc.areaByIsoCode(
          entry.key.isoCode,
        );

        if (area == null) {
          continue;
        }

        final itinerary = await _service.fetchItinerary(area);
        updated[area] = itinerary;
      }

      _itineraries.add(updated);
    }
  }
}

extension ListExtensions<T> on List<T> {
  (T, int)? firstWhereOrNullWithIndex(bool Function(T element) test) {
    if (isEmpty) {
      return null;
    }

    for (var i = 0; i <= length; i++) {
      final element = this[i];
      if (test(element)) {
        return (element, i);
      }
    }

    return null;
  }
}
