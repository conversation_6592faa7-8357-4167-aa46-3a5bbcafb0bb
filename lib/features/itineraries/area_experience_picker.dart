import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/animated_checkmark.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/pushable_tile.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../experiences/models/experience.dart';
import '../experiences/widgets/preferred_experiences_screen.dart';

class AreaExperiencePicker extends StatefulWidget {
  const AreaExperiencePicker({
    super.key,
    required this.area,
  });

  final GeoArea area;

  @override
  State<AreaExperiencePicker> createState() => _AreaExperiencePickerState();
}

class _AreaExperiencePickerState extends State<AreaExperiencePicker> {
  List<Experience>? experiences;
  final selected = <Experience>{};

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (experiences != null) {
      return;
    }

    DependencyInjector.experienceBloc
        .unselectedExperiencesForArea(widget.area)
        .then((value) {
      setState(() {
        experiences = value;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          slivers: [
            PlatformSliverAppBar(
              title: AppLocalizations.of(context)!.experiences,
            ),
            const SliverExperienceDescription(),
            if (experiences == null)
              _buildLoading()
            else if (experiences!.isEmpty)
              _buildError()
            else
              _buildExperiences(),
            const SliverBottomSafeArea(),
          ],
        ),
      ),
    );
  }

  SliverList _buildExperiences() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(childCount: experiences!.length,
          (context, index) {
        final experience = experiences![index];
        return PushableTile(
          title: experience.name,
          imageLocation: AssetImageLocation(
            experience.icon,
          ),
          onTapped: () {
            setState(() {
              final remove = selected.contains(experience);
              remove ? selected.remove(experience) : selected.add(experience);

              final selection = remove ? Selection.clear : Selection.want;
              DependencyInjector.experienceBloc.select(
                experience: experience,
                area: widget.area,
                selection: selection,
              );
            });
          },
          trailing: AnimatedCheckmark(checked: selected.contains(experience)),
        );
      }),
    );
  }

  SliverFillRemaining _buildError() {
    return const SliverFillRemaining(
      child: Center(
        child: Text('No Experiences currently available'),
      ),
    );
  }

  SliverFillRemaining _buildLoading() {
    return const SliverFillRemaining(
      child: Center(
        child: Spinner(),
      ),
    );
  }
}
