import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../generic_widgets/list_view_header.dart';
import '../../generic_widgets/responsive_padding.dart';
import 'add_more_sliver.dart';

class MultipleNotesSliver extends StatefulWidget {
  const MultipleNotesSliver({
    super.key,
    required this.notes,
    required this.title,
    required this.maxLength,
    this.maxLines = 1,
  });

  final int maxLength;
  final String title;
  final int maxLines;
  final List<String> notes;

  @override
  State<MultipleNotesSliver> createState() => _MultipleNotesSliverState();
}

class _MultipleNotesSliverState extends State<MultipleNotesSliver> {
  late final List<FocusNode> focusNodes;

  @override
  void initState() {
    super.initState();

    focusNodes = List.generate(
      widget.notes.length,
      (_) => FocusNode(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SliverStickyHeader(
      header: ListViewHeader(
        title: widget.title,
      ),
      sliver: ResponsiveSliverPadding(
        context: context,
        fillToEdgeOnPhone: false,
        sliver: MultiSliver(
          children: [
            if (widget.notes.isNotEmpty && focusNodes.isNotEmpty) _buildNotes(),
            _buildAddMore()
          ],
        ),
      ),
    );
  }

  Widget _buildAddMore() {
    return AddMoreSliver(
      onTapped: () async {
        // Only add a new row if the last row is not empty
        if (widget.notes.isNotEmpty && widget.notes.last.isEmpty) {
          return;
        }
        final node = FocusNode();

        setState(
          () {
            focusNodes.add(node);
            widget.notes.add('');
          },
        );

        await Future.delayed(const Duration(milliseconds: 100));
        node.requestFocus();
      },
    );
  }

  Widget _buildNotes() {
    return SliverList.list(
      children: [
        for (var i = 0; i < widget.notes.length; i++) _buildNoteTile(context, i)
      ],
      // itemCount: widget.notes.length,
      // itemBuilder: _buildNoteTile,
    );
  }

  Widget _buildNoteTile(BuildContext context, int index) {
    return Dismissible(
      key: ValueKey(index),
      direction: DismissDirection.endToStart,
      background: Container(
        color: Theme.of(context).colorScheme.error,
        child: const Align(
          alignment: Alignment(0.9, 0.0),
          child: Icon(Icons.delete),
        ),
      ),
      onDismissed: (_) => _deleteTile(index),
      child: Row(
        textBaseline: TextBaseline.ideographic,
        children: [
          _buildOrdinal(index),
          Expanded(
            child: _buildTextField(index),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdinal(int index) {
    return SizedBox(
        width: 30,
        child: FittedBox(
            fit: BoxFit.scaleDown,
            alignment: Alignment.centerLeft,
            child: Text('${index + 1}. ')));
  }

  Widget _buildTextField(int index) {
    return TextFormField(
      focusNode: focusNodes[index],
      initialValue: widget.notes[index],
      maxLines: widget.maxLines,
      minLines: 1,
      inputFormatters: [LengthLimitingTextInputFormatter(widget.maxLength)],
      onChanged: (text) {
        widget.notes[index] = text;
      },
      onFieldSubmitted: (text) {
        if (text.isEmpty) {
          _deleteTile(index);
        }
      },
    );
  }

  void _deleteTile(int index) {
    setState(
      () {
        widget.notes.removeAt(index);
        focusNodes[index].dispose();
        focusNodes.removeAt(index);
      },
    );
  }

  @override
  void dispose() {
    for (final node in focusNodes) {
      node.dispose();
    }
    super.dispose();
  }
}
