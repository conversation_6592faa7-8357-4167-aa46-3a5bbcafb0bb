import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import 'content/sharable_content.dart';
import 'map_exporter.dart';

class SharingService {
  final BuildContext context;
  final SharableContent content;

  SharingService(
    this.context, {
    required this.content,
  });

  void export() async {
    SpinnerDialog.present(context);

    final localizations = AppLocalizations.of(context)!;

    final (geometry, imageBounds) = await content.fetchGeometryAndImageBounds();
    final selections = await content.selections;
    final percent = await content.percentageComplete;
    final percentageLabel = content.percentageLabel(localizations);

    final exporter = MapExporter(
      geometry: geometry,
      selections: selections,
      palette: DependencyInjector.settingsBloc.currentPalette,
      imageBounds: imageBounds,
      percentageComplete: percent,
      percentageLabel: percentageLabel,
      annotationSize: content.annotationSize,
      showCount: content.showCount,
    );

    final image = await exporter.export();

    SpinnerDialog.dismiss();
    if (image == null) {
      return;
    }

    final message = await _shareMessage(localizations);

    Rect? shareOrigin;
    if (await _isRunningOnAnIPad() && context.mounted) {
      final renderBox = context.findRenderObject() as RenderBox?;
      final position = renderBox?.localToGlobal(Offset.zero);
      final size = renderBox?.size ?? Size.zero;
      if (position != null) {
        shareOrigin = Rect.fromLTWH(
          position.dx,
          position.dy,
          size.width,
          size.height,
        );
      }
    }

    final file = XFile(image.path, mimeType: 'image/png');

    SharePlus.instance.share(ShareParams(
      previewThumbnail: file,
      files: [file],
      subject: content.shareSubject(localizations),
      sharePositionOrigin: shareOrigin,
      text: message,
    ));
  }

  Future<bool> _isRunningOnAnIPad() async {
    if (Platform.isIOS == false) {
      return false;
    }

    try {
      final deviceInfo = await DeviceInfoPlugin().iosInfo;
      return deviceInfo.model.toLowerCase().contains('ipad');
    } catch (e) {
      return false;
    }
  }

  Future<String> _shareMessage(
    AppLocalizations localizations,
  ) async {
    final buffer = StringBuffer();
    await content.buildShareMessage(buffer, localizations);

    buffer.writeln(localizations.sharingHashtag);
    buffer.writeln(localizations.visitedWebsiteShortLink);

    return buffer.toString();
  }
}
