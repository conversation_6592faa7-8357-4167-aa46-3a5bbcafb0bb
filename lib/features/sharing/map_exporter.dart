import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/painting.dart';
import 'package:flutter/services.dart';

import '../../generic_widgets/selectable_item.dart';
import '../../helpers/data_extensions.dart';
import '../../models/geo_area.dart';
import '../../models/geo_bounds.dart';
import '../../models/palette.dart';
import '../../models/selection.dart';
import '../dashboard/graphs/percent_graph.dart';
import '../map/coordinate_transformer.dart';
import '../map/geometry_painter.dart';
import '../map/projection.dart';
import '../map/tiles/internal/geometry_repository.dart';

class MapExporter {
  MapExporter({
    required this.palette,
    required this.geometry,
    required this.selections,
    required this.percentageComplete,
    required this.percentageLabel,
    this.imageWidth = 2000,
    this.imageBounds = GeoBounds.wholePlanet,
    this.annotationSize = 4.0,
    this.showCount = false,
  });

  final Palette palette;
  final PolygonLookup geometry;
  final Map<SelectableItem, Selection> selections;
  final double imageWidth;
  final GeoBounds imageBounds;
  final double percentageComplete;
  final String percentageLabel;
  final double annotationSize;
  final bool showCount;

  static const margin = 32.0;

  Future<File?> export() async {
    final size = Size(imageWidth, imageWidth / imageBounds.calculateRatio());
    final recorder = PictureRecorder();
    final canvas = Canvas(
        recorder,
        Rect.fromLTWH(
          0,
          0,
          size.width,
          size.height,
        ));

    _drawGeometry(canvas, size);

    if (selections.keys.first is MapDisplayable) {
      final projection = EquirectangularProjection(size.width);
      final transformer = CoordinateTransformer(
        bounds: imageBounds,
        size: size,
        projection: projection,
      );

      _drawAnnotations(transformer, canvas);
    }

    await _drawWatermark(canvas, size);
    _drawPercentage(canvas, size);
    return _writeImage(recorder, size);
  }

  void _drawAnnotations(CoordinateTransformer transformer, Canvas canvas) {
    for (final entry in selections.entries) {
      final place = entry.key as MapDisplayable;
      final selection = entry.value;

      final point = transformer.normalize(place.coordinate);
      final color = palette.colorForSelection(selection);

      canvas.drawCircle(
        Offset(point.x, point.y),
        annotationSize,
        Paint()..color = color,
      );
    }
  }

  Future<void> _drawWatermark(Canvas canvas, Size size) async {
    final logoData = await rootBundle.load('assets/images/visited_logo.png');
    final logo = await decodeImageFromList(Uint8List.view(logoData.buffer));

    final offset = Offset(margin, size.height - logo.height - margin);
    canvas.drawImage(logo, offset, Paint());

    const fill = TextStyle(
      color: Color(0xFFFFFFFF),
      fontWeight: FontWeight.w700,
      fontSize: 48.0,
    );

    final outline = fill.copyWith(
      foreground: Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 4.0
        ..color = const Color(0xFF000000),
    );

    _drawAppName(logo, margin, outline, canvas, size);
    _drawAppName(logo, margin, fill, canvas, size);
    _drawWhiteWatermark(size, logo, margin, canvas);
  }

  void _drawWhiteWatermark(
    Size size,
    Image logo,
    double margin,
    Canvas canvas,
  ) {
    const scale = 3.0;

    final offset = Offset((size.width - (logo.width * scale)) / 2,
        (size.height - (logo.height * 2)) / 2);
    paintImage(
      canvas: canvas,
      rect: Rect.fromLTWH(
        offset.dx,
        offset.dy,
        logo.width * scale,
        logo.height * scale,
      ),
      image: logo,
      scale: 0.1,
      opacity: 0.05,
    );
    // canvas.drawImage(logo, offset, Paint());
  }

  void _drawAppName(
    Image logo,
    double margin,
    TextStyle style,
    Canvas canvas,
    Size size,
  ) {
    final span = TextSpan(
      text: 'Visited',
      style: style,
    );

    final painter = TextPainter(
      text: span,
      textAlign: TextAlign.left,
      textDirection: TextDirection.ltr,
    );

    painter.layout();
    painter.paint(
      canvas,
      Offset(
        (margin * 1.5) + logo.height,
        size.height - (margin * 1.5) - painter.height,
      ),
    );
  }

  void _drawGeometry(Canvas canvas, Size size) {
    final isViewingAreas = selections.keys.first is GeoArea;

    final painter = GeometryPainter(
      geometry: geometry,
      selectionGetter: (area) => isViewingAreas
          ? selections[area] ?? Selection.clear
          : Selection.clear,
      palette: palette,
      bounds: imageBounds,
      borderWidth: 4,
      projection: EquirectangularProjection(size.width),
    );

    painter.paint(canvas, size);
  }

  Future<File?> _writeImage(PictureRecorder recorder, Size size) async {
    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.round(),
      size.height.round(),
    );

    return image.saveTemporaryPng(
        filename: 'export-${DateTime.now().millisecondsSinceEpoch}.png');
  }

  void _drawPercentage(Canvas canvas, Size size) {
    final percentSize = Size.square(size.height / 8);

    final subtitlePainter = TextPainter(
      text: TextSpan(
        text: percentageLabel,
        style: TextStyle(
          color: palette.label,
          fontWeight: FontWeight.w700,
          fontSize: 24.0,
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    subtitlePainter.layout(maxWidth: percentSize.width + margin * 2);

    subtitlePainter.paint(
      canvas,
      Offset(
        size.width -
            percentSize.width -
            margin +
            ((percentSize.width - subtitlePainter.width) / 2),
        size.height - subtitlePainter.height - margin,
      ),
    );

    final percentBackground = PercentagePainter(
      percentage: 1.0,
      color: palette.label.withValues(alpha: 0.25),
      percentAnimated: 1.0,
      lineWidth: 4,
    );

    final percentPainter = PercentagePainter(
      percentage: showCount ? 1.0 : percentageComplete,
      color: palette.label,
      percentAnimated: 1.0,
      lineWidth: 10,
    );

    canvas.save();

    canvas.translate(
      size.width - percentSize.height - margin,
      size.height - percentSize.height - margin * 2 - subtitlePainter.height,
    );

    percentBackground.paint(canvas, percentSize);
    percentPainter.paint(canvas, percentSize);

    final span = TextSpan(
      text: showCount
          ? percentageComplete.toInt().toString()
          : '${(percentageComplete * 100).round()}%',
      style: TextStyle(
        color: palette.label,
        fontWeight: FontWeight.w700,
        fontSize: 40.0,
      ),
    );

    final painter = TextPainter(
      text: span,
      textAlign: TextAlign.left,
      textDirection: TextDirection.ltr,
    );

    painter.layout();

    painter.paint(
      canvas,
      Offset(
        (percentSize.height - painter.width) / 2,
        (percentSize.height - painter.height) / 2,
      ),
    );

    canvas.restore();
  }
}
