import 'dart:io';

import 'package:flutter/material.dart';

import '../../generic_widgets/device_aware_bottom_padding.dart';
import '../../generic_widgets/helper_tooltip.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/slide_and_fade_in.dart';
import '../../generic_widgets/sliver_sticky_bar.dart';
import '../../l10n/generated/app_localizations.dart';
import '../authentication/login_background.dart';

class FirstTimeScaffold extends StatefulWidget {
  const FirstTimeScaffold({
    super.key,
    required this.searchController,
    required this.title,
    required this.tooltip,
    required this.showContinue,
    required this.onContinue,
    required this.slivers,
    this.searchHint,
    this.backgroundImageIndex = 1,
    this.hideBackButton = false,
    this.footer,
  });

  final TextEditingController searchController;
  final String title;
  final String tooltip;
  final bool showContinue;
  final VoidCallback onContinue;
  final List<Widget> slivers;
  final String? searchHint;
  final int backgroundImageIndex;
  final bool hideBackButton;
  final Widget? footer;

  @override
  State<FirstTimeScaffold> createState() => _FirstTimeScaffoldState();

  static const tileBackgroundColour = Color(0xFFEBEBF1);
}

class _FirstTimeScaffoldState extends State<FirstTimeScaffold> {
  final scrollController = ScrollController();
  Color headerTextColor = Colors.white;

  @override
  void initState() {
    super.initState();

    if (Platform.isIOS) {
      scrollController.addListener(_onScroll);
    }
  }

  void _onScroll() {
    if (headerTextColor == Colors.white && scrollController.offset > 200) {
      setState(() {
        headerTextColor = Colors.black;
      });
    }

    if (headerTextColor == Colors.black && scrollController.offset < 200) {
      setState(() {
        headerTextColor = Colors.white;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      body: Stack(
        children: [
          const LoginBackground(),
          Positioned.fill(
            child: Column(
              children: [
                Expanded(
                  child: Scrollbar(
                    controller: scrollController,
                    child: _buildScrollView(localizations, context),
                  ),
                ),
                if (widget.footer != null) widget.footer!,
                if (widget.showContinue)
                  _buildContinueButton(context, localizations),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton(
    BuildContext context,
    AppLocalizations localizations,
  ) {
    return DeviceAwareBottomPadding(
      child: ResponsivePadding(
        context: context,
        fillToEdgeOnPhone: false,
        child: SlideAndFadeIn(
          offset: const Offset(0, 0.7),
          duration: const Duration(milliseconds: 250),
          child: PlatformFilledButton(
            title: localizations.continueText,
            onTapped: widget.onContinue,
          ),
        ),
      ),
    );
  }

  Widget _buildScrollView(
    AppLocalizations localizations,
    BuildContext context,
  ) {
    return CustomScrollView(
      controller: scrollController,
      slivers: [
        _buildAppBar(context),
        SliverToBoxAdapter(
          child: SizedBox(
            height: Theme.of(context).platform == TargetPlatform.android
                ? 16.0
                : 6.0,
          ),
        ),
        _buildSearchBar(context),
        const SliverToBoxAdapter(child: SizedBox(height: 16)),
        ...widget.slivers,
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return PlatformSliverAppBar(
      title: widget.title,
      backgroundColour: Theme.of(context).platform == TargetPlatform.iOS
          ? Colors.transparent
          : const Color(0xFF092a68),
      titleColour: headerTextColor,
      action: _buildHelperTip(context),
      elevation: 0,
      leading: widget.hideBackButton ? const SizedBox() : null,
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return SliverStickyBar(
      color: Colors.transparent,
      child: ResponsivePadding(
        context: context,
        fillToEdgeOnPhone: false,
        child: TextField(
          controller: widget.searchController,
          autocorrect: false,
          maxLines: 1,
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
          decoration: InputDecoration(
            fillColor: FirstTimeScaffold.tileBackgroundColour,
            filled: true,
            hintText:
                widget.searchHint ??
                MaterialLocalizations.of(context).searchFieldLabel,
            hintStyle: const TextStyle(
              fontWeight: FontWeight.w700,
              color: Colors.black87,
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.black87),
            ),
            suffix: (widget.searchController.text.isNotEmpty)
                ? _buildClearSearchButton()
                : null,
          ),
        ),
      ),
    );
  }

  Widget _buildClearSearchButton() {
    return GestureDetector(
      onTap: widget.searchController.clear,
      behavior: HitTestBehavior.opaque,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.black54,
          shape: BoxShape.circle,
        ),
        padding: const EdgeInsets.all(4),
        child: const Icon(Icons.clear_rounded, color: Colors.white, size: 15),
      ),
    );
  }

  Widget _buildHelperTip(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: HelperTooltip(message: widget.tooltip, color: headerTextColor),
    );
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }
}
