import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_marker_cluster/flutter_map_marker_cluster.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/stroked_text.dart';
import '../../../models/color_extensions.dart';
import '../../../models/geo_area.dart';
import '../../../models/label.dart';
import '../../../models/selection.dart';
import '../map_screen.dart';
import 'internal/coordinate_extension.dart';
import 'internal/map_zoom_to_tile_zoom_compenstator.dart';
import 'internal/tile_repository.dart';

class MapLabelLayer extends StatefulWidget {
  const MapLabelLayer({
    required this.eventNotifier,
    required this.mode,
    super.key,
  });

  final MapMode mode;
  final ValueNotifier<MapEvent?> eventNotifier;

  @override
  State<MapLabelLayer> createState() => _MapLabelLayerState();
}

class _MapLabelLayerState extends State<MapLabelLayer>
    with MapZoomToTileZoomCompensator {
  StreamSubscription? _regionListener;
  StreamSubscription? _paletteListener;

  late var _palette = DependencyInjector.settingsBloc.currentPalette;
  late var areas = <GeoArea>[];
  int lastZoomLevel = 0;

  @override
  void initState() {
    super.initState();
    _updateLabels();
    widget.eventNotifier.addListener(_updateLabels);
    _regionListener ??= DependencyInjector.settingsBloc.showRegions.listen((_) {
      lastZoomLevel = -1; // Forces the check for zoom changing to always pass
      _updateLabels();
    });

    _paletteListener ??=
        DependencyInjector.settingsBloc.palette.listen((palette) {
      setState(() {
        _palette = palette;
      });
    });
  }

  @override
  void dispose() {
    widget.eventNotifier.removeListener(_updateLabels);
    _regionListener?.cancel();
    _paletteListener?.cancel();
    super.dispose();
  }

  void _updateLabels() async {
    final camera = widget.eventNotifier.value?.camera;
    if (camera == null) {
      return;
    }

    final zoom = clampToTileZoom(camera);
    if (zoom == lastZoomLevel) {
      return;
    }
    final areas = await _fetchAreaThatShouldShowLabels(zoom);

    if (mounted) {
      setState(() {
        lastZoomLevel = zoom;
        this.areas = areas;
      });
    }
  }

  Future<List<GeoArea>> _fetchAreaThatShouldShowLabels(int zoom) async {
    final areaBloc = DependencyInjector.areaBloc;
    final areas = await areaBloc.allCountries();

    final subdivisionLevel = TileRepository.minSubdivisionLevel(zoom);
    if (widget.mode == MapMode.countries &&
        DependencyInjector.settingsBloc.currentSettings.showRegions &&
        subdivisionLevel > 0 &&
        mounted) {
      final subdivisionsToShow = <GeoArea>{};
      final areasToRemove = <GeoArea>{};
      await _addSubdivisionsBasedOnZoomLevel(
        areas: areas,
        areasToRemove: areasToRemove,
        subdivisionsToShow: subdivisionsToShow,
      );

      if (subdivisionLevel > 1) {
        final firstLevelSubdivisionsToRemove = <GeoArea>{};
        final secondLevelSubdivisions = <GeoArea>{};
        await _addSubdivisionsBasedOnZoomLevel(
          areas: subdivisionsToShow,
          areasToRemove: firstLevelSubdivisionsToRemove,
          subdivisionsToShow: secondLevelSubdivisions,
        );

        subdivisionsToShow.removeWhere(
            (area) => firstLevelSubdivisionsToRemove.contains(area));
        subdivisionsToShow.addAll(secondLevelSubdivisions);
      }

      areas.removeWhere((area) => areasToRemove.contains(area));
      areas.addAll(subdivisionsToShow);
    }
    return areas;
  }

  Future<void> _addSubdivisionsBasedOnZoomLevel({
    required Iterable<GeoArea> areas,
    required Set<GeoArea> areasToRemove,
    required Set<GeoArea> subdivisionsToShow,
  }) async {
    final iapBloc = DependencyInjector.iapBloc;
    final areaBloc = DependencyInjector.areaBloc;

    for (final area in areas) {
      if (iapBloc.canAccessSubdivisions(area)) {
        final subdivisions = await areaBloc.fetchSubdivisions(area);
        areasToRemove.add(area);
        subdivisionsToShow.addAll(subdivisions);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MediaQuery.withNoTextScaling(
      child: IgnorePointer(
        child: MarkerClusterLayerWidget(
          options: MarkerClusterLayerOptions(
            maxClusterRadius: 80,
            animationsOptions: _buildDisabledAnimations(),
            alignment: Alignment.center,
            padding: const EdgeInsets.all(50),
            centerMarkerOnClick: false,
            maxZoom: 15,
            markers: [
              for (final area in areas)
                if (area.labels != null) ...[
                  for (final label in area.labels!)
                    if (label.resolution <= lastZoomLevel)
                      _buildLabel(label, area)
                ]
            ],
            builder: (BuildContext context, List<Marker> markers) {
              if (markers.length == 1) {
                return markers.first.child;
              }

              return const SizedBox();
            },
          ),
        ),
      ),
    );
  }

  AnimationsOptions _buildDisabledAnimations() {
    return const AnimationsOptions(
      zoom: Duration(milliseconds: 200),
      fitBound: Duration.zero,
      centerMarker: Duration.zero,
      spiderfy: Duration.zero,
      clusterExpandCurve: SawTooth(0),
      clusterCollapseCurve: SawTooth(0),
    );
  }

  Marker _buildLabel(Label label, GeoArea area) {
    final selection = widget.mode == MapMode.cities
        ? Selection.clear
        : DependencyInjector.areaSelectionRepository.selectionSync(area) ??
            Selection.clear;

    const darkLabel = Color(0xFF111111);
    const lightLabel = Color(0xFFFFFFFF);

    final labelColour =
        _palette.colorForSelection(selection).legibleForegroundColor(
              lightColor: lightLabel,
              darkColor: darkLabel,
            );

    final strokeColour = labelColour == lightLabel ? darkLabel : lightLabel;

    const fontSize = 14.0;

    final style = TextStyle(
        fontSize: fontSize,
        fontFamily: 'Arnimo',
        fontWeight: FontWeight.bold,
        color: labelColour,
        shadows: [
          Shadow(
              color: strokeColour.withValues(alpha: 0.25),
              offset: const Offset(0, 2),
              blurRadius: 4)
        ]);

    const maxWidth = 150.0;
    const maxLines = 3;

    final painter = TextPainter(
      text: TextSpan(text: area.name, style: style),
      maxLines: maxLines,
      textDirection: TextDirection.ltr,
      textWidthBasis: TextWidthBasis.longestLine,
    )..layout(maxWidth: maxWidth);

    final size = painter.size;

    return Marker(
      point: label.coordinate.toLatLng(),
      width: size.width * 2,
      height: size.height * 2,
      child: Center(
        child: StrokedText(
          area.name,
          style: style,
          strokeWidth: 1.5,
          textAlign: TextAlign.center,
          strokeColour: strokeColour,
          maxLines: maxLines,
        ),
      ),
    );
  }
}
