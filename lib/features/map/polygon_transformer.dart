import 'dart:ui';

import 'package:flutter/material.dart';
import 'coordinate_transformer.dart';
import 'projection.dart';
import '../../models/geo_bounds.dart';
import '../../models/polygon.dart';

class PolygonTransformer {
  final Polygon polygon;
  final Size size;
  final GeoBounds bounds;
  final Projection projection;

  const PolygonTransformer({
    required this.polygon,
    required this.size,
    this.bounds = GeoBounds.wholePlanet,
    required this.projection,
  });

  Path normalize() {
    final transformer = CoordinateTransformer(
      bounds: bounds,
      size: size,
      projection: projection,
    );

    final path = Path();
    final first = transformer.normalize(polygon.coordinates.first);
    path.moveTo(first.x, first.y);
    for (var i = 1; i < polygon.coordinates.length; i++) {
      final coordinate = polygon.coordinates[i];
      final point = transformer.normalize(coordinate);
      path.lineTo(point.x, point.y);
    }

    path.close();
    return path;
  }

  List<Offset> normalizeRaw() {
    final transformer = CoordinateTransformer(
      bounds: bounds,
      size: size,
      projection: projection,
    );

    return polygon.coordinates.map((e) {
      final point = transformer.normalize(e);
      return Offset(point.x, point.y);
    }).toList();
  }
}
