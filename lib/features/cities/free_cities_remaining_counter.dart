import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../in_app_purchase/iap_product.dart';
import 'paywall_dialog.dart';

class FreeCitiesRemainingCounter extends StatelessWidget {
  const FreeCitiesRemainingCounter({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<int?>(
        stream: DependencyInjector.cityBloc.freeSelectionsRemaining(),
        builder: (context, snapshot) {
          final remaining = snapshot.data;
          if (remaining == null) {
            return const SizedBox();
          }

          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              PaywallDialog(feature: IAPFeature.unlockCities).show(
                context,
                barrierDismissible: true,
              );
            },
            child: Container(
              width: 50,
              height: 30,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Theme.of(context).colorScheme.primary.withAlpha(128),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary,
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                    color: Theme.of(context).colorScheme.primary.withAlpha(64),
                  )
                ],
              ),
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    remaining.toString(),
                    style: Theme.of(context)
                        .textTheme
                        .headlineMedium
                        ?.copyWith(color: Colors.white),
                  ),
                ),
              ),
            ),
          );
        });
  }
}
