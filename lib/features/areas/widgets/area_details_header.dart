import 'package:flutter/material.dart';

class AreaDetailsHeader extends StatelessWidget {
  const AreaDetailsHeader({
    super.key,
    required this.text,
    this.trailing,
    this.textStyle,
  });

  final String text;
  final Widget? trailing;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    final trailing = this.trailing;
    return Padding(
      padding: const EdgeInsets.only(top: 32),
      child: trailing == null
          ? _buildTitle(context)
          : Row(
              children: [
                Expanded(child: _buildTitle(context)),
                trailing,
              ],
            ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      text,
      style: textStyle ?? Theme.of(context).textTheme.titleLarge,
    );
  }
}
