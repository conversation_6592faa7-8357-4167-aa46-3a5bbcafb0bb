import 'dart:convert';

import 'package:collection/collection.dart';

import '../../caching/database.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../logger.dart';
import '../../models/geo_area.dart';
import '../../networking/asset_helper.dart';

class AreaRepository {
  final VisitedDatabase _database;
  String get languageCode =>
      DependencyInjector.settingsBloc.currentLanguage.localizationKey;
  late final _disputedTerritoriesServices =
      DependencyInjector.disputedTerritoriesService;

  final _inMemoryCache = <String, GeoArea>{};

  AreaRepository(this._database);

  // TODO: enforce that this is only initialized once
  Future<void> initialize() async {
    DependencyInjector.settingsBloc.language.listen((_) async {
      try {
        _inMemoryCache.clear();
        await _fillAreaCache();
        DependencyInjector.tileRenderingService.refresh();
      } catch (e) {
        log('Error updating areas: $e');
      }
    });

    await _importInitialData();
    await _fillAreaCache();
  }

  Future<void> _fillAreaCache() async {
    final dtos = await _database.fetchAllAreas();
    dtos.forEach(geoAreaFromDto);
  }

  Future<void> _importInitialData() async {
    if (await hasImportedData()) {
      return;
    }

    final json =
        await AssetHelper.loadCompressedJson('assets/data/geo_areas.bin');
    await cacheChanges(json);
  }

  GeoArea? fetchByIsoCodeSync(String isoCode) => _inMemoryCache[isoCode];

  Future<GeoArea?> fetchByIsoCode(String isoCode) async {
    final cached = _inMemoryCache[isoCode];
    if (cached != null) {
      return cached;
    }

    final dto = await _database.fetchAreaByIsoCode(isoCode);
    if (dto == null) {
      return null;
    }

    return geoAreaFromDto(dto);
  }

  Future<GeoArea?> fetchById(int id) async {
    final cached =
        _inMemoryCache.values.firstWhereOrNull((area) => area.id == id);
    if (cached != null) {
      return cached;
    }

    final dto = await _database.fetchAreaById(id);
    if (dto == null) {
      return null;
    }

    return geoAreaFromDto(dto);
  }

  Future<List<GeoArea>> fetchByIsoCodes(List<String> isoCodes) async {
    final dtos = await _database.fetchAllAreas();
    return dtos
        .where((dto) => isoCodes.contains(dto.isoCode))
        .map(geoAreaFromDto)
        .toList();
  }

  Future<List<GeoArea>> fetchTopLevel() async {
    if (_inMemoryCache.isNotEmpty) {
      return _inMemoryCache.values
          .where((area) => area.parentId == null)
          .toList();
    }

    final dtos = await _database.fetchTopLevelAreas();
    return dtos.map<GeoArea>(geoAreaFromDto).toList();
  }

  Future<Set<GeoArea>> fetchAll() async {
    if (_inMemoryCache.isNotEmpty) {
      return _inMemoryCache.values.toSet();
    }

    final dtos = await _database.fetchAllAreas();
    return dtos.map<GeoArea>(geoAreaFromDto).toSet();
  }

  Set<GeoArea> fetchAllSync() {
    return _inMemoryCache.values.toSet();
  }

  Future<Set<GeoArea>> fetchSubdivisions(GeoArea area) async {
    if (area.hasSubdivisions == false) {
      return {};
    }

    var subdivisions = _inMemoryCache.values
        .where((child) => child.parentId == area.id)
        .toSet();

    if (subdivisions.isEmpty) {
      subdivisions = await _pullSubdivisionsFromDatabase(area);
    }

    await _applyDisputedTerritoryAdjustments(area, subdivisions);
    return subdivisions;
  }

  Future<Set<GeoArea>> _pullSubdivisionsFromDatabase(GeoArea area) async {
    log('_pullSubdivisionsFromDatabase: $area');
    final dtos = await _database.fetchSubdivisions(area);
    final subdivisions = dtos.map<GeoArea>(geoAreaFromDto).toSet();
    return subdivisions;
  }

  Future<void> _applyDisputedTerritoryAdjustments(
    GeoArea area,
    Set<GeoArea> subdivisions,
  ) async {
    final adjustments =
        await _disputedTerritoriesServices.subdivisionAdjustments(area);

    subdivisions.removeWhere((area) => adjustments.remove
        .where((element) => element.isoCode == area.isoCode)
        .isNotEmpty);

    for (final simpleArea in adjustments.add) {
      final dto = await _database.fetchAreaById(simpleArea.id);
      if (dto != null) {
        final subdivision = geoAreaFromDto(dto);
        subdivisions.add(subdivision);
      }
    }
  }

  Future<GeoArea?> fetchParent(GeoArea area) async {
    final dto = await _database.fetchParent(area);
    if (dto == null) {
      return null;
    }

    return geoAreaFromDto(dto);
  }

  GeoArea geoAreaFromDto(AreaDto dto) {
    var area = _inMemoryCache[dto.isoCode];
    if (area != null) {
      return area;
    }

    // TODO: run this on an isolate to improve startup responsiveness while
    final code = languageCode;
    area = GeoArea.fromJson(
      jsonDecode(dto.json),
      parentId: dto.parentId,
      languageCode: code,
    );
    _inMemoryCache[dto.isoCode] = area;
    return area;
  }

  Future<void> cacheChanges(Map json) {
    final lastModified = DateTime.parse(json['lastModifiedDate']);
    final areas = json['areas']
        ?.map<AreaDto>(
          (areaJson) => AreaDto(
            id: areaJson['id'],
            parentId: areaJson['parentId'],
            isoCode: areaJson['iso'],
            continentId: areaJson['continentId'],
            json: jsonEncode(areaJson),
          ),
        )
        ?.toSet();

    final continents = json['regions']
        ?.map<ContinentDto>(
          (continentJson) => ContinentDto(
            id: continentJson['id'],
            name: continentJson['name'],
            translations: jsonEncode(continentJson['translations']),
          ),
        )
        ?.toSet();

    return _database.updateData(
      lastModified,
      areaDtos: areas,
      continentDtos: continents,
    );
  }

  Future<bool> hasImportedData() async {
    final lastModified = await fetchLastModifiedDate();
    return lastModified != null;
  }

  Future<DateTime?> fetchLastModifiedDate() {
    return _database.fetchLastTimeAreasWereModified();
  }
}
