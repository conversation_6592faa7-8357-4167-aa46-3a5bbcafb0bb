import '../../models/geo_area.dart';
import 'models/experience.dart';
import 'models/experience_selections.dart';

// Consider adding to the database for caching and offline mode?
// For now in memory is fine...
class ExperienceRepository {
  Set<Experience>? experiences;
  Set<ExperienceSelections>? selections;
  final areas = <Experience, List<GeoArea>>{};
  final experiencesByArea = <GeoArea, List<Experience>>{};

  void clear() {
    experiences = null;
    selections = null;
    areas.clear();
    experiencesByArea.clear();
  }

  void clearSelections() {
    selections = null;
  }
}
