import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/palette.dart';
import '../../models/selection.dart';
import '../settings/sliver_custom_palette_picker.dart';
import '../settings/sliver_standard_palette_picker.dart';
import '../settings/sliver_subheading_tile.dart';
import '../todo_lists/todo_list_details_screen.dart';
import 'components/map_poster_preview.dart';
import 'components/poster_bottom_button.dart';
import 'components/poster_custom_area_picker.dart';
import 'components/poster_custom_city_picker.dart';
import 'components/poster_type_tile.dart';
import 'enter_shipping_address_screen.dart';
import 'models/currency.dart';
import 'models/poster.dart';
import 'models/poster_type.dart';
import 'models/price.dart';

class PosterCustomizeScreen extends StatelessWidget {
  const PosterCustomizeScreen({
    super.key,
    required this.poster,
    this.useCustomSelections = false,
  });

  final Poster poster;
  final bool useCustomSelections;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final largeScreen = size.width > 640;
    return Scaffold(
      appBar: PlatformAppBar(
        title: AppLocalizations.of(context)!.posterCustomizeTitle,
      ),
      body: largeScreen
          ? _buildTwoColumnLayout(context)
          : _singleColumnLayout(context),
    );
  }

  Widget _singleColumnLayout(BuildContext context) {
    return Column(
            children: [
              SizedBox(
                  height: 250,
                  child: AspectRatio(
                      aspectRatio: MapPosterPreview.posterSize.width /
                          MapPosterPreview.posterSize.height,
                      child: const MapPosterPreview())),
              _buildConfigurationControls(context),
              _buildEnterShippingButton(context),
            ],
          );
  }

  Widget _buildTwoColumnLayout(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              const SizedBox(width: 32),
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    _buildConfigurationControls(
                      context,
                      includePrice: false,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const MapPosterPreview(
                        includeResponsivePadding: false,
                      ),
                      const SizedBox(height: 32),
                      if (useCustomSelections) _buildAddSelections(context),
                      _buildPricePicker(),
                    ],
                  )),
              const SizedBox(width: 16),
            ],
          ),
        ),
        _buildEnterShippingButton(context),
      ],
    );
  }

  Widget _buildAddSelections(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: PlatformFilledButton(
        title: AppLocalizations.of(context)!.addSelections,
        color: Colors.orangeAccent,
        textColor: Colors.black,
        onTapped: () {
          final poster =
              DependencyInjector.posterPrintingBloc.currentPosterType;

          String routeName;
          WidgetBuilder builder;

          switch (poster) {
            case CountryPosterType():
              routeName = 'poster_custom_area_selections';
              builder = (_) => PosterCustomAreaPicker(poster: poster);
              break;

            case CityPosterType():
              routeName = 'poster_custom_city_selections';
              builder = (_) => PosterCustomCityPicker(poster: poster);
              break;

            case TodoListPosterType(list: final list):
              routeName = 'poster_custom_todo_list_selections_${list?.id}';
              builder = (_) => TodoListDetailsScreen(
                    list: list!,
                    showMap: false,
                    showSharing: false,
                    showStats: false,
                    selections: poster.availableSelections,
                    onSelectedOverride: poster.selectForCustomPoster,
                    selectionStreamOverride: (item) {
                      return poster.selections.map((selections) {
                        return selections[item] ?? Selection.clear;
                      });
                    },
                  );
              break;

            case ExperiencePosterType(experience: final experience):
              routeName =
                  'poster_custom_experience_selections_${experience?.id}';
              builder = (_) => PosterCustomAreaPicker(poster: poster);
          }

          Navigator.of(context).pushMaterialRoute(
            name: routeName,
            builder: builder,
          );
        },
      ),
    );
  }

  Widget _buildEnterShippingButton(BuildContext context) {
    return PosterBottomButton(
      title: AppLocalizations.of(context)!.continueText,
      onTapped: () => Navigator.of(context).pushMaterialRoute(
          name: 'poster_enter_shipping_address',
          builder: (_) => const EnterShippingAddressScreen()),
    );
  }

  Widget _buildConfigurationControls(
    BuildContext context, {
    bool includePrice = false,
  }) {
    final bloc = DependencyInjector.posterPrintingBloc;
    return Expanded(
      child: Scrollbar(
        child: CustomScrollView(
          slivers: [
            if (includePrice)
              SliverToBoxAdapter(
                child: ResponsivePadding(
                  context: context,
                  fillToEdgeOnPhone: false,
                  child: _buildPricePicker(),
                ),
              ),
            _buildMapTypePicker(),
            if (useCustomSelections)
              SliverToBoxAdapter(child: _buildAddSelections(context)),
            _buildSelectionPicker(context),
            SliverStandardPalettePicker(
              activePalette: bloc.palette,
              // TODO: should we clear the tile cache here?
              onPaletteSelected: bloc.updatePalette,
            ),
            SliverCustomPalettePicker(
              palette: bloc.customPalette,
              // TODO: should we clear the tile cache here?
              onPaletteChanged: bloc.updateCustomPalette,
              showLabels: false,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricePicker() {
    final bloc = DependencyInjector.posterPrintingBloc;
    return StreamBuilder<Price>(
        stream: bloc.price,
        builder: (context, snapshot) {
          final price = snapshot.data;
          if (price == null) {
            return const SizedBox();
          }

          final items = bloc.availablePrices.map((e) {
            return DropdownMenuItem<Price>(
                value: e, child: _buildPriceTile(context, e));
          }).toList(growable: false);

          return Row(
            children: [
              Expanded(
                  child: Text(
                AppLocalizations.of(context)!.price,
                style: SliverSubHeadingTile.style(context),
              )),
              if (items.length == 1)
                _buildPriceTile(context, price)
              else
                DropdownButton<Price>(
                  value: price,
                  items: items,
                  onChanged: bloc.selectPrice,
                ),
            ],
          );
        });
  }

  Widget _buildPriceTile(BuildContext context, Price price) {
    final formattedPrice = price.tax != null
        ? AppLocalizations.of(context)!.formattedPlusTax(price.formatted)
        : price.formatted;

    return Row(
      children: [
        Image.asset(
          price.currency.asset,
          width: 30,
        ),
        const SizedBox(width: 20),
        Text(
          formattedPrice,
          style: Theme.of(context).textTheme.titleLarge,
        ),
      ],
    );
  }

  Widget _buildMapTypePicker() {
    return StreamBuilder<PosterType>(
      stream: DependencyInjector.posterPrintingBloc.posterType,
      builder: (context, snapshot) {
        final selected = snapshot.data ??
            (useCustomSelections
                ? CountryPosterType.custom()
                : CountryPosterType());

        final types = <PosterType>[
          CountryPosterType(),
          CityPosterType(),
          if (!useCustomSelections) ...[
            TodoListPosterType(),
            ExperiencePosterType()
          ]
        ];

        return SliverSubHeadingTile(
          title: AppLocalizations.of(context)!.posterType,
          sliver: SliverList.list(
            children: [
              for (final type in types)
                PosterTypeTile(
                  type: type,
                  selected: selected,
                  useCustom: useCustomSelections,
                )
            ],
          ),
        );
      },
    );
  }

  Widget _buildSelectionPicker(BuildContext context) {
    return StreamBuilder<(Palette, List<Selection>)>(
      stream: _mergedPaletteAndAvailableSelections,
      builder: (context, snapshot) {
        final record = snapshot.data;
        if (record == null) {
          return const SliverToBoxAdapter(child: SizedBox());
        }
        final (palette, selections) = record;

        return SliverSubHeadingTile(
          title: AppLocalizations.of(context)!.showSelections,
          sliver: SliverList.list(
            children: [
              for (final selection in selections)
                _buildSelectionSwitch(palette, selection, context),
            ],
          ),
        );
      },
    );
  }

  Stream<(Palette, List<Selection>)> get _mergedPaletteAndAvailableSelections {
    final bloc = DependencyInjector.posterPrintingBloc;
    return Rx.combineLatest2(
      bloc.palette,
      bloc.posterType,
      (palette, type) => (palette, type.availableSelections),
    );
  }

  Widget _buildSelectionSwitch(
    Palette palette,
    Selection selection,
    BuildContext context,
  ) {
    final bloc = DependencyInjector.posterPrintingBloc;
    return StreamBuilder<List<Selection>>(
      stream: bloc.printableSelections,
      builder: (context, snapshot) {
        final selections = snapshot.data ?? bloc.currentPrintableSelections;
        return SeparatedTile(
          child: SwitchListTile.adaptive(
            title: Text(selection.localized(AppLocalizations.of(context)!),
                style: Theme.of(context).textTheme.labelLarge),
            value: selections.contains(selection),
            activeColor: palette.colorForSelection(selection),
            onChanged: (_) => bloc.toggleSelection(selection),
          ),
        );
      },
    );
  }
}
