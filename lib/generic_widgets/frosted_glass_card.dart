import 'dart:ui';

import 'package:flutter/material.dart';

class FrostedGlassCard extends StatelessWidget {
  final Widget child;

  const FrostedGlassCard({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.0),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
        child: Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(96),
            borderRadius: BorderRadius.circular(16.0),
          ),
          child: child,
        ),
      ),
    );
  }
}
