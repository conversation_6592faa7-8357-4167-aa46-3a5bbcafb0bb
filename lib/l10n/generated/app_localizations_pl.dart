// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Polish (`pl`).
class AppLocalizationsPl extends AppLocalizations {
  AppLocalizationsPl([String locale = 'pl']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Język';

  @override
  String get pickEmailApp => 'Wybierz aplikację e-mail';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Odwiedziłem/am $amount krajów! Ile ty odwiedziłeś/aś? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Odwiedziłem/am $amount miast! Ile ty odwiedziłeś/aś? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Odwiedziłem/am $amount $listName! Ile ty odwiedziłeś/aś? www.visitedapp.com';
  }

  @override
  String get clear => 'Wyczyść';

  @override
  String get been => 'Odwiedzone';

  @override
  String get want => 'Chcę';

  @override
  String get live => 'Obecnie';

  @override
  String get lived => 'Mieszkałem/am';

  @override
  String get water => 'Woda';

  @override
  String get land => 'Ziemia';

  @override
  String get borders => 'Granice';

  @override
  String get labels => 'Etykiety';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inspiracja';

  @override
  String get inspirations => 'Inspiracje';

  @override
  String get delete => 'Usuń';

  @override
  String get unlockVisitedUpsellTitle => 'Chcesz zobaczyć więcej?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Odblokuj wszystkie funkcje i ciesz się Visited w pełnej krasie';

  @override
  String get checkTheDetails => 'Sprawdź detale';

  @override
  String get moreInspirationsComingSoon =>
      'Pracujemy nad zdobyciem większej ilości zdjęć. Sprawdź wkrótce!';

  @override
  String get unlockPremiumFeatures => 'Odblokuj funkcje premium';

  @override
  String get purchased => 'Zakupione!';

  @override
  String get buy => 'Kup';

  @override
  String get restorePurchases => 'Przywróć Zakup';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Na pewno?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Usunięcie tej karty jest trwałe. Nie będzie możliwości odzyskania tego obrazu.';

  @override
  String get cancel => 'Anuluj';

  @override
  String get map => 'Mapa';

  @override
  String get progress => 'Postęp';

  @override
  String get myTravelGoal => 'Mój Cel Podróży';

  @override
  String goalRemaining(int remaining) {
    return 'pozostało $remaining!';
  }

  @override
  String get top => 'SZCZYT';

  @override
  String get ofTheWorld => 'świata!';

  @override
  String get countries => 'kraje';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Kraje najchętniej odwiedzane z $country:';
  }

  @override
  String get login => 'Zaloguj';

  @override
  String get logout => 'Wyloguj';

  @override
  String get enterYourEmail => 'Wprowadź swój e-mail';

  @override
  String get privacyPolicy => 'Polityka Prywatności';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-polish/';

  @override
  String get termsOfUse => 'Warunki użytkowania';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-polish/';

  @override
  String get errorTitle => 'Ups!';

  @override
  String get enterValidEmail => 'Proszę wpisać prawidłowy adres e-mail';

  @override
  String get settings => 'Ustawienia';

  @override
  String get whereDoYouLive => 'Skąd jesteś?';

  @override
  String get whereHaveYouBeen => 'Gdzie byłaś/eś?';

  @override
  String get whereDoYouFlyFrom => 'Dokąd zmierzasz?';

  @override
  String get next => 'Dalej';

  @override
  String get missingAirports =>
      'Nie ma tu tego, czego szukasz? Wyślij do nas e-mail na adres';

  @override
  String get missingAirportsEmailTitle => 'Brak Lotnisk!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Witamy w Visited!';

  @override
  String get welcomeSubtitle => 'Czeka na Ciebie przygoda życia';

  @override
  String get getStarted => 'Zaczynamy';

  @override
  String get privacyAgreement => 'Umowa o ochronie prywatności';

  @override
  String get privacyAgreementSubtitle =>
      'Przed dalszym korzystaniem z serwisu Visited prosimy o wyrażenie zgody na poniższe punkty.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Zaznaczając to pole, potwierdzasz, że przeczytałaś i zgadzasz się przestrzegać Arriving in High Heels [Polityka prywatności](https://www.arrivinginhighheels.com/privacy-policy) oraz [Warunki użytkowania](https://www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Wyrażam zgodę na otrzymywanie od Arriving in High Heels wiadomości elektronicznych zawierających informacje i oferty dotyczące produktów, aplikacji i usług, które mogą być dla mnie interesujące, w tym powiadomienia o wyprzedażach, promocjach, ofertach i biuletynach. Zgodę tę mogę w każdej chwili wycofać w sposób opisany w Polityce Prywatności lub poprzez kliknięcie na link \"wypisz\" w wiadomościach elektronicznych.';

  @override
  String get submit => 'Wyślij';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Aby móc dalej korzystać z serwisu Visited, użytkownik musi wyrazić zgodę na oba nasze warunki.';

  @override
  String get deleteAccount => 'Usuń Konto';

  @override
  String get removeAdsUpsell =>
      'Chcesz zamiast zrezygnować z reklam i zrezygnować z subskrypcji email marketingu?';

  @override
  String get deleteAccountWarning =>
      'Usunięcie konta spowoduje usunięcie wszystkich Twoich informacji z naszych serwerów.\n Ten proces nie może być cofnięty.';

  @override
  String get about => 'O nas';

  @override
  String get popularity => 'Popularność';

  @override
  String get regions => 'Regiony';

  @override
  String get population => 'Populacja';

  @override
  String get size => 'Rozmiar';

  @override
  String get coverage => 'Pokrycie';

  @override
  String get percentOfCountryVisited => '% odwiedzonych krajów';

  @override
  String get visited => 'odwiedzono';

  @override
  String get notes => 'Notatki';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Modyfikuj';

  @override
  String get onlyCountSovereign => 'Tylko kraje suwerenne';

  @override
  String get countUkSeparately => 'Kraje U.K. liczone oddzielnie';

  @override
  String get showLegend => 'Pokaż legende';

  @override
  String get showLivedPin => 'Pokaż żywy pin';

  @override
  String get useMyColours => 'Użyj moich kolorów';

  @override
  String get mapColors => 'Kolor Mapy';

  @override
  String get traveller => 'Podróżnik';

  @override
  String get nightTraveller => 'Podróżnik nocny';

  @override
  String get original => 'Oryginał';

  @override
  String get explorer => 'Odkrywca';

  @override
  String get weekender => 'Weekendowiec';

  @override
  String get naturalist => 'Naturalista';

  @override
  String get historian => 'Historyk';

  @override
  String get thrillSeeker => 'Poszukiwacz wrażeń';

  @override
  String get culturalBuff => 'Miłośnik kultury';

  @override
  String get myColors => 'Moje kolory';

  @override
  String get experiences => 'Doświadczenia';

  @override
  String get done => 'Gotowe';

  @override
  String get experiencesInstructions => 'Naciśnij przycisk +, aby zacząć!';

  @override
  String get continueText => 'Kontynuuj';

  @override
  String get experiencesDescription => 'Co lubisz robić, kiedy podróżujesz?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-aplikacja-podrozy/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Moja Mapa Podróży';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Widziałem $percentage% świata';
  }

  @override
  String get requiresOnline =>
      'Przepraszamy, Visited wymaga aktywnego połączenia z siecią. Otwórz aplikację ustawień i upewnij się, że włączona jest funkcja Wi-Fi lub dane komórkowe, a tryb samolotowy jest wyłączony.';

  @override
  String get list => 'Lista';

  @override
  String get more => 'Więcej';

  @override
  String get myCountrySelections => 'Mój wybór krajów';

  @override
  String get cities => 'Miasta';

  @override
  String get citiesInstructions =>
      'Dotknij dowolnego kraju, aby rozpocząć wybieranie miast.';

  @override
  String get missingCitiesEmailTitle => 'Brakujące miasta!';

  @override
  String get lists => 'Listy';

  @override
  String get disputedTerritories => 'Terytoria sporne';

  @override
  String get sponsored => 'Sponsorowane';

  @override
  String get places => 'Miejsca';

  @override
  String get noListsError =>
      'Ups, brak list dostępnych w tej chwili, proszę spróbować trochę później.';

  @override
  String get noInspirationsError =>
      'Ups, żadne zdjęcia nie są dostępne w tej chwili, proszę spróbować trochę później';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Twoje najczęściej odwiedzane kraje:';

  @override
  String get update => 'Aktualizacja';

  @override
  String get signup => 'Zapisać się';

  @override
  String get loginWallSubtitle =>
      'Załóż darmowe konto, aby doświadczyć pełnej wersji Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Po zamknięciu aplikacji utracisz wszystkie wybrane opcje.';

  @override
  String get createAccount => 'Załóż konto';

  @override
  String get continueWithoutAccount => 'Kontynuuj bez konta';

  @override
  String get inspirationPromotion =>
      'Zainspiruj się piękną fotografią podróżniczą';

  @override
  String get saveStatsPromotion => 'Zapisz swoje statystyki podróży!';

  @override
  String get selectRegionsPromotion => 'Wybierz stany i prowincje';

  @override
  String get experiencesPromotion => 'Śledź doświadczenia na całym świecie';

  @override
  String get missingListItem =>
      'Czy coś przegapiliśmy? Stuknij tutaj, aby wysłać nam e -mail, aby dodać swoje ulubione miejsce.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Brakujący element z $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Odwiedziłem $amount $listName';
  }

  @override
  String get orderPoster => 'Plakat';

  @override
  String get shareMap => 'Udostępnij mapę';

  @override
  String get posterLandingPageTitle => 'Zdobądź swój plakat';

  @override
  String get posterNotAvailableError =>
      'Kupowanie plakatu nie jest teraz dostępne. Spróbuj ponownie później.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping wysyłka';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## O naszych niestandardowych mapach drukowanych\nWydrukuj swoją spersonalizowaną mapę świata. Dostosuj go do własnych kolorów i dostarczyć go prosto do domu.\n \n### Specyfikacje:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientacja krajobrazu.\n- Micro INK, kropelki do precyzyjnych wydruków, 8 -bitowy kolor, prawie jakość drukowania fotograficznego,\n- Satynowy papier o grubości 0,22 mm\n\n### Szczegóły dostawy:\nWysyłka z Toronto w Kanadzie do dowolnego miejsca na świecie za pomocą Kanady Post. Od 2 do 4 tygodni dostawy do większości miejsc docelowych. Wszystkie zamówienia są wysyłane zwinięte w kartonowym pudełku na podany adres wysyłki. Wszelkie płatności są obsługiwane przez Apple Pay, lub Stripe.\n\n\n### Anulowanie/zwrot:\nZamówienia są przetwarzane natychmiast po przesłaniu dla najszybszego możliwego zwrotu. Dlatego nie ma dostępnej zwrotu/anulowania.';

  @override
  String get posterDescriptionMarkdown =>
      '## O naszych niestandardowych mapach drukowanych\nWydrukuj swoją spersonalizowaną mapę świata. Dostosuj go do własnych kolorów i dostarczyć go prosto do domu.\n \n### Specyfikacje:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientacja krajobrazu.\n- Micro INK, kropelki do precyzyjnych wydruków, 8 -bitowy kolor, prawie jakość drukowania fotograficznego,\n- Satynowy papier o grubości 0,22 mm\n\n### Szczegóły dostawy:\nWysyłka z Toronto w Kanadzie do dowolnego miejsca na świecie za pomocą Kanady Post. Od 2 do 4 tygodni dostawy do większości miejsc docelowych. Wszystkie zamówienia są wysyłane zwinięte w kartonowym pudełku na podany adres wysyłki. Wszelkie płatności są obsługiwane przez Google Pay lub Stripe.\n\n\n### Anulowanie/zwrot:\nZamówienia są przetwarzane natychmiast po przesłaniu dla najszybszego możliwego zwrotu. Dlatego nie ma dostępnej zwrotu/anulowania.';

  @override
  String get posterCustomizeTitle => 'Dostosuj plakat';

  @override
  String get enterShippingAddress => 'Wprowadź adres wysyłki';

  @override
  String get price => 'Cena £';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + podatek';
  }

  @override
  String get showSelections => 'Wybór pokazu';

  @override
  String get posterNoRefunds =>
      'Po wydrukowaniu plakatu nie są dostępne żadne zwroty.';

  @override
  String get posterReviewOrder => 'Sprawdź swoje zamówienie';

  @override
  String get email => 'E-mail';

  @override
  String get emailEmptyError => 'Wpisz swój e -mail';

  @override
  String get fullName => 'Pełne imię i nazwisko';

  @override
  String get fullNameEmptyError => 'Wpisz imię i nazwisko';

  @override
  String get streetAddressEmptyError => 'Wprowadź swój adres uliczny';

  @override
  String get cityEmptyError => 'Wprowadź swoje miasto';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Wprowadź swój $fieldName';
  }

  @override
  String get country => 'Kraj';

  @override
  String get countryEmptyError => 'Wprowadź swój kraj';

  @override
  String get posterReviewOrderTitle => 'Sprawdź swoje zamówienie';

  @override
  String get buyNow => 'Kup Teraz';

  @override
  String get secureCheckoutDisclaimer =>
      'Bezpieczna kasa dostarczona przez Stripe';

  @override
  String get total => 'Całkowity';

  @override
  String get tax => 'Podatek';

  @override
  String get subtotal => 'SubTotal';

  @override
  String get posterProductName => 'Na zamówienie plakat mapy';

  @override
  String get shipping => 'Wysyłka';

  @override
  String get posterOrderReceivedTitle => 'zamówienie przyjęte';

  @override
  String get posterOrderReceivedSubtitle => 'Otrzymaliśmy Twoje zamówienie!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Sprawdź swój e -mail, aby uzyskać więcej aktualizacji. \nZezwól na 4 tydzień na przybycie plakatu. \nJeśli masz jakieś pytania, napisz do nas na [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Wydrukowane status zamówienia plakatu';

  @override
  String get moreInfo => 'Więcej informacji';

  @override
  String get logoutConfirm => 'Czy chciałbyś wylogować się z aplikacji?';

  @override
  String get emailNotAvailable => 'Ten e -mail został pobrany.';

  @override
  String get alphabetical => 'Alfabetyczny';

  @override
  String get firstTimeLiveTutorial =>
      'Podanie swojego kraju i miasta spersonalizuje Twoje doświadczenie z aplikacją.';

  @override
  String get firstTimeBeenTutorial =>
      'Wybierając miejsce, w którym byłeś, możesz wyświetlić mapę wszystkich krajów, w których byłeś i zobaczyć osobiste statystyki.';

  @override
  String get progressTooltipGoal =>
      'Twoje cele podróży oparte są na liczbie krajów, które \"chcesz\" odwiedzić w porównaniu do krajów, w których \"byłeś\".';

  @override
  String get progressTooltipRank =>
      'Liczba ta pokazuje, jak plasujesz się w porównaniu z podróżnikami z całego świata.  Możesz zwiększyć swoją rangę podróżując do większej liczby krajów.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Ten wykres jest oparty na liczbie krajów, w których byłeś, w porównaniu do wszystkich krajów świata.';

  @override
  String get sortBy => 'Sortuj według';

  @override
  String get updateWishlist => 'Aktualizuj listę życzeń';

  @override
  String get mapInfo =>
      'Kliknij na kraj, aby wybrać był, chce lub mieszka. Możesz również kliknąć na ikonę znajdującą się w lewym górnym rogu, aby wyświetlić widok listy.';

  @override
  String get oneTimePurchase => 'Wszystko jest jednorazowym zakupem!';

  @override
  String get contact => 'Kontakt';

  @override
  String get contactUs => 'Skontaktuj się z nami';

  @override
  String get noCitiesSelected => 'Nie wybrałeś jeszcze żadnego miasta...';

  @override
  String get updateTravelGoal => 'Zaktualizuj cel podróży';

  @override
  String get travelGoalComplete =>
      'Gratulacje! \n\nyou osiągnął cel podróży! \n\ntap przycisk +, aby dodać więcej krajów.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Nie ma konta skojarzonego e -mail $email. Czy chciałbyś to teraz stworzyć?';
  }

  @override
  String get tryAgain => 'Spróbuj ponownie';

  @override
  String get itineraries => 'Plany podróży';

  @override
  String get itinerary => 'Plan podróży';

  @override
  String get place => 'Miejsce';

  @override
  String get itinerariesDescription =>
      'To są miejsca, którymi wyraziłeś zainteresowanie.\nSkorzystaj z tego przewodnika, aby zaplanować następne wakacje.';

  @override
  String get addMore => 'Dodaj więcej';

  @override
  String get interests => 'Zainteresowania';

  @override
  String get selection => 'Wybór';

  @override
  String get goal => 'Bramka';

  @override
  String get noItineraries => 'Brak Itinerariuszy';

  @override
  String get noItinerariesExplanation =>
      'Proszę dodać kilka miejsc, inspiracji lub doświadczeń, aby zobaczyć, jak automatycznie generują się Twoje trasy podróży.';

  @override
  String get clusterPins => 'Grupuj Znaczniki Mapy';

  @override
  String get toggleRegions => 'Pokaż regiony podczas powiększania';

  @override
  String get mapProjection => 'Projekcja mapy';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Równoważnoprzestrzenny';

  @override
  String get yourTravellerType => 'Twój typ podróżnika:';

  @override
  String get yourHotelPreferences => 'Twoje preferencje hotelowe:';

  @override
  String get budget => 'Budżetowy';

  @override
  String get midScale => 'Średnia klasa';

  @override
  String get luxury => 'Luksusowy';

  @override
  String get noTravellerType =>
      'Dodaj elementy do swojej listy życzeń, aby odkryć, jakim typem podróżnika jesteś.';

  @override
  String get unlockLived => 'Odblokuj Życie';

  @override
  String get unlockLivedDescription =>
      'Wybierz na mapie, gdzie wcześniej mieszkałeś!';

  @override
  String get futureFeaturesDescription => '...i wszystkie przyszłe funkcje';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Twój najczęściej odwiedzany kraj:';

  @override
  String get departureDate => 'Data wyjazdu';

  @override
  String get returnDate => 'Data powrotu';

  @override
  String get hotels => 'Hotele';

  @override
  String get food => 'Jedzenie';

  @override
  String get travelDates => 'Terminy podróży';

  @override
  String get posterForMe => 'Dla mnie';

  @override
  String get posterSendGift => 'Wyślij prezent';

  @override
  String get addSelections => 'Dodaj wybory';

  @override
  String get posterType => 'Typ plakatu';

  @override
  String get help => 'Pomoc';

  @override
  String get tutorialMap =>
      'Kliknij kraj, aby wybrać: byłeś, chcesz i mieszkałeś.';

  @override
  String get tutorialMapList =>
      'Stuknij ikonę listy (lewy górny róg), aby wybrać według listy.';

  @override
  String get tutorialCountryDetails =>
      'Kliknij kraj, a następnie „więcej”, aby wybrać według regionu.';

  @override
  String get tutorialItems =>
      'Przesuń przełącznik, aby wybrać sposób zaznaczania elementów.';

  @override
  String get tutorialInspirations =>
      'Przesuń w prawo lub w lewo, aby przejść do następnej karty.';

  @override
  String get lifetime => 'Dożywotni';

  @override
  String get chooseYourPlan => 'Wybierz swój plan';

  @override
  String get requestARefund => 'Poproś o zwrot pieniędzy';

  @override
  String get noPurchasesFound => 'Nie znaleziono zakupów.';

  @override
  String get noProductsAvailable => 'Brak dostępnych produktów';

  @override
  String get posterLandingAppBar => 'Zabierz swoje historie do domu';

  @override
  String get posterLandingSubHeading => 'Twoja podróż, Twoja historia';

  @override
  String get posterLandingSubDescription =>
      'Twoje podróże to coś więcej niż wycieczki, to historie, wspomnienia i kamienie milowe. Zmień te niezapomniane chwile w spersonalizowaną mapę świata, która jest tak wyjątkowa jak Twoje przygody.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Mapa Twoich osiągnięć: wyróżnij każdy cel podróży, od pierwszej wielkiej podróży po najbardziej odważną przygodę.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Świętuj każdą podróż: codziennie przeżywaj swoje podróże na nowo dzięki pięknie wykonanemu plakatowi zaprojektowanemu, aby inspirować.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Prezent, który będzie dla nich skarbem: zaskocz współpodróżnika niestandardową mapą przedstawiającą jego podróż, idealną na urodziny, kamienie milowe lub po prostu tak.';

  @override
  String get posterLandingHowItWorks => 'Jak to działa!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Dostosuj swój projekt: wybierz kolory, style i zaznacz swoje podróże (lub ich!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Podgląd mapy: zobacz, jak ożywa przed złożeniem zamówienia.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Bezpieczna płatność: szybka i bezpieczna dzięki Apple Pay lub Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Bezpieczna płatność: szybka i bezpieczna dzięki Google Pay lub Stripe. 4. Gotowe do ekspozycji: Wyślemy produkt prosto pod Twoje drzwi (lub pod ich drzwi).';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Gotowy do wyświetlenia: Wyślemy go prosto do Twoich (lub ich) drzwi.';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Doświadczenia innych podróżników';

  @override
  String get posterLandingCustomerReview1 =>
      '„Ta mapa to świetny sposób na śledzenie wszystkich miejsc, do których podróżowałem, i planowanie przyszłych podróży. Jest solidnej jakości i świetnie wygląda, wisząc w moim biurze. Kupiłem nawet jedną dla mojego brata i nie mógł przestać mówić, jaka jest fajna!” - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '„Podróżowałem do ponad 150 portów, pracując na statku wycieczkowym. Ta mapa to świetny dodatek do mojego salonu jako pamiątka wszystkich lat spędzonych na morzu”. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      '„Świetny prezent na Dzień Matki. Moja mama była bardzo wzruszona!” Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '„Wydrukowałem mapę miejsc, które chciałem odwiedzić z moją dziewczyną. To był świetny prezent świąteczny. Wysoka jakość”. Brad J.';

  @override
  String get posterLandingSpecifications => 'Dane techniczne';

  @override
  String get posterLandingSpecification1 =>
      '• Wymiary: 16\" x 20\" (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '• Orientacja: Pozioma';

  @override
  String get posterLandingSpecification3 =>
      '• Jakość druku: Mikroatrament, krople dla precyzyjnych wydruków. 8-bitowy kolor, prawie fotograficzna jakość wydruku. • Papier: papier satynowy o grubości 0,22 mm';

  @override
  String get posterLandingSpecification4 => 'Szczegóły wysyłki';

  @override
  String get posterLandingShippingHeader =>
      '• Wysyłka z Toronto w Kanadzie do dowolnego miejsca na świecie za pośrednictwem Poczty Kanadyjskiej.';

  @override
  String get posterLandingShipping1 =>
      '• Dostawa do większości miejsc zajmuje od 2 do 4 tygodni.';

  @override
  String get posterLandingShipping2 =>
      '• Wszystkie zamówienia są zwijane w tekturowe pudełko na podany adres wysyłki.';

  @override
  String get posterLandingShipping3 => 'Anulowanie/Zwrot pieniędzy:';

  @override
  String get posterLandingCancellationHeader =>
      'Zwroty pieniędzy są dostępne przed wysłaniem plakatu do drukarni, co może potrwać do 24 godzin. Po przetworzeniu zamówienia zwrot pieniędzy/anulowanie nie jest możliwe. Otrzymasz wiadomość e-mail po wydrukowaniu zamówienia.';

  @override
  String get posterLandingCancellationBody =>
      'Zwroty są dostępne przed wysłaniem plakatu do drukarni, co może potrwać do 24 godzin.  Po przetworzeniu zamówienia zwrot/anulowanie nie są dostępne.  Po wydrukowaniu zamówienia otrzymasz wiadomość e-mail.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
