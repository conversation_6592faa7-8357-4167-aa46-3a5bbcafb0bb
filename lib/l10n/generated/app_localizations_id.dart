// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Indonesian (`id`).
class AppLocalizationsId extends AppLocalizations {
  AppLocalizationsId([String locale = 'id']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Bahasa';

  @override
  String get pickEmailApp => 'Pilih aplikasi email Anda';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Saya telah mengunjungi $amount negara! Berapa banyak yang telah Anda kunjungi? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Saya telah mengunjungi $amount kota! Berapa banyak yang telah Anda kunjungi? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Saya telah mengunjungi $amount $listName! Berapa banyak yang telah Anda kunjungi? www.visitedapp.com';
  }

  @override
  String get clear => 'Jelas';

  @override
  String get been => 'Pernah';

  @override
  String get want => 'Ingin';

  @override
  String get live => 'Tinggal';

  @override
  String get lived => 'Pernah Tinggal';

  @override
  String get water => 'Air';

  @override
  String get land => 'Tanah';

  @override
  String get borders => 'Perbatasan';

  @override
  String get labels => 'Label';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inspirasi';

  @override
  String get inspirations => 'Inspirasi';

  @override
  String get delete => 'Hapus';

  @override
  String get unlockVisitedUpsellTitle => 'Ingin melihat lebih banyak?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Buka semua fitur dan nikmati Visited di kapasitas terbaiknya';

  @override
  String get checkTheDetails => 'Periksa Detail';

  @override
  String get moreInspirationsComingSoon =>
      'Kami sedang bekerja untuk mendapatkan lebih banyak gambar. Kembali lagi nanti.';

  @override
  String get unlockPremiumFeatures => 'Buka fitur premium';

  @override
  String get purchased => 'Dibeli!';

  @override
  String get buy => 'Beli';

  @override
  String get restorePurchases => 'Pulihkan Pembelian';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Apakah anda yakin?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Penghapusan kartu bersifat permanen. Tidak ada cara untuk memulihkan gambar ini.';

  @override
  String get cancel => 'Batalkan';

  @override
  String get map => 'Peta';

  @override
  String get progress => 'Progres';

  @override
  String get myTravelGoal => 'Tujuan Perjalanan Saya';

  @override
  String goalRemaining(int remaining) {
    return '$remaining lebih banyak lagi!';
  }

  @override
  String get top => 'TERATAS';

  @override
  String get ofTheWorld => 'di dunia!';

  @override
  String get countries => 'negara';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Negara Teratas yang dikunjungi dari $country:';
  }

  @override
  String get login => 'Masuk';

  @override
  String get logout => 'Keluar';

  @override
  String get enterYourEmail => 'Masukkan email anda';

  @override
  String get privacyPolicy => 'Kebijakan Privasi';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-indonesian/';

  @override
  String get termsOfUse => 'Syarat Penggunaan';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-indonesian/';

  @override
  String get errorTitle => 'Uupps!';

  @override
  String get enterValidEmail => 'Harap masukkan email yang benar';

  @override
  String get settings => 'Pengaturan';

  @override
  String get whereDoYouLive => 'Dimana kamu tinggal?';

  @override
  String get whereHaveYouBeen => 'Darimana saja anda?';

  @override
  String get whereDoYouFlyFrom => 'Darimana anda terbang?';

  @override
  String get next => 'Selanjutnya';

  @override
  String get missingAirports =>
      'Tidak menemukan apa yang anda cari? Kirim email kepada <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Bandara Terlewat!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Selamat Datang di Visited';

  @override
  String get welcomeSubtitle => 'Petualangan seumur hidup sedang menanti';

  @override
  String get getStarted => 'Memulai';

  @override
  String get privacyAgreement => 'Perjanjian Privasi';

  @override
  String get privacyAgreementSubtitle =>
      'Harap setujui item berikut sebelum melanjutkan menggunakan Visited..';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Dengan mencentang kotak ini, anda menyatakan bahwa anda telah membaca dan setuju untuk terikat dengan [Kebijakan Privasi](https://www.arrivinginhighheels.com/privacy-policy) dan [Ketentuan Penggunaan](https://www.arrivinginhighheels.com/privacy-policy) dari Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Saya setuju untuk menerima pesan elektronik dari Arriving in High Heels yang berisi informasi dan penawaran sehubungan dengan produk, aplikasi, dan layanan yang mungkin menarik bagi saya, termasuk pemberitahuan penjualan, promosi, penawaran, dan buletin. Saya dapat menarik persetujuan ini kapan saja seperti yang dijelaskan dalam Kebijakan Privasi atau dengan mengklik tautan \"berhenti berlangganan\" dalam pesan elektronik.';

  @override
  String get submit => 'Kirim';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Anda harus menyetujui persyaratan dan keikutsertaan kami untuk terus menggunakan Visited';

  @override
  String get deleteAccount => 'Hapus Akun';

  @override
  String get removeAdsUpsell =>
      'Apakah anda ingin menghentikan iklan dan berhenti berlangganan email marketing ?';

  @override
  String get deleteAccountWarning =>
      'Menghapus akun anda akan menghapus semua informasi anda dari server kami.\n Proses ini tidak dapat dibatalkan.';

  @override
  String get about => 'Tentang';

  @override
  String get popularity => 'Popularitas';

  @override
  String get regions => 'Wilayah';

  @override
  String get population => 'Populasi';

  @override
  String get size => 'Ukuran';

  @override
  String get coverage => 'Cakupan';

  @override
  String get percentOfCountryVisited => '% negara yang dikunjungi';

  @override
  String get visited => 'dikunjungi';

  @override
  String get notes => 'Catatan';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Sesuaikan';

  @override
  String get onlyCountSovereign => 'Hanya Hitung Negara yang Berdaulat';

  @override
  String get countUkSeparately =>
      'Hitung Negara Bagian Inggris secara terpisah';

  @override
  String get showLegend => 'Tampilkan Legenda';

  @override
  String get showLivedPin => 'Tampilkan Pin Langsung';

  @override
  String get useMyColours => 'Gunakan Warna Saya';

  @override
  String get mapColors => 'Warna Peta';

  @override
  String get traveller => 'Wisatawan';

  @override
  String get nightTraveller => 'Wisatawan Malam';

  @override
  String get original => 'Asli';

  @override
  String get explorer => 'Penjelajah';

  @override
  String get weekender => 'Si Akhir Pekan';

  @override
  String get naturalist => 'Sang Naturalis';

  @override
  String get historian => 'Sejarawan';

  @override
  String get thrillSeeker => 'Pencari Tantangan';

  @override
  String get culturalBuff => 'Penggemar Budaya';

  @override
  String get myColors => 'Warna Saya';

  @override
  String get experiences => 'Pengalaman';

  @override
  String get done => 'Selesai';

  @override
  String get experiencesInstructions => 'Ketuk tombol + untuk memulai!';

  @override
  String get continueText => 'Lanjutkan';

  @override
  String get experiencesDescription => 'Apa yang anda sukai saat berwisata?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Peta Perjalanan Saya';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Saya telah melihat $percentage% dari dunia';
  }

  @override
  String get requiresOnline =>
      'Maaf, Visited memerlukan koneksi jaringan yang aktif. Silakan buka aplikasi pengaturan anda dan pastikan bahwa \\r Wi-Fi atau data Seluler diaktifkan dan Mode Pesawat dinonaktifkan';

  @override
  String get list => 'Daftar';

  @override
  String get more => 'Lebih Banyak';

  @override
  String get myCountrySelections => 'Negara Pilihan Saya';

  @override
  String get cities => 'Kota';

  @override
  String get citiesInstructions =>
      'Ketuk negara mana saja untuk mulai memilih kota';

  @override
  String get missingCitiesEmailTitle => 'Kota yang Terlewat!';

  @override
  String get lists => 'Daftar';

  @override
  String get disputedTerritories => 'Wilayah Sengketa';

  @override
  String get sponsored => 'Disponsori';

  @override
  String get places => 'Tempat';

  @override
  String get noListsError =>
      'Ups, tidak ada daftar yang tersedia saat ini, silahkan coba lagi nanti';

  @override
  String get noInspirationsError =>
      'Ups, tidak ada foto yang tersedia saat ini, silahkan coba lagi nanti';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Negara yang paling sering Anda kunjungi:';

  @override
  String get update => 'Memperbarui';

  @override
  String get signup => 'Daftar';

  @override
  String get loginWallSubtitle =>
      'Membuat akun gratis untuk mengalami versi lengkap Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Anda akan kehilangan semua pilihan Anda setelah menutup aplikasi.';

  @override
  String get createAccount => 'Buat Akun';

  @override
  String get continueWithoutAccount => 'Lanjutkan tanpa akun';

  @override
  String get inspirationPromotion =>
      'Dapatkan inspirasi dengan fotografi perjalanan yang indah';

  @override
  String get saveStatsPromotion => 'Simpan Statistik Perjalanan Anda!';

  @override
  String get selectRegionsPromotion => 'Pilih Negara bagian dan Provinsi';

  @override
  String get experiencesPromotion => 'Melacak Pengalaman di Seluruh Dunia';

  @override
  String get missingListItem =>
      'Apakah kita melewatkan sesuatu? Ketuk di sini untuk mengirimi kami email agar tempat favorit Anda ditambahkan.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Item yang hilang dari $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Saya telah mengunjungi $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Bagikan peta';

  @override
  String get posterLandingPageTitle => 'Dapatkan poster Anda';

  @override
  String get posterNotAvailableError =>
      'Pembelian poster tidak tersedia sekarang. Silakan coba lagi nanti.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping pengiriman';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Tentang peta cetak khusus kami\nCetak peta dunia pribadi Anda. Kustomisasi dengan warna Anda sendiri dan kirimkan langsung ke rumah Anda.\n \n### Spesifikasi:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientasi lansekap.\n- Tinta mikro, tetesan untuk cetakan yang tepat, warna 8 bit, kualitas cetak hampir foto,\n- Kertas satin tebal 0,22mm\n\n### Rincian pengiriman:\nPengiriman dari Toronto, Kanada ke mana saja di dunia menggunakan Canada Post. Mohon tunggu 2 hingga 4 minggu untuk pengiriman ke sebagian besar tujuan. Semua pesanan dikirim digulung dalam kotak tabung kardus ke alamat pengiriman yang dikirimkan. Semua pembayaran ditangani oleh Apple Pay, atau Stripe.\n\n\n### Pembatalan/Pengembalian Dana:\nPesanan diproses segera setelah diserahkan untuk perputaran tercepat. Oleh karena itu, tidak ada pengembalian uang/pembatalan yang tersedia.';

  @override
  String get posterDescriptionMarkdown =>
      '## Tentang peta cetak khusus kami\nCetak peta dunia pribadi Anda. Kustomisasi dengan warna Anda sendiri dan kirimkan langsung ke rumah Anda.\n \n### Spesifikasi:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientasi lansekap.\n- Tinta mikro, tetesan untuk cetakan yang tepat, warna 8 bit, kualitas cetak hampir foto,\n- Kertas satin tebal 0,22mm\n\n### Rincian pengiriman:\nPengiriman dari Toronto, Kanada ke mana saja di dunia menggunakan Canada Post. Mohon tunggu 2 hingga 4 minggu untuk pengiriman ke sebagian besar tujuan. Semua pesanan dikirim digulung dalam kotak tabung kardus ke alamat pengiriman yang dikirimkan. Semua pembayaran ditangani oleh Google Pay atau Stripe.\n\n\n### Pembatalan/Pengembalian Dana:\nPesanan diproses segera setelah diserahkan untuk perputaran tercepat. Oleh karena itu, tidak ada pengembalian uang/pembatalan yang tersedia.';

  @override
  String get posterCustomizeTitle => 'Kustomisasi poster';

  @override
  String get enterShippingAddress => 'Masukkan alamat pengiriman';

  @override
  String get price => 'Harga';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + pajak';
  }

  @override
  String get showSelections => 'Tampilkan pilihan';

  @override
  String get posterNoRefunds =>
      'Tidak ada pengembalian uang yang tersedia setelah poster Anda dicetak.';

  @override
  String get posterReviewOrder => 'Tinjau pesanan Anda';

  @override
  String get email => 'Surel';

  @override
  String get emailEmptyError => 'Masukkan email Anda';

  @override
  String get fullName => 'Nama lengkap';

  @override
  String get fullNameEmptyError => 'Harap masukkan nama lengkap Anda';

  @override
  String get streetAddressEmptyError => 'Harap masukkan alamat jalan Anda';

  @override
  String get cityEmptyError => 'Harap masukkan kota Anda';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Harap masukkan $fieldName Anda';
  }

  @override
  String get country => 'Negara';

  @override
  String get countryEmptyError => 'Harap masukkan negara Anda';

  @override
  String get posterReviewOrderTitle => 'Tinjau pesanan Anda';

  @override
  String get buyNow => 'Beli sekarang';

  @override
  String get secureCheckoutDisclaimer =>
      'Amankan checkout yang disediakan oleh Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Pajak';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Poster peta yang dikunjungi khusus';

  @override
  String get shipping => 'Pengiriman';

  @override
  String get posterOrderReceivedTitle => 'Pesanan Diterima';

  @override
  String get posterOrderReceivedSubtitle => 'Kami menerima pesanan Anda!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Periksa email Anda untuk pembaruan lebih lanjut. \nHarap tewas hingga 4 minggu agar poster Anda tiba. \nJika Anda memiliki pertanyaan, silakan email kami di [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Status Pesanan Poster Dicetak';

  @override
  String get moreInfo => 'Informasi lebih lanjut';

  @override
  String get logoutConfirm => 'Apakah Anda ingin keluar dari aplikasi?';

  @override
  String get emailNotAvailable => 'Email itu telah diambil.';

  @override
  String get alphabetical => 'Alfabetis';

  @override
  String get firstTimeLiveTutorial =>
      'Menyediakan negara dan kota asal Anda akan mempersonalisasikan pengalaman aplikasi Anda.';

  @override
  String get firstTimeBeenTutorial =>
      'Memilih di mana Anda telah memungkinkan Anda untuk melihat peta Anda dari semua negara yang telah Anda kunjungi dan melihat statistik pribadi.';

  @override
  String get progressTooltipGoal =>
      'Tujuan perjalanan Anda didasarkan pada jumlah negara yang Anda \"ingin\" bepergian dibandingkan dengan negara -negara di mana Anda \"pernah\".';

  @override
  String get progressTooltipRank =>
      'Jumlah ini menunjukkan bagaimana peringkat Anda dibandingkan dengan pelancong di seluruh dunia. Anda dapat meningkatkan peringkat Anda dengan bepergian ke lebih banyak negara.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Grafik ini didasarkan pada jumlah negara yang telah Anda bandingkan dengan total negara di dunia.';

  @override
  String get sortBy => 'Sortir dengan';

  @override
  String get updateWishlist => 'Perbarui daftar keinginan';

  @override
  String get mapInfo =>
      'Klik negara untuk memilih, ingin atau hidup. Anda juga dapat mengklik ikon yang ditemukan di sudut kiri atas untuk tampilan daftar.';

  @override
  String get oneTimePurchase => 'Semuanya adalah pembelian satu kali!';

  @override
  String get contact => 'Kontak';

  @override
  String get contactUs => 'Hubungi kami';

  @override
  String get noCitiesSelected => 'Anda belum memilih kota mana pun, namun ...';

  @override
  String get updateTravelGoal => 'Perbarui tujuan perjalanan';

  @override
  String get travelGoalComplete =>
      'Selamat! \n\nyou telah menyelesaikan tujuan perjalanan Anda! \n\ntap tombol + untuk menambahkan lebih banyak negara.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Tidak ada akun yang terkait dengan email $email. Apakah Anda ingin membuatnya sekarang?';
  }

  @override
  String get tryAgain => 'Coba Lagi';

  @override
  String get itineraries => 'Rencana Perjalanan';

  @override
  String get itinerary => 'Rencana Perjalanan';

  @override
  String get place => 'Tempat';

  @override
  String get itinerariesDescription =>
      'Ini adalah tempat-tempat yang Anda minati.\nGunakan panduan ini untuk membantu merencanakan liburan Anda berikutnya.';

  @override
  String get addMore => 'Tambahkan Lebih Banyak';

  @override
  String get interests => 'Minat';

  @override
  String get selection => 'Pilihan';

  @override
  String get goal => 'Sasaran';

  @override
  String get noItineraries => 'Tidak Ada Rencana Perjalanan';

  @override
  String get noItinerariesExplanation =>
      'Tambahkan beberapa tempat, inspirasi atau pengalaman untuk melihat itinerari Anda secara otomatis tercipta.';

  @override
  String get clusterPins => 'Kelompokkan Penanda Peta';

  @override
  String get toggleRegions => 'Tampilkan Wilayah Saat Zoom In';

  @override
  String get mapProjection => 'Proyeksi Peta';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Ekuirektangular';

  @override
  String get yourTravellerType => 'Tipe pelancong Anda:';

  @override
  String get yourHotelPreferences => 'Preferensi Hotel Anda:';

  @override
  String get budget => 'Anggaran';

  @override
  String get midScale => 'Kelas Menengah';

  @override
  String get luxury => 'Mewah';

  @override
  String get noTravellerType =>
      'Tambahkan item ke daftar keinginan Anda untuk menemukan tipe pelancong Anda.';

  @override
  String get unlockLived => 'Buka Hidup';

  @override
  String get unlockLivedDescription =>
      'Pilih di mana Anda pernah tinggal di peta!';

  @override
  String get futureFeaturesDescription => '...dan semua fitur masa depan';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Negara yang Paling Sering Anda Kunjungi:';

  @override
  String get departureDate => 'Tanggal Keberangkatan';

  @override
  String get returnDate => 'Tanggal Kembali';

  @override
  String get hotels => 'Hotel';

  @override
  String get food => 'Makanan';

  @override
  String get travelDates => 'Tanggal Perjalanan';

  @override
  String get posterForMe => 'Untuk Saya';

  @override
  String get posterSendGift => 'Kirim hadiah';

  @override
  String get addSelections => 'Tambahkan Pilihan';

  @override
  String get posterType => 'Jenis Poster';

  @override
  String get help => 'Membantu';

  @override
  String get tutorialMap =>
      'Ketuk negara yang akan dipilih: pernah, diinginkan, dan ditinggali.';

  @override
  String get tutorialMapList =>
      'Ketuk ikon daftar (pojok kiri atas) untuk memilih berdasarkan daftar.';

  @override
  String get tutorialCountryDetails =>
      'Ketuk negara lalu “lainnya” untuk memilih berdasarkan wilayah.';

  @override
  String get tutorialItems =>
      'Geser tombol untuk memilih cara Anda memilih item.';

  @override
  String get tutorialInspirations =>
      'Gesek ke kanan atau kiri untuk berpindah ke kartu berikutnya.';

  @override
  String get lifetime => 'Seumur hidup';

  @override
  String get chooseYourPlan => 'Pilih rencana Anda';

  @override
  String get requestARefund => 'Minta pengembalian dana';

  @override
  String get noPurchasesFound => 'Tidak ada pembelian yang ditemukan.';

  @override
  String get noProductsAvailable => 'Tidak ada produk yang tersedia';

  @override
  String get posterLandingAppBar => 'Bawa Kisah Anda ke Rumah';

  @override
  String get posterLandingSubHeading => 'Perjalanan Anda, Kisah Anda';

  @override
  String get posterLandingSubDescription =>
      'Perjalanan Anda lebih dari sekadar perjalanan, melainkan kisah, kenangan, dan tonggak sejarah. Ubah momen tak terlupakan itu menjadi peta dunia yang dipersonalisasi dan seunik petualangan Anda.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Peta Pencapaian Anda: Soroti setiap destinasi, dari perjalanan besar pertama Anda hingga petualangan paling menantang Anda.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Rayakan Setiap Perjalanan: Hidupkan kembali perjalanan Anda setiap hari dengan poster yang dibuat dengan indah untuk menginspirasi.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Hadiah yang Akan Mereka Hargai: Kejutkan sesama pelancong dengan peta khusus yang memamerkan perjalanan mereka, cocok untuk ulang tahun, tonggak sejarah, atau apa pun.';

  @override
  String get posterLandingHowItWorks => 'Cara Kerjanya!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Kustomisasi Desain Anda: Pilih warna, gaya, dan tandai perjalanan Anda (atau perjalanan mereka!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Pratinjau Peta Anda: Lihat peta itu menjadi kenyataan sebelum Anda memesan.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Pembayaran Aman: Cepat dan aman dengan Apple Pay atau Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Pembayaran Aman: Cepat dan aman dengan Google Pay atau Stripe. ';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Siap untuk Dipajang: Kami akan mengirimkannya langsung ke rumah Anda (atau rumah mereka).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Pengalaman dari Sesama Wisatawan';

  @override
  String get posterLandingCustomerReview1 =>
      'Peta ini adalah cara yang bagus untuk melacak semua tempat yang telah saya kunjungi dan merencanakan perjalanan kami di masa mendatang. Kualitasnya bagus dan terlihat mengagumkan saat dipajang di kantor saya. Saya bahkan membelikannya untuk saudara laki-laki saya dan dia tidak bisa berhenti membicarakan betapa kerennya peta ini! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Saya telah bepergian ke lebih dari 150 pelabuhan saat bekerja di kapal pesiar. Peta ini merupakan tambahan yang bagus untuk ruang tamu saya sebagai kenangan akan semua tahun yang telah saya lalui di laut. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Hadiah yang luar biasa untuk hari ibu. Ibu saya sangat tersentuh! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Mencetak peta tempat-tempat yang ingin saya kunjungi bersama pacar saya. Itu adalah hadiah Natal yang luar biasa. Kualitasnya juga tinggi. Brad J.';

  @override
  String get posterLandingSpecifications => 'Spesifikasi';

  @override
  String get posterLandingSpecification1 =>
      '• Dimensi: 16\" x 20\" (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '• Orientasi: Lanskap';

  @override
  String get posterLandingSpecification3 =>
      '• Kualitas Cetak: Tinta mikro, tetesan untuk cetakan yang presisi. Warna 8 bit, kualitas cetak hampir seperti foto.';

  @override
  String get posterLandingSpecification4 =>
      '• Kertas: Kertas satin setebal 0,22 mm';

  @override
  String get posterLandingShippingHeader => 'Rincian Pengiriman';

  @override
  String get posterLandingShipping1 =>
      '• Pengiriman dari Toronto, Kanada ke mana pun di dunia menggunakan Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Pengiriman ke sebagian besar tujuan memerlukan waktu 2-4 minggu.';

  @override
  String get posterLandingShipping3 =>
      '• Semua pesanan digulung dalam kotak tabung kardus ke alamat pengiriman yang Anda berikan.';

  @override
  String get posterLandingCancellationHeader => 'Pembatalan/Pengembalian Dana:';

  @override
  String get posterLandingCancellationBody =>
      'Pengembalian dana tersedia sebelum poster Anda dikirim ke percetakan, yang dapat memakan waktu hingga 24 jam. Setelah pesanan Anda diproses, tidak ada pengembalian dana/pembatalan yang tersedia. Anda akan menerima email saat pesanan Anda telah dicetak.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
