// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Norwegian Bokmål (`nb`).
class AppLocalizationsNb extends AppLocalizations {
  AppLocalizationsNb([String locale = 'nb']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Språk';

  @override
  String get pickEmailApp => 'Velg e-postappen din';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Jeg har besøkt $amount land! Hvor mange har du besøkt? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Jeg har besøkt $amount byer! Hvor mange har du besøkt? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Jeg har besøkt $amount $listName! Hvor mange har du besøkt? www.visitedapp.com';
  }

  @override
  String get clear => 'Klar';

  @override
  String get been => 'Vært';

  @override
  String get want => 'Ønsker';

  @override
  String get live => 'Bo';

  @override
  String get lived => 'Bodde';

  @override
  String get water => 'Vann';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Grenser';

  @override
  String get labels => 'Etiketter';

  @override
  String get legend => 'Legende';

  @override
  String get inspiration => 'Inspirasjon';

  @override
  String get inspirations => 'Inspirasjoner';

  @override
  String get delete => 'Slett';

  @override
  String get unlockVisitedUpsellTitle => 'Vil se mer?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Lås opp alle funksjonene og nyt Visited i full styrke';

  @override
  String get checkTheDetails => 'Sjekk detaljene';

  @override
  String get moreInspirationsComingSoon =>
      'Vi jobber med å få flere bilder. Kom tilbake snart!';

  @override
  String get unlockPremiumFeatures => 'Lås opp premium-funksjoner';

  @override
  String get purchased => 'Kjøpt!';

  @override
  String get buy => 'Kjøp';

  @override
  String get restorePurchases => 'Gjenopprett kjøp';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Er du sikker?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Å slette dette kortet er permanent. Det er ingen måte å gjenopprette dette bildet.';

  @override
  String get cancel => 'Avbryt';

  @override
  String get map => 'Kart';

  @override
  String get progress => 'Framgang';

  @override
  String get myTravelGoal => 'Mitt Reisemål';

  @override
  String goalRemaining(int remaining) {
    return '$remaining mer å gå!';
  }

  @override
  String get top => 'TOPP';

  @override
  String get ofTheWorld => 'av verden!';

  @override
  String get countries => 'land';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Topp Land Visited fra $country:';
  }

  @override
  String get login => 'Logg inn';

  @override
  String get logout => 'Logg ut';

  @override
  String get enterYourEmail => 'Skriv inn epostadressen din';

  @override
  String get privacyPolicy => 'Personvernregler';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-norwegian/';

  @override
  String get termsOfUse => 'Vilkår for bruk';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-norwegian/';

  @override
  String get errorTitle => 'Ops!';

  @override
  String get enterValidEmail => 'Vennligst skriv inn en gyldig e-postadresse';

  @override
  String get settings => 'Innstillinger';

  @override
  String get whereDoYouLive => 'Hvor bor du?';

  @override
  String get whereHaveYouBeen => 'Hvor har du vært?';

  @override
  String get whereDoYouFlyFrom => 'Hvor flyr du ut av?';

  @override
  String get next => 'Neste';

  @override
  String get missingAirports =>
      'Ser du ikke hva du leter etter? Send oss ​​en e-post på <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Mangler flyplasser!';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => 'Velkommen til Visited';

  @override
  String get welcomeSubtitle => 'Livets eventyr venter';

  @override
  String get getStarted => 'Kom i gang';

  @override
  String get privacyAgreement => 'Personvernavtale';

  @override
  String get privacyAgreementSubtitle =>
      'Godta følgende ting før du fortsetter å bruke Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Ved å merke av i denne boksen erkjenner du at du har lest og godtar å være bundet av Arriving in High Heels \'[Privacy Policy] (https://www.arrivinginhighheels.com/privacy-policy) og [Terms of Use] (http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Jeg godtar å motta elektroniske meldinger fra Arriving in High Heels som inneholder informasjon og tilbud med hensyn til produkter, applikasjoner og tjenester som kan være av interesse for meg, inkludert varsel om salg, kampanjer, tilbud og nyhetsbrev. Jeg kan når som helst trekke tilbake dette samtykke som beskrevet i personvernerklæringen eller ved å klikke på \"avmelding\"-linken i de elektroniske meldingene.';

  @override
  String get submit => 'Sende inn';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Du må godta begge vilkårene våre og velge å fortsette å bruke Visited.';

  @override
  String get deleteAccount => 'Slett Konto';

  @override
  String get removeAdsUpsell =>
      'Ønsker du å velge bort annonser og avslutte abonnementet på e-postmarkedsføring i stedet?';

  @override
  String get deleteAccountWarning =>
      'Når du sletter kontoen din, fjernes all informasjonen fra serverne våre.\n Denne prosessen kan ikke angres.';

  @override
  String get about => 'Om';

  @override
  String get popularity => 'Popularitet';

  @override
  String get regions => 'Regioner';

  @override
  String get population => 'Befolkning';

  @override
  String get size => 'Størrelse';

  @override
  String get coverage => 'Dekning';

  @override
  String get percentOfCountryVisited => '% av landet besøkt';

  @override
  String get visited => 'besøkt';

  @override
  String get notes => 'Merknader';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Tilpass';

  @override
  String get onlyCountSovereign => 'Bare Tell Suverene Land';

  @override
  String get countUkSeparately => 'Tell Britiske Land separat';

  @override
  String get showLegend => 'Vis Legende';

  @override
  String get showLivedPin => 'Vis Lived Pin';

  @override
  String get useMyColours => 'Bruk mine farger';

  @override
  String get mapColors => 'Kartfarger';

  @override
  String get traveller => 'Den Reisende';

  @override
  String get nightTraveller => 'Nattreisende';

  @override
  String get original => 'Originalen';

  @override
  String get explorer => 'Utforskeren';

  @override
  String get weekender => 'The Weekender';

  @override
  String get naturalist => 'Naturforskeren';

  @override
  String get historian => 'Historikeren';

  @override
  String get thrillSeeker => 'Spenning Søkeren';

  @override
  String get culturalBuff => 'Den Kulturelle Buff';

  @override
  String get myColors => 'Mine Farger';

  @override
  String get experiences => 'Opplevelser';

  @override
  String get done => 'Ferdig';

  @override
  String get experiencesInstructions =>
      'Trykk på + knappen for å komme i gang!';

  @override
  String get continueText => 'Fortsett';

  @override
  String get experiencesDescription => 'Hva liker du å gjøre når du reiser?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Mitt reisekart';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Jeg har sett $percentage% av verden';
  }

  @override
  String get requiresOnline =>
      'Beklager, Visited krever en aktiv nettverkstilkobling. Åpne innstillingsappen din og sørg for at Wi-Fi eller mobildata er aktivert og at flymodus er deaktivert';

  @override
  String get list => 'Liste';

  @override
  String get more => 'Mer';

  @override
  String get myCountrySelections => 'Mine Landvalg';

  @override
  String get cities => 'Byer';

  @override
  String get citiesInstructions =>
      'Trykk på hvilket som helst land for å begynne å velge byer.';

  @override
  String get missingCitiesEmailTitle => 'Manglende byer!';

  @override
  String get lists => 'Lister';

  @override
  String get disputedTerritories => 'Omstridte territorier';

  @override
  String get sponsored => 'Sponset';

  @override
  String get places => 'Steder';

  @override
  String get noListsError =>
      'Opps, ingen lister tilgjengelig for øyeblikket, prøv litt senere';

  @override
  String get noInspirationsError =>
      'Opps, ingen bilder er tilgjengelige akkurat nå, prøv litt senere';

  @override
  String get mostFrequentlyVisitedCountries => 'Dine mest besøkte land:';

  @override
  String get update => 'Oppdater';

  @override
  String get signup => 'Melde deg på';

  @override
  String get loginWallSubtitle =>
      'Opprett en gratis konto for å oppleve den fullstendige versjonen av Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Du vil miste alle valgene dine etter at du har lukket appen.';

  @override
  String get createAccount => 'Opprett konto';

  @override
  String get continueWithoutAccount => 'Fortsett uten en konto';

  @override
  String get inspirationPromotion =>
      'Bli inspirert med vakker reisefotografering';

  @override
  String get saveStatsPromotion => 'Lagre reisestatistikken din!';

  @override
  String get selectRegionsPromotion => 'Velg delstater og provinser';

  @override
  String get experiencesPromotion => 'Spor opplevelser over hele verden';

  @override
  String get missingListItem =>
      'Savnet vi noe? Trykk her for å sende oss en e -post for å få ditt favorittsted lagt til.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Mangler element fra $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Jeg har besøkt $amount $listName';
  }

  @override
  String get orderPoster => 'Plakat';

  @override
  String get shareMap => 'Del kart';

  @override
  String get posterLandingPageTitle => 'Få din plakat';

  @override
  String get posterNotAvailableError =>
      'Plakatinnkjøp er ikke tilgjengelig akkurat nå. Prøv igjen senere.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping frakt';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Om våre tilpassede utskriftskart\nSkriv ut ditt personlige verdenskart. Tilpass den med dine helt egne farger og få den levert rett hjem.\n \n### Spesifikasjoner:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landskapsorientering.\n- Mikroblekk, dråper for presise utskrifter, 8 -bits farge, nesten fotoutskriftskvalitet,\n- 0,22 mm tykt satengpapir\n\n### Fraktdetaljer:\nFrakt fra Toronto, Canada til hvor som helst i verden ved hjelp av Canada Post. Tillat 2 til 4 uker for levering til de fleste destinasjoner. Alle bestillinger sendes rullet opp i en papp -rørboks til leveringsadressen som er sendt inn. All betaling håndteres av Apple Pay, eller Stripe.\n\n\n### Avbestilling/refusjon:\nBestillinger behandles umiddelbart etter å ha blitt sendt inn for raskest mulig snuoperasjon. Derfor er det ingen refusjon/avbestilling tilgjengelig.';

  @override
  String get posterDescriptionMarkdown =>
      '## Om våre tilpassede utskriftskart\nSkriv ut ditt personlige verdenskart. Tilpass den med dine helt egne farger og få den levert rett hjem.\n \n### Spesifikasjoner:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landskapsorientering.\n- Mikroblekk, dråper for presise utskrifter, 8 -bits farge, nesten fotoutskriftskvalitet,\n- 0,22 mm tykt satengpapir\n\n### Fraktdetaljer:\nFrakt fra Toronto, Canada til hvor som helst i verden ved hjelp av Canada Post. Tillat 2 til 4 uker for levering til de fleste destinasjoner. Alle bestillinger sendes rullet opp i en papp -rørboks til leveringsadressen som er sendt inn. All betaling håndteres av Google Pay eller Stripe.\n\n\n### Avbestilling/refusjon:\nBestillinger behandles umiddelbart etter å ha blitt sendt inn for raskest mulig snuoperasjon. Derfor er det ingen refusjon/avbestilling tilgjengelig.';

  @override
  String get posterCustomizeTitle => 'Tilpass plakaten';

  @override
  String get enterShippingAddress => 'Skriv inn leveringsadresse';

  @override
  String get price => 'Pris';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + skatt';
  }

  @override
  String get showSelections => 'Vis utvalg';

  @override
  String get posterNoRefunds =>
      'Ingen refusjoner er tilgjengelige etter at plakaten din er skrevet ut.';

  @override
  String get posterReviewOrder => 'Se over bestillingen din';

  @override
  String get email => 'E -post';

  @override
  String get emailEmptyError => 'Vennligst skriv inn e -posten din';

  @override
  String get fullName => 'Fullt navn';

  @override
  String get fullNameEmptyError => 'Vennligst oppgi ditt fulle navn';

  @override
  String get streetAddressEmptyError => 'Vennligst skriv inn gateadressen din';

  @override
  String get cityEmptyError => 'Vennligst skriv inn byen din';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Vennligst skriv inn $fieldName';
  }

  @override
  String get country => 'Land';

  @override
  String get countryEmptyError => 'Vennligst skriv inn landet ditt';

  @override
  String get posterReviewOrderTitle => 'Se over bestillingen din';

  @override
  String get buyNow => 'Kjøp nå';

  @override
  String get secureCheckoutDisclaimer => 'Sikker kasse levert av Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Avgift';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Tilpasset besøkte kartplakat';

  @override
  String get shipping => 'Shipping';

  @override
  String get posterOrderReceivedTitle => 'Ordre mottatt';

  @override
  String get posterOrderReceivedSubtitle => 'Vi mottok bestillingen din!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Sjekk e-posten din for flere oppdateringer. \nDet kan ta opptil 4 uker før plakaten din kommer. \nHvis du har spørsmål, vennligst send oss ​​en e-post på [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Trykt plakatordre status';

  @override
  String get moreInfo => 'Mer informasjon';

  @override
  String get logoutConfirm => 'Vil du logge ut av appen?';

  @override
  String get emailNotAvailable => 'Den e -posten er tatt.';

  @override
  String get alphabetical => 'Alfabetisk';

  @override
  String get firstTimeLiveTutorial =>
      'Å tilby hjemlandet og byen vil tilpasse appopplevelsen din.';

  @override
  String get firstTimeBeenTutorial =>
      'Hvis du velger hvor du har vært, kan du se kartet ditt over alle landene du har vært og se personlig statistikk.';

  @override
  String get progressTooltipGoal =>
      'Reisemålene dine er basert på antall land du \"vil\" reise sammenlignet med land der du har vært';

  @override
  String get progressTooltipRank =>
      'Dette tallet viser hvordan du rangerer sammenlignet med reisende rundt om i verden. Du kan øke rangeringen din ved å reise til flere land.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Denne grafen er basert på antall land du har vært i forhold til totale land i verden.';

  @override
  String get sortBy => 'Sorter etter';

  @override
  String get updateWishlist => 'Oppdater ønskeliste';

  @override
  String get mapInfo =>
      'Klikk på landet for å velge, vil ha eller leve. Du kan også klikke på ikonet som finnes i øverste venstre hjørne for listevisningen.';

  @override
  String get oneTimePurchase => 'Alt er et engangskjøp!';

  @override
  String get contact => 'Kontakt';

  @override
  String get contactUs => 'Kontakt oss';

  @override
  String get noCitiesSelected => 'Du har ikke valgt noen byer, men ...';

  @override
  String get updateTravelGoal => 'Oppdater reisemål';

  @override
  String get travelGoalComplete =>
      'Gratulerer! \n\nyou har fullført reisemålet ditt! \n\ntap + -knappen for å legge til flere land.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Det er ingen konto tilknyttet e -posten $email. Vil du lage det nå?';
  }

  @override
  String get tryAgain => 'Prøv igjen';

  @override
  String get itineraries => 'Reiseplaner';

  @override
  String get itinerary => 'Reiseplan';

  @override
  String get place => 'Steder';

  @override
  String get itinerariesDescription =>
      'Dette er steder du har uttrykt interesse for.\nBruk denne guiden til å planlegge din neste ferie.';

  @override
  String get addMore => 'Legg til mer';

  @override
  String get interests => 'Interesser';

  @override
  String get selection => 'Utvalg';

  @override
  String get goal => 'Mål';

  @override
  String get noItineraries => 'Ingen Reiseruter';

  @override
  String get noItinerariesExplanation =>
      'Legg til noen steder, inspirasjoner eller opplevelser for å se hvordan reiserutene dine genereres automatisk.';

  @override
  String get clusterPins => 'Grupper Kartmarkører';

  @override
  String get toggleRegions => 'Vis regioner ved zooming';

  @override
  String get mapProjection => 'Kartprojeksjon';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Ekvirektangulær';

  @override
  String get yourTravellerType => 'Din reisende type:';

  @override
  String get yourHotelPreferences => 'Dine hotellpreferanser:';

  @override
  String get budget => 'Budsjett';

  @override
  String get midScale => 'Middels';

  @override
  String get luxury => 'Luksus';

  @override
  String get noTravellerType =>
      'Legg til elementer i ønskelisten din for å oppdage hva slags reisende du er.';

  @override
  String get unlockLived => 'Lås Opp Bodde';

  @override
  String get unlockLivedDescription =>
      'Velg hvor du tidligere har bodd på kartet!';

  @override
  String get futureFeaturesDescription => '...og alle fremtidige funksjoner';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Ditt mest besøkte land:';

  @override
  String get departureDate => 'Avreisedato';

  @override
  String get returnDate => 'Returdato';

  @override
  String get hotels => 'Hoteller';

  @override
  String get food => 'Mat';

  @override
  String get travelDates => 'Reisedatoer';

  @override
  String get posterForMe => 'For meg';

  @override
  String get posterSendGift => 'Send en gave';

  @override
  String get addSelections => 'Legg til valg';

  @override
  String get posterType => 'Postertype';

  @override
  String get help => 'Hjelp';

  @override
  String get tutorialMap =>
      'Trykk på et land for å velge: vært, ønsket og levd.';

  @override
  String get tutorialMapList =>
      'Trykk på listeikonet (øverst i venstre hjørne) for å velge etter liste.';

  @override
  String get tutorialCountryDetails =>
      'Trykk på landet og deretter \"mer\" for å velge etter region.';

  @override
  String get tutorialItems =>
      'Skyv bryteren for å velge hvordan du vil velge elementer.';

  @override
  String get tutorialInspirations =>
      'Sveip til høyre eller venstre for å gå til neste kort.';

  @override
  String get lifetime => 'Livstid';

  @override
  String get chooseYourPlan => 'Velg din plan';

  @override
  String get requestARefund => 'Be om refusjon';

  @override
  String get noPurchasesFound => 'Ingen kjøp funnet.';

  @override
  String get noProductsAvailable => 'Ingen produkter tilgjengelige';

  @override
  String get posterLandingAppBar => 'Ta med historiene dine hjem';

  @override
  String get posterLandingSubHeading => 'Din reise, din historie';

  @override
  String get posterLandingSubDescription =>
      'Reisene dine er mer enn turer, de er historier, minner og milepæler. Gjør disse uforglemmelige øyeblikkene til et personlig verdenskart som er like unikt som dine eventyr.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Et kart over dine prestasjoner: Fremhev alle destinasjoner, fra din første store tur til ditt mest dristige eventyr.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Feir hver reise: Gjenopplev reisene dine daglig med en vakkert utformet plakat designet for å inspirere.';

  @override
  String get posterLandingPromoBullet3 =>
      '• En gave de vil verdsette: Overrask en medreisende med et tilpasset kart som viser reisen deres, perfekt for bursdager, milepæler eller bare fordi.';

  @override
  String get posterLandingHowItWorks => 'Hvordan det fungerer!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Tilpass designet ditt: Velg farger, stiler og merk reisene dine (eller deres!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Forhåndsvis kartet ditt: Se det komme til live før bestillingen din.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Trygg betaling: Rask og sikker med Apple Pay eller Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Trygg betaling: Rask og sikker med Google Pay eller Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Klar for visning: Vi sender den rett til døren din (eller deres).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Erfaringer fra andre reisende';

  @override
  String get posterLandingCustomerReview1 =>
      '«Dette kartet er en flott måte å holde oversikt over hvor jeg har reist og planlegge våre fremtidige turer.  Kvaliteten er solid, og det ser fantastisk ut på kontoret mitt.  Jeg kjøpte til og med et til broren min, og han kunne ikke slutte å snakke om hvor kult det er!» - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '«Jeg har reist til over 150 havner mens jeg har jobbet på cruise. Dette kartet er et flott tillegg til stuen min som et minne om alle årene til sjøs.» - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      '«Flott gave til morsdagen. Moren min ble veldig rørt!» Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '«Trykte ut et kart over steder jeg ønsket å besøke sammen med kjæresten min. Det var en flott julegave. Høy kvalitet også.» Brad J.';

  @override
  String get posterLandingSpecifications => 'Spesifikasjoner';

  @override
  String get posterLandingSpecification1 =>
      '- Dimensjoner 16« x 20» (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '- Orientering: Liggende';

  @override
  String get posterLandingSpecification3 =>
      '- Utskriftskvalitet: Mikroblekk, dråper for presise utskrifter.  8-biters farger, nesten fotoutskriftskvalitet.';

  @override
  String get posterLandingSpecification4 => '- Papir 0,22 mm tykt satinpapir';

  @override
  String get posterLandingShippingHeader => 'Leveringsdetaljer';

  @override
  String get posterLandingShipping1 =>
      '- Frakt fra Toronto i Canada til hvor som helst i verden med Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '- Beregn 2-4 ukers leveringstid til de fleste destinasjoner.';

  @override
  String get posterLandingShipping3 =>
      '- Alle bestillinger rulles sammen i en pappeske til leveringsadressen du oppgir.';

  @override
  String get posterLandingCancellationHeader => 'Avbestilling/refusjon:';

  @override
  String get posterLandingCancellationBody =>
      'Refusjoner er tilgjengelige før plakaten er sendt til trykkeriet, noe som kan ta opptil 24 timer.  Etter at bestillingen er behandlet, er ingen refusjon/avbestilling tilgjengelig.  Du vil motta en e-post når bestillingen din er trykt.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
