// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swedish (`sv`).
class AppLocalizationsSv extends AppLocalizations {
  AppLocalizationsSv([String locale = 'sv']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Språk';

  @override
  String get pickEmailApp => 'Välj din e-postapp';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Jag har besökt $amount länder! Hur många har du besökt? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Jag har besökt $amount städer! Hur många har du besökt? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Jag har besökt $amount $listName! Hur många har du besökt? www.visitedapp.com';
  }

  @override
  String get clear => 'Klar';

  @override
  String get been => 'Har varit';

  @override
  String get want => 'Vill';

  @override
  String get live => 'Live';

  @override
  String get lived => 'Bodde';

  @override
  String get water => 'Vatten';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Gränser';

  @override
  String get labels => 'Etiketter';

  @override
  String get legend => 'Legend';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirationer';

  @override
  String get delete => 'Radera';

  @override
  String get unlockVisitedUpsellTitle => 'Vill du se mer?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Lås upp alla funktioner och njut av Visited i full styrka';

  @override
  String get checkTheDetails => 'Kontrollera detaljerna';

  @override
  String get moreInspirationsComingSoon =>
      'Vi arbetar med att få fler bilder. Kom tillbaka snart!';

  @override
  String get unlockPremiumFeatures => 'Lås upp premiumfunktioner';

  @override
  String get purchased => 'Köpt!';

  @override
  String get buy => 'Köpa';

  @override
  String get restorePurchases => 'Återställ köp';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Är du säker?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Att radera detta kort är permanent. Det finns inget sätt att återställa den här bilden.';

  @override
  String get cancel => 'Avbryt';

  @override
  String get map => 'Karta';

  @override
  String get progress => 'Framsteg';

  @override
  String get myTravelGoal => 'Mitt resmål';

  @override
  String goalRemaining(int remaining) {
    return '$remaining mer att gå!';
  }

  @override
  String get top => 'TOPP';

  @override
  String get ofTheWorld => 'i världen!';

  @override
  String get countries => 'länder';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Toppländerna besökta från $country:';
  }

  @override
  String get login => 'Logga in';

  @override
  String get logout => 'Logga ut';

  @override
  String get enterYourEmail => 'Skriv in din e-postadress';

  @override
  String get privacyPolicy => 'Integritetspolicy';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-swedish/';

  @override
  String get termsOfUse => 'Användarvillkor';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-swedish/';

  @override
  String get errorTitle => 'Oj då!';

  @override
  String get enterValidEmail => 'Ange en giltig e-postadress';

  @override
  String get settings => 'Inställningar';

  @override
  String get whereDoYouLive => 'Var bor du?';

  @override
  String get whereHaveYouBeen => 'Var har du varit?';

  @override
  String get whereDoYouFlyFrom => 'Var flyger du ifrån?';

  @override
  String get next => 'Nästa';

  @override
  String get missingAirports =>
      'Ser du inte vad du letar efter? Skicka oss ett e-postmeddelande på <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Saknade flygplatser!';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => 'Välkommen till Visited';

  @override
  String get welcomeSubtitle => 'Livets äventyr väntar';

  @override
  String get getStarted => 'Kom igång';

  @override
  String get privacyAgreement => 'Sekretessavtal';

  @override
  String get privacyAgreementSubtitle =>
      'Godkänn följande artiklar innan du fortsätter att använda Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Genom att kryssa i den här rutan bekräftar du att du har läst och accepterar att vara bunden av Arriving in High Heels \'[Integritetspolicy] (https://www.arrivinginhighheels.com/privacy-policy) och [Användarvillkor] (http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Jag godkänner att ta emot elektroniska meddelanden från Arriving in High Heels som innehåller information och erbjudanden med avseende på produkter, applikationer och tjänster som kan vara av intresse för mig, inklusive meddelande om försäljning, kampanjer, erbjudanden och nyhetsbrev. Jag kan när som helst återkalla detta samtycke enligt beskrivningen i sekretesspolicyn eller genom att klicka på länken \"avregistrera\" i de elektroniska meddelandena.';

  @override
  String get submit => 'Skicka in';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Du måste godkänna båda våra villkor och välja att fortsätta använda Visited.';

  @override
  String get deleteAccount => 'Radera konto';

  @override
  String get removeAdsUpsell =>
      'Vill du välja bort annonser och avsluta prenumerationen på e-postmarknadsföring istället?';

  @override
  String get deleteAccountWarning =>
      'Om du tar bort ditt konto tas all din information bort från våra servrar.\n \n Denna process kan inte ångras.';

  @override
  String get about => 'Om';

  @override
  String get popularity => 'Popularitet';

  @override
  String get regions => 'Regioner';

  @override
  String get population => 'Befolkning';

  @override
  String get size => 'Storlek';

  @override
  String get coverage => 'Yta';

  @override
  String get percentOfCountryVisited => '% av besökta länder';

  @override
  String get visited => 'besökt';

  @override
  String get notes => 'Anteckningar';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Anpassa';

  @override
  String get onlyCountSovereign => 'Räkna endast suveräna länder';

  @override
  String get countUkSeparately => 'Räkna länder i Storbritannien separat';

  @override
  String get showLegend => 'Visa legend';

  @override
  String get showLivedPin => 'Visa levd stift';

  @override
  String get useMyColours => 'Använd Mina färger';

  @override
  String get mapColors => 'Kartfärger';

  @override
  String get traveller => 'Resenären';

  @override
  String get nightTraveller => 'Nattresenären';

  @override
  String get original => 'Originalet';

  @override
  String get explorer => 'Utforskaren';

  @override
  String get weekender => 'Helgfiraren';

  @override
  String get naturalist => 'Naturforskaren';

  @override
  String get historian => 'Historikern';

  @override
  String get thrillSeeker => 'Äventyraren';

  @override
  String get culturalBuff => 'Kulturstarke';

  @override
  String get myColors => 'Mina färger';

  @override
  String get experiences => 'Upplevelser';

  @override
  String get done => 'Klar';

  @override
  String get experiencesInstructions =>
      'Tryck på +-knappen för att komma igång!';

  @override
  String get continueText => 'Fortsätt';

  @override
  String get experiencesDescription => 'Vad gillar du att göra när du reser?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Min resekarta';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Jag har sett $percentage% av världen';
  }

  @override
  String get requiresOnline =>
      'Tyvärr, Visited kräver en aktiv nätverksanslutning. Öppna din inställningsapp och se till att antingen Wi-Fi eller mobildata är aktiverat och att flygplansläge är inaktiverat';

  @override
  String get list => 'Lista';

  @override
  String get more => 'Mer';

  @override
  String get myCountrySelections => 'Mina landval';

  @override
  String get cities => 'Städer';

  @override
  String get citiesInstructions =>
      'Klicka på vilket land som helst för att börja välja städer.';

  @override
  String get missingCitiesEmailTitle => 'Saknade städer!';

  @override
  String get lists => 'Listor';

  @override
  String get disputedTerritories => 'Omstridda territorier';

  @override
  String get sponsored => 'Sponsrad';

  @override
  String get places => 'Platser';

  @override
  String get noListsError =>
      'Opps, inga listor tillgängliga just nu, försök lite senare';

  @override
  String get noInspirationsError =>
      'Opps, inga foton är tillgängliga just nu, försök lite senare';

  @override
  String get mostFrequentlyVisitedCountries => 'Dina mest besökta länder:';

  @override
  String get update => 'Uppdatering';

  @override
  String get signup => 'Bli Medlem';

  @override
  String get loginWallSubtitle =>
      'Skapa ett gratis konto för att uppleva den fullständiga versionen av Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Du kommer att förlora alla dina val efter att du har stängt appen.';

  @override
  String get createAccount => 'Skapa konto';

  @override
  String get continueWithoutAccount => 'Fortsätt utan ett konto';

  @override
  String get inspirationPromotion =>
      'Bli inspirerad med vacker resefotografering';

  @override
  String get saveStatsPromotion => 'Spara din resestatistik!';

  @override
  String get selectRegionsPromotion => 'Välj stater och provinser';

  @override
  String get experiencesPromotion => 'Spåra upplevelser över hela världen';

  @override
  String get missingListItem =>
      'Missade vi något? Klicka här för att skicka ett e -postmeddelande för att få din favoritplats tillagd.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Saknas objekt från $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Jag har besökt $amount $listName';
  }

  @override
  String get orderPoster => 'Affisch';

  @override
  String get shareMap => 'Aktiekarta';

  @override
  String get posterLandingPageTitle => 'Få din affisch';

  @override
  String get posterNotAvailableError =>
      'Affischköp är inte tillgängligt just nu. Vänligen försök igen senare.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping frakt';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Om våra anpassade utskriftskartor\nSkriv ut din personliga världskarta. Anpassa den med dina egna färger och få den levererad direkt till ditt hem.\n \n### Specifikationer:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landskapsorientering.\n- Mikrobläck, droppar för exakta tryck, 8 bitars färg, nästan fototryckskvalitet,\n- 0,22 mm tjockt satinpapper\n\n### Frakt detaljer:\nFrakt från Toronto, Kanada till var som helst i världen med Canada Post. Tillåt 2 till 4 veckor för leverans till de flesta destinationer. Alla beställningar skickas upp i en kartongrörslåda till den skickade leveransadressen. All betalning hanteras av Apple Pay, eller Stripe.\n\n\n### Avbokning/återbetalning:\nBeställningar behandlas omedelbart efter att de lämnats in för den snabbaste möjliga vändningen. Därför finns det ingen återbetalning/avbokning tillgänglig.';

  @override
  String get posterDescriptionMarkdown =>
      '## Om våra anpassade utskriftskartor\nSkriv ut din personliga världskarta. Anpassa den med dina egna färger och få den levererad direkt till ditt hem.\n \n### Specifikationer:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landskapsorientering.\n- Mikrobläck, droppar för exakta tryck, 8 bitars färg, nästan fototryckskvalitet,\n- 0,22 mm tjockt satinpapper\n\n### Frakt detaljer:\nFrakt från Toronto, Kanada till var som helst i världen med Canada Post. Tillåt 2 till 4 veckor för leverans till de flesta destinationer. Alla beställningar skickas upp i en kartongrörslåda till den skickade leveransadressen. All betalning hanteras av Google Pay eller Stripe.\n\n\n### Avbokning/återbetalning:\nBeställningar behandlas omedelbart efter att de lämnats in för den snabbaste möjliga vändningen. Därför finns det ingen återbetalning/avbokning tillgänglig.';

  @override
  String get posterCustomizeTitle => 'Anpassa affisch';

  @override
  String get enterShippingAddress => 'Ange leveransadress';

  @override
  String get price => 'Pris';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + skatt';
  }

  @override
  String get showSelections => 'Visning';

  @override
  String get posterNoRefunds =>
      'Inga återbetalningar finns tillgängliga efter att din affisch har skrivits ut.';

  @override
  String get posterReviewOrder => 'Granska din beställning';

  @override
  String get email => 'E-post';

  @override
  String get emailEmptyError => 'Ange din e -post';

  @override
  String get fullName => 'Fullständiga namn';

  @override
  String get fullNameEmptyError => 'Vänligen fyll i ditt fullständiga namn';

  @override
  String get streetAddressEmptyError => 'Ange din gatuadress';

  @override
  String get cityEmptyError => 'Ange din stad';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Ange ditt $fieldName';
  }

  @override
  String get country => 'Land';

  @override
  String get countryEmptyError => 'Ange ditt land';

  @override
  String get posterReviewOrderTitle => 'Granska din beställning';

  @override
  String get buyNow => 'Köp nu';

  @override
  String get secureCheckoutDisclaimer =>
      'Säker kassan tillhandahålls av Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Beskatta';

  @override
  String get subtotal => 'Delsumma';

  @override
  String get posterProductName => 'Anpassad besökta kartplakat';

  @override
  String get shipping => 'Frakt';

  @override
  String get posterOrderReceivedTitle => 'Order mottagna';

  @override
  String get posterOrderReceivedSubtitle => 'Vi fick din beställning!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Kontrollera din e -post för fler uppdateringar. \nLåt upp till fyra veckor för att din affisch kommer. \nOm du har några frågor, vänligen maila oss på [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Tryckt affischorderstatus';

  @override
  String get moreInfo => 'Mer information';

  @override
  String get logoutConfirm => 'Vill du logga ut från appen?';

  @override
  String get emailNotAvailable => 'Det e -postmeddelandet har tagits.';

  @override
  String get alphabetical => 'Alfabetisk';

  @override
  String get firstTimeLiveTutorial =>
      'Om du anger ditt hemland och din stad blir din upplevelse av appen mer personlig.';

  @override
  String get firstTimeBeenTutorial =>
      'Om du väljer var du har varit kan du visa kartan med alla länder du har varit i och se personlig statistik.';

  @override
  String get progressTooltipGoal =>
      'Dina resmål baseras på antalet länder du \"vill\" resa till jämfört med länder där du har \"varit\".';

  @override
  String get progressTooltipRank =>
      'Det här antalet visar hur du rankas jämfört med resenärer runt om i världen.  Du kan öka din ranking genom att resa till fler länder.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Den här grafen är baserad på antalet länder du har varit i jämfört med antalet länder i världen.';

  @override
  String get sortBy => 'Sortera efter';

  @override
  String get updateWishlist => 'Uppdatera önskelistan';

  @override
  String get mapInfo =>
      'Klicka på landet för att välja varit, vill eller bor. Du kan också klicka på ikonen som finns i det övre vänstra hörnet för att få en listvy.';

  @override
  String get oneTimePurchase => 'Allt är ett engångsköp!';

  @override
  String get contact => 'Kontakta';

  @override
  String get contactUs => 'Kontakta oss';

  @override
  String get noCitiesSelected => 'Du har inte valt några städer ännu...';

  @override
  String get updateTravelGoal => 'Uppdatera resemålet';

  @override
  String get travelGoalComplete =>
      'Grattis! \\ n \\ Nyou har slutfört ditt resmål! \n\ntap + -knappen för att lägga till fler länder.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Det finns inget konto associerat e -postmeddelandet $email. Vill du skapa det nu?';
  }

  @override
  String get tryAgain => 'Försök igen';

  @override
  String get itineraries => 'Reseplaner';

  @override
  String get itinerary => 'Reseplan';

  @override
  String get place => 'Plats';

  @override
  String get itinerariesDescription =>
      'Det här är platser som du har uttryckt intresse för.\nAnvänd den här guiden för att planera din nästa semester.';

  @override
  String get addMore => 'Lägg till mer';

  @override
  String get interests => 'Intressen';

  @override
  String get selection => 'Urval';

  @override
  String get goal => 'Mål';

  @override
  String get noItineraries => 'Inga Resplaner';

  @override
  String get noItinerariesExplanation =>
      'Lägg till några platser, inspirationer eller upplevelser för att se dina resplaner generera automatiskt.';

  @override
  String get clusterPins => 'Klusterkarta Markörer';

  @override
  String get toggleRegions => 'Visa regioner vid zoomning';

  @override
  String get mapProjection => 'Kartprojektion';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Likformig';

  @override
  String get yourTravellerType => 'Din resenärstyp:';

  @override
  String get yourHotelPreferences => 'Dina hotellpreferenser:';

  @override
  String get budget => 'Budget';

  @override
  String get midScale => 'Mellansegment';

  @override
  String get luxury => 'Lyx';

  @override
  String get noTravellerType =>
      'Lägg till artiklar i din önskelista för att upptäcka vilken typ av resenär du är.';

  @override
  String get unlockLived => 'Lås Upp Levda';

  @override
  String get unlockLivedDescription =>
      'Välj var du har bott tidigare på kartan!';

  @override
  String get futureFeaturesDescription => '...och alla framtida funktioner';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Ditt mest besökta land:';

  @override
  String get departureDate => 'Avresedatum';

  @override
  String get returnDate => 'Returdatum';

  @override
  String get hotels => 'Hotell';

  @override
  String get food => 'Mat';

  @override
  String get travelDates => 'Resedatum';

  @override
  String get posterForMe => 'För mig';

  @override
  String get posterSendGift => 'Skicka en present';

  @override
  String get addSelections => 'Lägg till val';

  @override
  String get posterType => 'Postertyp';

  @override
  String get help => 'Hjälp';

  @override
  String get tutorialMap =>
      'Tryck på ett land för att välja: varit, önskat och levt.';

  @override
  String get tutorialMapList =>
      'Klicka på listikonen (övre vänstra hörnet) för att välja efter lista.';

  @override
  String get tutorialCountryDetails =>
      'Tryck på landet och sedan på \"mer\" för att välja efter region.';

  @override
  String get tutorialItems =>
      'Skjut reglaget för att välja hur du vill välja objekt.';

  @override
  String get tutorialInspirations =>
      'Svep åt höger eller vänster för att flytta till nästa kort.';

  @override
  String get lifetime => 'Livstid';

  @override
  String get chooseYourPlan => 'Välj din plan';

  @override
  String get requestARefund => 'Begär återbetalning';

  @override
  String get noPurchasesFound => 'Inga köp hittades.';

  @override
  String get noProductsAvailable => 'Inga produkter tillgängliga';

  @override
  String get posterLandingAppBar => 'Ta hem dina berättelser';

  @override
  String get posterLandingSubHeading => 'Din resa, din berättelse';

  @override
  String get posterLandingSubDescription =>
      'Dina resor är mer än resor, de är berättelser, minnen och milstolpar. Förvandla dessa oförglömliga ögonblick till en personlig världskarta som är lika unik som dina äventyr.';

  @override
  String get posterLandingPromoBullet1 =>
      '• En karta över dina prestationer: Markera varje destination, från din första stora resa till ditt mest vågade äventyr.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Fira varje resa: Återupplev dina resor dagligen med en vackert utformad postere designad för att inspirera.';

  @override
  String get posterLandingPromoBullet3 =>
      '• En gåva de kommer att uppskatta: Överraska en medresenär med en anpassad karta som visar deras resa, perfekt för födelsedagar, milstolpar eller bara därför.';

  @override
  String get posterLandingHowItWorks => 'Hur det fungerar!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Anpassa din design: Välj färger, stilar och markera dina resor (eller deras!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Förhandsgranska din karta: Se den komma till liv innan din beställning.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Säker betalning: Snabb och säker med Apple Pay eller Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Säker betalning: Snabb och säker med Google Pay eller Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Klar för visning: Vi skickar den direkt till din dörr (eller deras).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Erfarenheter från andra resenärer';

  @override
  String get posterLandingCustomerReview1 =>
      '”Den här kartan är ett bra sätt att hålla reda på vart jag har rest och planera våra framtida resor.  Kvaliteten är gedigen och den ser fantastisk ut på mitt kontor.  Jag köpte till och med en till min bror och han kunde inte sluta prata om hur cool den är!” - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '”Jag har besökt över 150 hamnar när jag arbetat med kryssningar. Den här kartan är ett utmärkt tillskott till mitt vardagsrum som ett minne av alla år till sjöss.” - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '”Bra present till mors dag. Min mamma blev väldigt rörd!” Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '”Skrev ut en karta över platser som jag ville besöka med min gf. Det var en fantastisk julklapp. Hög kvalitet också.” Brad J.';

  @override
  String get posterLandingSpecifications => 'Specifikationer';

  @override
  String get posterLandingSpecification1 =>
      '- Dimensioner: 16” x 20” (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '- Orientering: Landskap';

  @override
  String get posterLandingSpecification3 =>
      '- Utskriftskvalitet: Mikrobläck, droppar för exakta utskrifter. 8-bitars färg, nästan fotografisk utskriftskvalitet.';

  @override
  String get posterLandingSpecification4 =>
      '- Papper: 0,22 mm tjockt satinpapper';

  @override
  String get posterLandingShippingHeader => 'Leveransinformation';

  @override
  String get posterLandingShipping1 =>
      '- Frakt från Toronto, Kanada till var som helst i världen med Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '- Räkna med 2-4 veckors leveranstid till de flesta destinationer.';

  @override
  String get posterLandingShipping3 =>
      '- Alla beställningar rullas upp i en kartonglåda till den leveransadress du anger.';

  @override
  String get posterLandingCancellationHeader => 'Avbeställning/återbetalning:';

  @override
  String get posterLandingCancellationBody =>
      'Återbetalning kan ske innan din poster har skickats till tryckeriet, vilket kan ta upp till 24 timmar.  Efter att din beställning har behandlats är ingen återbetalning/annullering möjlig.  Du kommer att få ett e-postmeddelande när din beställning har tryckts.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
