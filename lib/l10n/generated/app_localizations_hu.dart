// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hungarian (`hu`).
class AppLocalizationsHu extends AppLocalizations {
  AppLocalizationsHu([String locale = 'hu']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Nyelv';

  @override
  String get pickEmailApp => 'Válassza ki az e-mail alkalmazását';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return '$amount országot látogattam meg! Te hányat látogattál meg? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return '$amount várost látogattam meg! Te hányat látogattál meg? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return '$amount $listName helyet látogattam meg! Te hányat látogattál meg? www.visitedapp.com';
  }

  @override
  String get clear => 'Nullázás';

  @override
  String get been => 'Volt';

  @override
  String get want => 'Szeretném';

  @override
  String get live => 'Jelenlegi';

  @override
  String get lived => 'Éltem';

  @override
  String get water => 'Víz';

  @override
  String get land => 'Szárazföld';

  @override
  String get borders => 'Határok';

  @override
  String get labels => 'Cetlik';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inspiráció';

  @override
  String get inspirations => 'Inspirációk';

  @override
  String get delete => 'Törtlés';

  @override
  String get unlockVisitedUpsellTitle => 'Szeretne többet látni?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Oldja fel az összes funkciót és élvezze a Visited-ot teljes valójában';

  @override
  String get checkTheDetails => 'Nézze meg a részleteket';

  @override
  String get moreInspirationsComingSoon =>
      'Dolgozunk a képek beszerzésén. Jöjjön vissza később!';

  @override
  String get unlockPremiumFeatures => 'Oldja fel a prémium funkiciókat';

  @override
  String get purchased => 'Megvéve!';

  @override
  String get buy => 'Vétel';

  @override
  String get restorePurchases => 'Visszatérítés';

  @override
  String get ok => 'Rendben';

  @override
  String get areYouSure => 'Biztos benne?';

  @override
  String get deleteInspirationConfirmMessage =>
      'E kártya kitörtlése végleges. Nincs mód a kép visszaszerzésére.';

  @override
  String get cancel => 'Mégse';

  @override
  String get map => 'Térkép';

  @override
  String get progress => 'Haladás';

  @override
  String get myTravelGoal => 'Az utazási célom';

  @override
  String goalRemaining(int remaining) {
    return '$remaining még ennyi kell!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'A világról!';

  @override
  String get countries => 'országok';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return '$country-ból legtöbbet látogatott országok';
  }

  @override
  String get login => 'Bejelentkezés';

  @override
  String get logout => 'Kijelentkezés';

  @override
  String get enterYourEmail => 'Adja meg az ímél címét';

  @override
  String get privacyPolicy => 'Adatvédelmi Irányelvek';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-hungarian/';

  @override
  String get termsOfUse => 'Felhasználási Feltételek';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-hungarian/';

  @override
  String get errorTitle => 'Hoppá!';

  @override
  String get enterValidEmail => 'Kérjük érvényem ímél címet adjon meg';

  @override
  String get settings => 'Beállítások';

  @override
  String get whereDoYouLive => 'Hol él?';

  @override
  String get whereHaveYouBeen => 'Hol járt már?';

  @override
  String get whereDoYouFlyFrom => 'Honnan repül ki?';

  @override
  String get next => 'Következő';

  @override
  String get missingAirports =>
      'Nem találja amit keres? Küldjön egy ímé<NAME_EMAIL> ímél címre';

  @override
  String get missingAirportsEmailTitle => 'Hiányzó Repterek!';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => 'Üdvözöljük a Visited-ban';

  @override
  String get welcomeSubtitle => 'Egy ételre szóló kaland várja';

  @override
  String get getStarted => 'Kezdjen bele';

  @override
  String get privacyAgreement => 'Adatvédelmi Megállípodás';

  @override
  String get privacyAgreementSubtitle =>
      'Kérjük, fogadja el a következő elemeket, mielőtt folytatná a Visited használatát.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Ennek a négyzetnek a bepipálásával tudomásul veszi, és elfogadja, hogy a Arriving in High Heels kötelezi Önt [Adatvédelmi irányelvek] (https://www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Beleegyezem, hogy a Arriving High Heels-től érkező elektronikus üzeneteket kapjak, amelyek információkat és ajánlatokat tartalmaznak a számomra érdekes termékekről, alkalmazásokról és szolgáltatásokról, beleértve az értékesítésről szóló értesítést, akciókat, ajánlatokat és hírlevelet. Ezt a hozzájárulást bármikor visszavonhatom az Adatvédelmi irányelvekben leírtak szerint, vagy az elektronikus üzenetek „leiratkozás” linkjére kattintva.';

  @override
  String get submit => 'Küldés';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'El kell fogadnia mindkét feltételt és döntést, hogy tovább használhass a Visited-ot';

  @override
  String get deleteAccount => 'Felhasználó törlése';

  @override
  String get removeAdsUpsell =>
      'Szeretne leiratkozni a hirdetésekről, és íméles marketingről?';

  @override
  String get deleteAccountWarning =>
      'A felhasználója törtésével minden információ kitörtlődik a szerverünkről. Ezt a folyamatot nem lehet visszavonni.';

  @override
  String get about => 'Tudjon meg többet';

  @override
  String get popularity => 'Népszerűség';

  @override
  String get regions => 'Régiók';

  @override
  String get population => 'Népessék';

  @override
  String get size => 'Méret';

  @override
  String get coverage => 'Átfedés';

  @override
  String get percentOfCountryVisited => 'Az országok %-a meglátogatva';

  @override
  String get visited => 'Meglátogatva';

  @override
  String get notes => 'Jegyzetek';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Személyre szabás';

  @override
  String get onlyCountSovereign => 'Csak a független államok beszámítása';

  @override
  String get countUkSeparately =>
      'Az Egyesült Királyság országainak külön beszámítása';

  @override
  String get showLegend => 'Legenda Mutatása';

  @override
  String get showLivedPin => 'Jelenlegi tűzők mutatása';

  @override
  String get useMyColours => 'Az én színeim használata';

  @override
  String get mapColors => 'Térkép színei';

  @override
  String get traveller => 'Az utazó';

  @override
  String get nightTraveller => 'Az Éjszakai utazó';

  @override
  String get original => 'Az Eredeti';

  @override
  String get explorer => 'A Felfedező';

  @override
  String get weekender => 'A Hétvégés';

  @override
  String get naturalist => 'A Természettudós';

  @override
  String get historian => 'A Történész';

  @override
  String get thrillSeeker => 'Az IzgalomKereső';

  @override
  String get culturalBuff => 'A MűvelődésiGorilla';

  @override
  String get myColors => 'Az én színeim';

  @override
  String get experiences => 'Tapasztalatok';

  @override
  String get done => 'Kész';

  @override
  String get experiencesInstructions => 'A kezdéshez nyomja meg a + gombot!';

  @override
  String get continueText => 'Tovább';

  @override
  String get experiencesDescription => 'Mit szeret csinálni az utazása során?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Az én utazási térképem';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Már a világ $percentage%-át láttam';
  }

  @override
  String get requiresOnline =>
      'Sajnáljuk, Visited-hoz aktív internetkapcsolat szükséges. Kérjük nyissa meg a beállításokat és nézze meg, hogy a wifi be van-e kapcsolva a repülő üzemmód pedig ki.';

  @override
  String get list => 'Lista';

  @override
  String get more => 'Több';

  @override
  String get myCountrySelections => 'A Kiválasztott Országaim';

  @override
  String get cities => 'Városok';

  @override
  String get citiesInstructions =>
      'Koppintson az egy városra, hogy elkezdje a városok kiválasztásán';

  @override
  String get missingCitiesEmailTitle => 'Hiányzó Városok!';

  @override
  String get lists => 'Listák';

  @override
  String get disputedTerritories => 'Vitatott területek';

  @override
  String get sponsored => 'Szponzorált';

  @override
  String get places => 'Helyek';

  @override
  String get noListsError =>
      'Hoppá, jelenleg nincs elérhető lista, kérjük próbálja meg később';

  @override
  String get noInspirationsError =>
      'Hoppá, jelenleg nincs elérhető fotó, kérjük próbálja meg később';

  @override
  String get mostFrequentlyVisitedCountries =>
      'A leggyakrabban látogatott országok:';

  @override
  String get update => 'Frissítés';

  @override
  String get signup => 'Regisztrálj';

  @override
  String get loginWallSubtitle =>
      'Hozzon létre egy ingyenes fiókot, hogy megtapasztalja az Visited teljes verzióját';

  @override
  String get loseAllSelectionsWarning =>
      'Az alkalmazás bezárása után elveszíti az összes választását.';

  @override
  String get createAccount => 'Fiók létrehozása';

  @override
  String get continueWithoutAccount => 'Folytatás fiók nélkül';

  @override
  String get inspirationPromotion => 'Ihletet kap a gyönyörű utazási fotózás';

  @override
  String get saveStatsPromotion => 'Mentsd meg utazási statisztikáidat!';

  @override
  String get selectRegionsPromotion => 'Államok és tartományok kiválasztása';

  @override
  String get experiencesPromotion =>
      'Élmények nyomon követése a világ minden tájáról';

  @override
  String get missingListItem =>
      'Hiányzott valami? Koppintson ide, hogy e -mailt küldjön nekünk, hogy hozzáadja a kedvenc helyét.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Hiányzó tétel a $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Meglátogattam a $amount $listName -et';
  }

  @override
  String get orderPoster => 'Poszter';

  @override
  String get shareMap => 'Megosztási térkép';

  @override
  String get posterLandingPageTitle => 'Szerezd meg a posztered';

  @override
  String get posterNotAvailableError =>
      'A poszter vásárlása jelenleg nem érhető el. Kérlek, próbáld újra később.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Szállítás';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Az egyedi nyomtatási térképeinkről\nNyomtassa ki személyre szabott világtérképét. Testreszabhatja a saját színeivel, és egyenesen otthonához szállítsa.\n \n### Műszaki adatok:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Táj orientációja.\n- Mikro tinta, cseppek pontos nyomatokhoz, 8 bites szín, szinte fotónyomtatás,\n- 0,22 mm vastag szatén papír\n\n### Szállítási adatok:\nSzállítás Torontóból, Kanadából a világ bármely pontjára, a Canada Post segítségével. Kérjük, hagyjon 2–4 hetet a legtöbb rendeltetési helyre történő szállításhoz. Az összes megrendelést egy kartondobozba rakják a benyújtott szállítási címre. Az összes fizetést az Apple Pay, a vagy a Stripe kezeli.\n\n\n### Lemondás/visszatérítés:\nA megrendeléseket azonnal feldolgozzák, miután a lehető leggyorsabban benyújtották. Ezért nincs elérhető visszatérítés/lemondás.';

  @override
  String get posterDescriptionMarkdown =>
      '## Az egyedi nyomtatási térképeinkről\nNyomtassa ki személyre szabott világtérképét. Testreszabhatja a saját színeivel, és egyenesen otthonához szállítsa.\n \n### Műszaki adatok:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Táj orientációja.\n- Mikro tinta, cseppek pontos nyomatokhoz, 8 bites szín, szinte fotónyomtatás,\n- 0,22 mm vastag szatén papír\n\n### Szállítási adatok:\nSzállítás Torontóból, Kanadából a világ bármely pontjára, a Canada Post segítségével. Kérjük, hagyjon 2–4 hetet a legtöbb rendeltetési helyre történő szállításhoz. Az összes megrendelést egy kartondobozba rakják a benyújtott szállítási címre. Az összes fizetést az a Google Pay vagy a Stripe kezeli.\n\n\n### Lemondás/visszatérítés:\nA megrendeléseket azonnal feldolgozzák, miután a lehető leggyorsabban benyújtották. Ezért nincs elérhető visszatérítés/lemondás.';

  @override
  String get posterCustomizeTitle => 'Testreszabja a plakátot';

  @override
  String get enterShippingAddress => 'Írja be a szállítási címet';

  @override
  String get price => 'Ár';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + adó';
  }

  @override
  String get showSelections => 'A kiválasztás megjelenítése';

  @override
  String get posterNoRefunds =>
      'A poszter kinyomtatása után nem állnak rendelkezésre visszatérítés.';

  @override
  String get posterReviewOrder => 'Nézze át rendelését';

  @override
  String get email => 'Email';

  @override
  String get emailEmptyError => 'Kérem, írja be az e-mail címét';

  @override
  String get fullName => 'Teljes név';

  @override
  String get fullNameEmptyError => 'Kérem adja meg a teljes nevét';

  @override
  String get streetAddressEmptyError => 'Kérjük, írja be az utcai címét';

  @override
  String get cityEmptyError => 'Kérjük, írja be a városát';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Kérjük, írja be a $fieldName';
  }

  @override
  String get country => 'Ország';

  @override
  String get countryEmptyError => 'Kérjük, írja be az országát';

  @override
  String get posterReviewOrderTitle => 'Nézze át rendelését';

  @override
  String get buyNow => 'Vásárolj most';

  @override
  String get secureCheckoutDisclaimer =>
      'Biztonságos pénztár, amelyet a Stripe biztosít';

  @override
  String get total => 'Teljes';

  @override
  String get tax => 'Adó';

  @override
  String get subtotal => 'Altotikus';

  @override
  String get posterProductName => 'Egyéni meglátogatott térképplakát';

  @override
  String get shipping => 'Szállítás';

  @override
  String get posterOrderReceivedTitle => 'A megrendelés beérkezett';

  @override
  String get posterOrderReceivedSubtitle => 'Megkaptuk a megrendelését!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'További frissítésekért ellenőrizze e-mailjeit. \nKérjük, várjon legfeljebb 4 hetet, amíg a poszter megérkezik. \nHa bármilyen kérdése van, kérjük, írjon nekünk az [<EMAIL>] (<EMAIL>) címre.';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Nyomtatott poszter megrendelés állapota';

  @override
  String get moreInfo => 'Több információ';

  @override
  String get logoutConfirm => 'Szeretne bejelentkezni az alkalmazásból?';

  @override
  String get emailNotAvailable => 'Ezt az e -mailt megkapták.';

  @override
  String get alphabetical => 'Betűrendes';

  @override
  String get firstTimeLiveTutorial =>
      'A szülőföld és a város biztosítása személyre szabja az alkalmazás élményét.';

  @override
  String get firstTimeBeenTutorial =>
      'Ha kiválasztja, hogy hol volt, lehetővé teszi, hogy megtekintse az összes ország térképét, ahol járt, és láthatja a személyes statisztikákat.';

  @override
  String get progressTooltipGoal =>
      'Utazási céljaid azon országok számán alapulnak, amelyeket \"szeretne\" utazni olyan országokhoz képest, ahol \"voltál\".';

  @override
  String get progressTooltipRank =>
      'Ez a szám megmutatja, hogyan rangsorolod az utazókhoz képest a világ minden tájáról. Növelheti rangját, ha több országba utazik.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Ez a grafikon azon országok számán alapul, amelyekben a világ összes országa összehasonlítása volt.';

  @override
  String get sortBy => 'Sorrend';

  @override
  String get updateWishlist => 'Frissítse a kívánságlistát';

  @override
  String get mapInfo =>
      'Kattintson az országra, hogy válassza ki a választást, a Want vagy a Live -t. A lista nézethez kattintson a bal felső sarokban található ikonra is.';

  @override
  String get oneTimePurchase => 'Minden egyszeri vásárlás!';

  @override
  String get contact => 'Kapcsolatba lépni';

  @override
  String get contactUs => 'Lépjen kapcsolatba velünk';

  @override
  String get noCitiesSelected =>
      'Még nem választott ki egyetlen várost sem ...';

  @override
  String get updateTravelGoal => 'Frissítse az utazási célt';

  @override
  String get travelGoalComplete =>
      'Gratulálunk! \n\ntap a + gombot további országok hozzáadásához.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Nincs számla társítva az $email e -mailt. Szeretné most létrehozni?';
  }

  @override
  String get tryAgain => 'Próbáld újra';

  @override
  String get itineraries => 'Utazási tervek';

  @override
  String get itinerary => 'Utazási terv';

  @override
  String get place => 'Hely';

  @override
  String get itinerariesDescription =>
      'Ezek azok a helyek, amelyek iránt érdeklődését kifejezte.\nHasználja ezt az útmutatót, hogy segítsen megtervezni a következő nyaralását.';

  @override
  String get addMore => 'Továbbiak hozzáadása';

  @override
  String get interests => 'Érdeklődés';

  @override
  String get selection => 'Kiválasztás';

  @override
  String get goal => 'Cél';

  @override
  String get noItineraries => 'Nincsenek Útiterv';

  @override
  String get noItinerariesExplanation =>
      'Kérjük, adjon hozzá néhány helyet, inspirációt vagy élményt, hogy láthassa az útiterv automatikusan generálódik.';

  @override
  String get clusterPins => 'Csoportosított Térkép Jelölők';

  @override
  String get toggleRegions => 'Régiók megjelenítése zoomoláskor';

  @override
  String get mapProjection => 'Térképvetület';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Egyenlítőhengeres';

  @override
  String get yourTravellerType => 'Az Ön utazói típusa:';

  @override
  String get yourHotelPreferences => 'Hotel preferenciái:';

  @override
  String get budget => 'Költségvetés';

  @override
  String get midScale => 'Közepes szint';

  @override
  String get luxury => 'Luxus';

  @override
  String get noTravellerType =>
      'Adjon hozzá elemeket a kívánságlistájához, hogy megtudja, milyen típusú utazó Ön.';

  @override
  String get unlockLived => 'Feloldás Élve';

  @override
  String get unlockLivedDescription =>
      'Válassza ki a térképen, hol élt korábban!';

  @override
  String get futureFeaturesDescription => '...és minden jövőbeli funkció';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Az Ön leggyakrabban látogatott országa:';

  @override
  String get departureDate => 'Indulási dátum';

  @override
  String get returnDate => 'Visszatérési dátum';

  @override
  String get hotels => 'Szállodák';

  @override
  String get food => 'Étel';

  @override
  String get travelDates => 'Utazási dátumok';

  @override
  String get posterForMe => 'Nekem';

  @override
  String get posterSendGift => 'Ajándék küldése';

  @override
  String get addSelections => 'Választások hozzáadása';

  @override
  String get posterType => 'Poszter típusa';

  @override
  String get help => 'Segítség';

  @override
  String get tutorialMap =>
      'Koppintson egy országra a következő kiválasztásához: volt, akar és élt.';

  @override
  String get tutorialMapList =>
      'Érintse meg a lista ikont (bal felső sarokban) a lista szerinti kiválasztásához.';

  @override
  String get tutorialCountryDetails =>
      'Érintse meg az országot, majd a „több” gombot a régió szerinti kiválasztásához.';

  @override
  String get tutorialItems =>
      'Csúsztassa a kapcsolót az elemek kiválasztásának módjának kiválasztásához.';

  @override
  String get tutorialInspirations =>
      'Csúsztassa jobbra vagy balra a következő kártyára lépéshez.';

  @override
  String get lifetime => 'Élettartam';

  @override
  String get chooseYourPlan => 'Válassza ki a tervét';

  @override
  String get requestARefund => 'Visszatérítés kérése';

  @override
  String get noPurchasesFound => 'Nem található vásárlás.';

  @override
  String get noProductsAvailable => 'Nincsenek elérhető termékek';

  @override
  String get posterLandingAppBar => 'Hozd haza történeteidet';

  @override
  String get posterLandingSubHeading => 'Az utazásod, a te történeted';

  @override
  String get posterLandingSubDescription =>
      'Az Ön utazásai több mint utazások, történetek, emlékek és mérföldkövek. Alakítsa át ezeket a felejthetetlen pillanatokat egy személyre szabott világtérképpé, amely ugyanolyan egyedi, mint a kalandjai.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Eredményeid térképe: Kiemeljen minden úti célt, az első nagy utazástól a legmerészebb kalandig.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Ünnepeljen minden utazást: Élje újra utazásait naponta egy gyönyörűen kialakított poszterrel, amely inspirál.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Ajándék, amit kincsnek tartanak: Lepje meg útitársát egy egyéni térképpel, amely bemutatja az utazását, tökéletes születésnapokra, mérföldkövekre vagy csak azért.';

  @override
  String get posterLandingHowItWorks => 'Hogyan működik!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Szabja testre a dizájnt: Válasszon színeket, stílusokat, és jelölje meg utazásait (vagy az övéket!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Tekintse meg térképe előnézetét: Nézze meg, hogy életre kel, mielőtt megrendelné.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Biztonságos fizetés: Gyors és biztonságos az Apple Pay vagy a Stripe segítségével.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Biztonságos fizetés: Gyors és biztonságos a Google Pay vagy a Stripe segítségével.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Megjelenítésre kész: egyenesen az Ön (vagy az övék) ajtóhoz szállítjuk.';

  @override
  String get posterLandingCustomerReviewsHeader => 'Utazótársak tapasztalatai';

  @override
  String get posterLandingCustomerReview1 =>
      'Ez a térkép nagyszerű módja annak, hogy nyomon követhessem mindenhol, ahol utaztam, és megtervezzük jövőbeli utazásainkat. A minőség jó, és fantasztikusan jól néz ki az irodámban. Még a bátyámnak is szereztem egyet, és nem tudta abbahagyni, hogy milyen klassz! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Több mint 150 kikötőbe utaztam, miközben körutazáson dolgoztam. Ez a térkép nagyszerű kiegészítője a nappalimnak, emlékként a tengeren töltött évekre. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Remek ajándék anyák napjára. Anyukám nagyon meghatódott! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Nyomtatott egy térképet azokról a helyekről, amelyeket meg akartam látogatni a gf-emmel. Remek karácsonyi ajándék volt. Kiváló minőség is. Brad J.';

  @override
  String get posterLandingSpecifications => 'Műszaki adatok';

  @override
  String get posterLandingSpecification1 =>
      '• Méretek: 16\" x 20\" (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '• Tájolás: Táj';

  @override
  String get posterLandingSpecification3 =>
      '• Nyomtatási minőség: Mikrotinta, cseppek a precíz nyomatokhoz.  8 bites színes, szinte fotónyomtatási minőség.';

  @override
  String get posterLandingSpecification4 =>
      '• Papír: 0,22 mm vastag szaténpapír';

  @override
  String get posterLandingShippingHeader => 'Szállítási részletek';

  @override
  String get posterLandingShipping1 =>
      '• Szállítás Torontóból, Kanadából a világ bármely pontjára a Canada Post segítségével.';

  @override
  String get posterLandingShipping2 =>
      '• Várjon 2-4 hetet a szállításhoz a legtöbb helyre.';

  @override
  String get posterLandingShipping3 =>
      '• Minden rendelést egy kartondobozba tekerünk az Ön által megadott szállítási címre.';

  @override
  String get posterLandingCancellationHeader => 'Lemondás/visszatérítés:';

  @override
  String get posterLandingCancellationBody =>
      'A visszatérítésre azelőtt van lehetőség, hogy a plakátot elküldik a nyomtatóra, ami akár 24 órát is igénybe vehet.  A rendelés feldolgozása után nincs lehetőség visszatérítésre/lemondásra.  A megrendelés kinyomtatásáról e-mailben értesítünk.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
