// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Idioma';

  @override
  String get pickEmailApp => 'Escolha a sua aplicação de e-mail';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Eu visitei $amount países! Quantos já visitou? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Eu visitei $amount cidades! Quantas já visitou? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Eu visitei $amount $listName! Quantos já visitou? www.visitedapp.com';
  }

  @override
  String get clear => 'Limpar';

  @override
  String get been => 'Esteve';

  @override
  String get want => 'Quer';

  @override
  String get live => 'Vive';

  @override
  String get lived => 'Morava';

  @override
  String get water => 'Água';

  @override
  String get land => 'Terra';

  @override
  String get borders => 'Fronteiras';

  @override
  String get labels => 'Etiquetas';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inspiração';

  @override
  String get inspirations => 'Inspirações';

  @override
  String get delete => 'Excluir';

  @override
  String get unlockVisitedUpsellTitle => 'Quer ver mais?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Desbloqueie todos os recursos e aproveite o Visited com todo o seu potencial';

  @override
  String get checkTheDetails => 'Verifique os detalhes';

  @override
  String get moreInspirationsComingSoon =>
      'Estamos trabalhando para conseguir mais imagens. Verifique em breve!';

  @override
  String get unlockPremiumFeatures => 'Desbloquear recursos premium';

  @override
  String get purchased => 'Comprado!';

  @override
  String get buy => 'Comprar';

  @override
  String get restorePurchases => 'Restaurar compra';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Tem certeza?';

  @override
  String get deleteInspirationConfirmMessage =>
      'A exclusão deste cartão é permanente. Não há como recuperar esta imagem.';

  @override
  String get cancel => 'Cancelar';

  @override
  String get map => 'Mapa';

  @override
  String get progress => 'Progresso';

  @override
  String get myTravelGoal => 'Minha meta de viagem';

  @override
  String goalRemaining(int remaining) {
    return 'Faltam $remaining!';
  }

  @override
  String get top => 'PRINCIPAL';

  @override
  String get ofTheWorld => 'do mundo!';

  @override
  String get countries => 'países';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Principais países visitados de $country:';
  }

  @override
  String get login => 'Entrar';

  @override
  String get logout => 'Sair';

  @override
  String get enterYourEmail => 'Digite seu e-mail';

  @override
  String get privacyPolicy => 'Política de Privacidade';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-portuguese/';

  @override
  String get termsOfUse => 'Termos de uso';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-portuguese/';

  @override
  String get errorTitle => 'Ops!';

  @override
  String get enterValidEmail => 'Por favor digite um email válido';

  @override
  String get settings => 'Configurações';

  @override
  String get whereDoYouLive => 'Onde você vive?';

  @override
  String get whereHaveYouBeen => 'Onde você esteve?';

  @override
  String get whereDoYouFlyFrom => 'De onde você pega os voos?';

  @override
  String get next => 'Próximo';

  @override
  String get missingAirports =>
      'Não encontra o que está procurando? Envie-nos um e-<NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aeroportos ausentes!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Bem-vindo ao Visited';

  @override
  String get welcomeSubtitle => 'A aventura da sua vida te espera';

  @override
  String get getStarted => 'Começar';

  @override
  String get privacyAgreement => 'Acordo de Privacidade';

  @override
  String get privacyAgreementSubtitle =>
      'Por favor, concorde com os itens a seguir antes de continuar a usar o Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Ao marcar esta caixa, você reconhece que leu e concorda em obedecer à [Política de Privacidade] (https://www.arrivinginhighheels.com/privacy-policy) e aos [Termos de Uso] (https://www.arrivinginhighheels.com/terms-of-use) da Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Concordo em receber mensagens eletrônicas da Arriving in High Heels contendo informações e ofertas relacionadas a produtos, aplicativos e serviços que possam ser do meu interesse, incluindo notificações de vendas, promoções, ofertas e boletins informativos. Posso retirar este consentimento a qualquer momento conforme descrito na Política de Privacidade ou clicando no link “cancelar” nas mensagens eletrônicas.';

  @override
  String get submit => 'Enviar';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Você deve concordar com os nossos termos e optar por continuar usando o Visited.';

  @override
  String get deleteAccount => 'Excluir conta';

  @override
  String get removeAdsUpsell =>
      'Ao invés disso, você não gostaria de desativar os anúncios e cancelar a inscrição de marketing por email?';

  @override
  String get deleteAccountWarning =>
      'Excluir sua conta removerá todas as suas informações de nossos servidores.\n Este processo não pode ser desfeito.';

  @override
  String get about => 'Sobre';

  @override
  String get popularity => 'Popularidade';

  @override
  String get regions => 'Regiões';

  @override
  String get population => 'População';

  @override
  String get size => 'Tamanho';

  @override
  String get coverage => 'Cobertura';

  @override
  String get percentOfCountryVisited => '% do país visitado';

  @override
  String get visited => 'visitado';

  @override
  String get notes => 'Notas';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Customizar';

  @override
  String get onlyCountSovereign => 'Contar apenas países soberanos';

  @override
  String get countUkSeparately =>
      'Contar os países do Reino Unido separadamente';

  @override
  String get showLegend => 'Mostrar legenda';

  @override
  String get showLivedPin => 'Mostrar distintivo vivido';

  @override
  String get useMyColours => 'Usar minhas cores';

  @override
  String get mapColors => 'Cores do mapa';

  @override
  String get traveller => 'O Viajante';

  @override
  String get nightTraveller => 'O Viajante Noturno';

  @override
  String get original => 'O Original';

  @override
  String get explorer => 'O Explorador';

  @override
  String get weekender => 'O Viajante dos Finais de Semana';

  @override
  String get naturalist => 'O Naturalista';

  @override
  String get historian => 'O Historiador';

  @override
  String get thrillSeeker => 'O Buscador das Emoções';

  @override
  String get culturalBuff => 'O Aficionado Cultural';

  @override
  String get myColors => 'Minhas cores';

  @override
  String get experiences => 'Experiências';

  @override
  String get done => 'Pronto';

  @override
  String get experiencesInstructions => 'Toque no botão + para começar!';

  @override
  String get continueText => 'Continuar';

  @override
  String get experiencesDescription =>
      'O que você gosta de fazer quando viaja?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-aplicativo-de-viagem/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'My Travel Map';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Eu vi $percentage% do mundo';
  }

  @override
  String get requiresOnline =>
      'Desculpe, Visited requer uma conexão de rede ativa. Por favor, abra o aplicativo de configurações e certifique-se de que tanto o \\r Wi-Fi ou os dados móveis estejam habilitados e o modo avião esteja desabilitado';

  @override
  String get list => 'Lista';

  @override
  String get more => 'Mais';

  @override
  String get myCountrySelections => 'Minhas seleções de país';

  @override
  String get cities => 'Cidades';

  @override
  String get citiesInstructions =>
      'Toque em qualquer país para começar a selecionar cidades.';

  @override
  String get missingCitiesEmailTitle => 'Cidades ausentes!';

  @override
  String get lists => 'Listas';

  @override
  String get disputedTerritories => 'Territórios em disputa';

  @override
  String get sponsored => 'Patrocinado';

  @override
  String get places => 'Locais';

  @override
  String get noListsError =>
      'Ops, não há lista disponível no momento, por favor, tente um pouco mais tarde';

  @override
  String get noInspirationsError =>
      'Ops, não há fotos disponíveis no momento, por favor, tente um pouco mais tarde';

  @override
  String get mostFrequentlyVisitedCountries => 'Países mais visitados:';

  @override
  String get update => 'Atualizar';

  @override
  String get signup => 'Inscrever-se';

  @override
  String get loginWallSubtitle =>
      'Crie uma conta gratuita para experimentar a versão completa do Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Perderá todas as suas seleções depois de fechar a aplicação.';

  @override
  String get createAccount => 'Criar Conta';

  @override
  String get continueWithoutAccount => 'Continuar sem uma Conta';

  @override
  String get inspirationPromotion =>
      'Inspire-se com uma bela fotografia de viagem';

  @override
  String get saveStatsPromotion => 'Guarde as estatísticas de viagem!';

  @override
  String get selectRegionsPromotion => 'Selecione Estados e Províncias';

  @override
  String get experiencesPromotion => 'Experiências de pista em todo o mundo';

  @override
  String get missingListItem =>
      'Perdemos alguma coisa? Toque aqui para nos enviar um e -mail para adicionar seu lugar favorito.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Item ausente de $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Eu visitei $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Compartilhar mapa';

  @override
  String get posterLandingPageTitle => 'Obtenha o seu poster';

  @override
  String get posterNotAvailableError =>
      'A compra de pôsteres não está disponível no momento. Por favor, tente novamente mais tarde.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Envio';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Sobre nossos mapas de impressão personalizados\nImprima seu mapa do mundo personalizado. Personalize -o com suas próprias cores e entregue diretamente em sua casa.\n \n### Especificações:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientação da paisagem.\n- Micro tinta, gotículas para impressões precisas, cores de 8 bits, qualidade de impressão quase fotográfica,\n- Papel de cetim de 0,22 mm de espessura\n\n### Detalhes de envio:\nEnvio de Toronto, Canadá para qualquer lugar do mundo usando o Canada Post. Aguarde de 2 a 4 semanas para a entrega da maioria dos destinos. Todos os pedidos são enviados enrolados em uma caixa de tubo de papelão para o endereço de entrega enviado. Todo o pagamento é tratado pelo Apple Pay, ou Stripe.\n\n\n### Cancelamento/reembolso:\nOs pedidos são processados ​​imediatamente após serem enviados para a reviravolta mais rápida possível. Portanto, não há reembolso/cancelamento disponível.';

  @override
  String get posterDescriptionMarkdown =>
      '## Sobre nossos mapas de impressão personalizados\nImprima seu mapa do mundo personalizado. Personalize -o com suas próprias cores e entregue diretamente em sua casa.\n \n### Especificações:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientação da paisagem.\n- Micro tinta, gotículas para impressões precisas, cores de 8 bits, qualidade de impressão quase fotográfica,\n- Papel de cetim de 0,22 mm de espessura\n\n### Detalhes de envio:\nEnvio de Toronto, Canadá para qualquer lugar do mundo usando o Canada Post. Aguarde de 2 a 4 semanas para a entrega da maioria dos destinos. Todos os pedidos são enviados enrolados em uma caixa de tubo de papelão para o endereço de entrega enviado. Todo o pagamento é tratado pelo Google Pay ou Stripe.\n\n\n### Cancelamento/reembolso:\nOs pedidos são processados ​​imediatamente após serem enviados para a reviravolta mais rápida possível. Portanto, não há reembolso/cancelamento disponível.';

  @override
  String get posterCustomizeTitle => 'Personalize o pôster';

  @override
  String get enterShippingAddress => 'Digite o endereço de entrega';

  @override
  String get price => 'Preço';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + imposto';
  }

  @override
  String get showSelections => 'Mostrar seleção';

  @override
  String get posterNoRefunds =>
      'Nenhum reembolso está disponível após o seu pôster ter sido impresso.';

  @override
  String get posterReviewOrder => 'Revise seu pedido';

  @override
  String get email => 'E-mail';

  @override
  String get emailEmptyError => 'Por favor introduza o seu e-mail';

  @override
  String get fullName => 'Nome completo';

  @override
  String get fullNameEmptyError => 'porfavor digite seu nome completo';

  @override
  String get streetAddressEmptyError => 'Por favor, digite seu endereço de rua';

  @override
  String get cityEmptyError => 'Por favor, digite sua cidade';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Por favor, digite seu $fieldName';
  }

  @override
  String get country => 'País';

  @override
  String get countryEmptyError => 'Por favor, digite seu país';

  @override
  String get posterReviewOrderTitle => 'Revise seu pedido';

  @override
  String get buyNow => 'Compre Agora';

  @override
  String get secureCheckoutDisclaimer =>
      'Check -out seguro fornecido por listra';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Imposto';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Pôster de mapa visitado personalizado';

  @override
  String get shipping => 'Envio';

  @override
  String get posterOrderReceivedTitle => 'Pedido Recebido';

  @override
  String get posterOrderReceivedSubtitle => 'Recebemos seu pedido!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Verifique seu e-mail para mais atualizações. \nAguarde até 4 semanas para que seu pôster chegue. \nSe você tiver alguma dúvida, envie um email para [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Status do pedido de pôster impresso';

  @override
  String get moreInfo => 'Mais Informações';

  @override
  String get logoutConfirm => 'Você gostaria de sair do aplicativo?';

  @override
  String get emailNotAvailable => 'Esse e -mail foi recebido.';

  @override
  String get alphabetical => 'Alfabético';

  @override
  String get firstTimeLiveTutorial =>
      'Ao fornecer o seu país e cidade de origem, personalizará a sua experiência de aplicação.';

  @override
  String get firstTimeBeenTutorial =>
      'Seleccionando onde esteve permite-lhe visualizar o seu mapa de todos os países onde esteve e ver as estatísticas pessoais.';

  @override
  String get progressTooltipGoal =>
      'Os seus objectivos de viagem baseiam-se no número de países que \"Quer\" viajar em comparação com os países onde \"Já esteve\".';

  @override
  String get progressTooltipRank =>
      'Este número mostra a sua classificação em comparação com os viajantes de todo o mundo.  Pode aumentar a sua classificação ao viajar para mais países.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Este gráfico é baseado no número de países em que esteve em comparação com o total de países do mundo.';

  @override
  String get sortBy => 'Ordenar por';

  @override
  String get updateWishlist => 'Actualização da Lista de Desejos';

  @override
  String get mapInfo =>
      'Clicar sobre o país para seleccionar ter sido, querer ou viver. Pode também clicar no ícone encontrado no canto superior esquerdo para a visualização da lista.';

  @override
  String get oneTimePurchase => 'Tudo é uma compra única!';

  @override
  String get contact => 'Contacte';

  @override
  String get contactUs => 'Contacte-nos';

  @override
  String get noCitiesSelected => 'Ainda não seleccionou nenhuma cidade...';

  @override
  String get updateTravelGoal => 'Atualize a meta de viagem';

  @override
  String get travelGoalComplete =>
      'Parabéns! \n\ntap o botão + para adicionar mais países.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Não há conta associada ao email $email. Você gostaria de criá -lo agora?';
  }

  @override
  String get tryAgain => 'Tentar novamente';

  @override
  String get itineraries => 'Planos de viagem';

  @override
  String get itinerary => 'Plano de viagem';

  @override
  String get place => 'Locais';

  @override
  String get itinerariesDescription =>
      'Estes são os locais pelos quais manifestou interesse.\nUtilize este guia para ajudar a planear as suas próximas férias.';

  @override
  String get addMore => 'Adicionar mais';

  @override
  String get interests => 'Interesses';

  @override
  String get selection => 'Seleção';

  @override
  String get goal => 'Meta';

  @override
  String get noItineraries => 'Sem Itinerários';

  @override
  String get noItinerariesExplanation =>
      'Por favor, adicione alguns lugares, inspirações ou experiências para ver os seus itinerários gerarem automaticamente.';

  @override
  String get clusterPins => 'Agrupar Marcadores de Mapa';

  @override
  String get toggleRegions => 'Mostrar regiões ao ampliar';

  @override
  String get mapProjection => 'Projeção do mapa';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'O seu tipo de viajante:';

  @override
  String get yourHotelPreferences => 'As suas preferências de hotel:';

  @override
  String get budget => 'Orçamento';

  @override
  String get midScale => 'Médio alcance';

  @override
  String get luxury => 'Luxo';

  @override
  String get noTravellerType =>
      'Adicione itens à sua lista de desejos para descobrir que tipo de viajante é.';

  @override
  String get unlockLived => 'Desbloquear Vivido';

  @override
  String get unlockLivedDescription =>
      'Selecione no mapa onde viveu anteriormente!';

  @override
  String get futureFeaturesDescription =>
      '...e todas as funcionalidades futuras';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Seu país mais visitado:';

  @override
  String get departureDate => 'Data de partida';

  @override
  String get returnDate => 'Data de regresso';

  @override
  String get hotels => 'Hotéis';

  @override
  String get food => 'Comida';

  @override
  String get travelDates => 'Datas de viagem';

  @override
  String get posterForMe => 'Para mim';

  @override
  String get posterSendGift => 'Enviar um presente';

  @override
  String get addSelections => 'Adicionar seleções';

  @override
  String get posterType => 'Tipo de poster';

  @override
  String get help => 'Ajuda';

  @override
  String get tutorialMap =>
      'Toque em um país para selecionar: foi, quer e viveu.';

  @override
  String get tutorialMapList =>
      'Toque no ícone da lista (canto superior esquerdo) para selecionar por lista.';

  @override
  String get tutorialCountryDetails =>
      'Toque no país e depois em “mais” para selecionar por região.';

  @override
  String get tutorialItems =>
      'Deslize o botão de alternância para escolher como deseja selecionar os itens.';

  @override
  String get tutorialInspirations =>
      'Deslize para a direita ou para a esquerda para passar para o próximo cartão.';

  @override
  String get lifetime => 'Vitalício';

  @override
  String get chooseYourPlan => 'Escolha o seu plano';

  @override
  String get requestARefund => 'Solicitar um reembolso';

  @override
  String get noPurchasesFound => 'Nenhuma compra encontrada.';

  @override
  String get noProductsAvailable => 'Nenhum produto disponível';

  @override
  String get posterLandingAppBar => 'Leve as suas histórias para casa';

  @override
  String get posterLandingSubHeading => 'A Sua Viagem, a Sua História';

  @override
  String get posterLandingSubDescription =>
      'As suas viagens são mais do que simples passeios, são histórias, memórias e marcos. Transforme estes momentos inesquecíveis num mapa-mundo personalizado, tão único como as suas aventuras.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Um mapa das suas conquistas: destaque cada destino, desde a sua primeira grande viagem até à sua aventura mais ousada.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebre cada viagem: reviva as suas viagens diariamente com um póster maravilhosamente elaborado e concebido para inspirar.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Um presente que eles vão apreciar: surpreenda um companheiro de viagem com um mapa personalizado que mostre a sua viagem, perfeito para aniversários, datas importantes ou apenas porque sim.';

  @override
  String get posterLandingHowItWorks => 'Como funciona!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1.º Personalize o seu design: escolha cores, estilos e marque as suas viagens (ou as deles!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2.º Visualize o seu mapa: veja-o ganhar vida antes de fazer a sua encomenda.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3.º Pagamento seguro: rápido e seguro com Apple Pay ou Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3.º Pagamento seguro: rápido e seguro com Google Pay ou Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4.º Pronto para exibição: enviaremos diretamente para a sua porta (ou para a deles).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiências de outros viajantes';

  @override
  String get posterLandingCustomerReview1 =>
      '“Este mapa é uma excelente forma de manter um registo de todos os sítios por onde viajei e de planear as nossas futuras viagens.  A qualidade é sólida e fica muito bem pendurado no meu escritório.  Até comprei um para o meu irmão e ele não parava de falar de como é fixe!” - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '“Viajei para mais de 150 portos enquanto trabalhava num cruzeiro. Este mapa é um ótimo complemento para a minha sala de estar como recordação de todos os anos no mar.” - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '“Excelente prenda para o dia da mãe. A minha mãe ficou super emocionada!” Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '“Imprimi um mapa dos sítios que queria visitar com a minha namorada. Foi uma óptima prenda de Natal. Também é de alta qualidade.” Brad J.';

  @override
  String get posterLandingSpecifications => 'Especificações';

  @override
  String get posterLandingSpecification1 =>
      '- Dimensões: 16“ x 20” (40,64cm x 50,8cm)';

  @override
  String get posterLandingSpecification2 => '- Orientação: Paisagem';

  @override
  String get posterLandingSpecification3 =>
      '- Qualidade de impressão: Micro gotas de tinta para impressões precisas.  Cor de 8 bits, qualidade de impressão quase fotográfica.';

  @override
  String get posterLandingSpecification4 =>
      '- Papel: Papel acetinado de 0,22 mm de espessura';

  @override
  String get posterLandingShippingHeader => 'Detalhes de envio';

  @override
  String get posterLandingShipping1 =>
      '- Envio a partir de Toronto, Canadá, para qualquer parte do mundo utilizando os Correios do Canadá.';

  @override
  String get posterLandingShipping2 =>
      '- Prazo de entrega de 2 a 4 semanas para a maioria dos destinos.';

  @override
  String get posterLandingShipping3 =>
      '- Todas as encomendas são enroladas numa caixa de cartão para o endereço de envio que indicar.';

  @override
  String get posterLandingCancellationHeader => 'Cancelamento/Reembolso:';

  @override
  String get posterLandingCancellationBody =>
      'Os reembolsos estão disponíveis antes de o seu cartaz ter sido enviado para a gráfica, o que pode demorar até 24 horas.  Depois de a encomenda ter sido processada, não é possível efetuar qualquer reembolso/cancelamento.  Receberá um e-mail quando a sua encomenda tiver sido impressa.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}

/// The translations for Portuguese, as used in Brazil (`pt_BR`).
class AppLocalizationsPtBr extends AppLocalizationsPt {
  AppLocalizationsPtBr() : super('pt_BR');

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Idioma';

  @override
  String get pickEmailApp => 'Escolha seu aplicativo de e-mail';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Eu visitei $amount países! Quantos você já visitou? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Eu visitei $amount cidades! Quantas você já visitou? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Eu visitei $amount $listName! Quantos você já visitou? www.visitedapp.com';
  }

  @override
  String get clear => 'Limpar';

  @override
  String get been => 'Esteve';

  @override
  String get want => 'Quer';

  @override
  String get live => 'Vive';

  @override
  String get lived => 'Morava';

  @override
  String get water => 'Água';

  @override
  String get land => 'Terra';

  @override
  String get borders => 'Fronteiras';

  @override
  String get labels => 'Etiquetas';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inspiração';

  @override
  String get inspirations => 'Inspirações';

  @override
  String get delete => 'Excluir';

  @override
  String get unlockVisitedUpsellTitle => 'Quer ver mais?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Desbloqueie todos os recursos e aproveite o Visited com todo o seu potencial';

  @override
  String get checkTheDetails => 'Verifique os detalhes';

  @override
  String get moreInspirationsComingSoon =>
      'Estamos trabalhando para conseguir mais imagens. Verifique em breve!';

  @override
  String get unlockPremiumFeatures => 'Desbloquear recursos premium';

  @override
  String get purchased => 'Comprado!';

  @override
  String get buy => 'Comprar';

  @override
  String get restorePurchases => 'Restaurar compra';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Tem certeza?';

  @override
  String get deleteInspirationConfirmMessage =>
      'A exclusão deste cartão é permanente. Não há como recuperar esta imagem.';

  @override
  String get cancel => 'Cancelar';

  @override
  String get map => 'Mapa';

  @override
  String get progress => 'Progresso';

  @override
  String get myTravelGoal => 'Minha meta de viagem';

  @override
  String goalRemaining(int remaining) {
    return 'Faltam $remaining!';
  }

  @override
  String get top => 'PRINCIPAL';

  @override
  String get ofTheWorld => 'do mundo!';

  @override
  String get countries => 'países';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Principais países visitados de $country:';
  }

  @override
  String get login => 'Entrar';

  @override
  String get logout => 'Sair';

  @override
  String get enterYourEmail => 'Digite seu e-mail';

  @override
  String get privacyPolicy => 'Política de Privacidade';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-portuguese/';

  @override
  String get termsOfUse => 'Termos de uso';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-portuguese/';

  @override
  String get errorTitle => 'Ops!';

  @override
  String get enterValidEmail => 'Por favor digite um email válido';

  @override
  String get settings => 'Configurações';

  @override
  String get whereDoYouLive => 'Onde você vive?';

  @override
  String get whereHaveYouBeen => 'Onde você esteve?';

  @override
  String get whereDoYouFlyFrom => 'De onde você pega os voos?';

  @override
  String get next => 'Próximo';

  @override
  String get missingAirports =>
      'Não encontra o que está procurando? Envie-nos um e-<NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aeroportos ausentes!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Bem-vindo ao Visited';

  @override
  String get welcomeSubtitle => 'A aventura da sua vida te espera';

  @override
  String get getStarted => 'Começar';

  @override
  String get privacyAgreement => 'Acordo de Privacidade';

  @override
  String get privacyAgreementSubtitle =>
      'Por favor, concorde com os itens a seguir antes de continuar a usar o Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Ao marcar esta caixa, você reconhece que leu e concorda em obedecer à [Política de Privacidade] (https://www.arrivinginhighheels.com/privacy-policy) e aos [Termos de Uso] (https://www.arrivinginhighheels.com/terms-of-use) da Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Concordo em receber mensagens eletrônicas da Arriving in High Heels contendo informações e ofertas relacionadas a produtos, aplicativos e serviços que possam ser do meu interesse, incluindo notificações de vendas, promoções, ofertas e boletins informativos. Posso retirar este consentimento a qualquer momento conforme descrito na Política de Privacidade ou clicando no link \"cancelar\" nas mensagens eletrônicas.';

  @override
  String get submit => 'Enviar';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Você deve concordar com os nossos termos e optar por continuar usando o Visited.';

  @override
  String get deleteAccount => 'Excluir conta';

  @override
  String get removeAdsUpsell =>
      'Ao invés disso, você não gostaria de desativar os anúncios e cancelar a inscrição de marketing por email?';

  @override
  String get deleteAccountWarning =>
      'Excluir sua conta removerá todas as suas informações de nossos servidores.\n Este processo não pode ser desfeito.';

  @override
  String get about => 'Sobre';

  @override
  String get popularity => 'Popularidade';

  @override
  String get regions => 'Regiões';

  @override
  String get population => 'População';

  @override
  String get size => 'Tamanho';

  @override
  String get coverage => 'Cobertura';

  @override
  String get percentOfCountryVisited => '% do país visitado';

  @override
  String get visited => 'visitado';

  @override
  String get notes => 'Notas';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Customizar';

  @override
  String get onlyCountSovereign => 'Contar apenas países soberanos';

  @override
  String get countUkSeparately =>
      'Contar os países do Reino Unido separadamente';

  @override
  String get showLegend => 'Mostrar legenda';

  @override
  String get showLivedPin => 'Mostrar distintivo vivido';

  @override
  String get useMyColours => 'Usar minhas cores';

  @override
  String get mapColors => 'Cores do mapa';

  @override
  String get traveller => 'O Viajante';

  @override
  String get nightTraveller => 'O Viajante Noturno';

  @override
  String get original => 'O Original';

  @override
  String get explorer => 'O Explorador';

  @override
  String get weekender => 'O Viajante dos Finais de Semana';

  @override
  String get naturalist => 'O Naturalista';

  @override
  String get historian => 'O Historiador';

  @override
  String get thrillSeeker => 'O Buscador das Emoções';

  @override
  String get culturalBuff => 'O Aficionado Cultural';

  @override
  String get myColors => 'Minhas cores';

  @override
  String get experiences => 'Experiências';

  @override
  String get done => 'Pronto';

  @override
  String get experiencesInstructions => 'Toque no botão + para começar!';

  @override
  String get continueText => 'Continuar';

  @override
  String get experiencesDescription =>
      'O que você gosta de fazer quando viaja?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-aplicativo-de-viagem/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'My Travel Map';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Eu vi $percentage% do mundo';
  }

  @override
  String get requiresOnline =>
      'Desculpe, Visited requer uma conexão de rede ativa. Por favor, abra o aplicativo de configurações e certifique-se de que tanto o \\r Wi-Fi ou os dados móveis estejam habilitados e o modo avião esteja desabilitado';

  @override
  String get list => 'Lista';

  @override
  String get more => 'Mais';

  @override
  String get myCountrySelections => 'Minhas seleções de país';

  @override
  String get cities => 'Cidades';

  @override
  String get citiesInstructions =>
      'Toque em qualquer país para começar a selecionar cidades.';

  @override
  String get missingCitiesEmailTitle => 'Cidades ausentes!';

  @override
  String get lists => 'Listas';

  @override
  String get disputedTerritories => 'Territórios em disputa';

  @override
  String get sponsored => 'Patrocinado';

  @override
  String get places => 'Locais';

  @override
  String get noListsError =>
      'Ops, não há lista disponível no momento, por favor, tente um pouco mais tarde';

  @override
  String get noInspirationsError =>
      'Ops, não há fotos disponíveis no momento, por favor, tente um pouco mais tarde';

  @override
  String get mostFrequentlyVisitedCountries => 'Países mais visitados:';

  @override
  String get update => 'Atualizar';

  @override
  String get signup => 'Inscrever-se';

  @override
  String get loginWallSubtitle =>
      'Crie uma conta gratuita para experimentar a versão completa do Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Perderá todas as suas seleções depois de fechar a aplicação.';

  @override
  String get createAccount => 'Criar Conta';

  @override
  String get continueWithoutAccount => 'Continuar sem uma Conta';

  @override
  String get inspirationPromotion =>
      'Inspire-se com uma bela fotografia de viagem';

  @override
  String get saveStatsPromotion => 'Guarde as estatísticas de viagem!';

  @override
  String get selectRegionsPromotion => 'Selecione Estados e Províncias';

  @override
  String get experiencesPromotion => 'Experiências de pista em todo o mundo';

  @override
  String get missingListItem =>
      'Perdemos alguma coisa? Toque aqui para nos enviar um e -mail para adicionar seu lugar favorito.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Item ausente de $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Eu visitei $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Compartilhar mapa';

  @override
  String get posterLandingPageTitle => 'Pegue seu pôster';

  @override
  String get posterNotAvailableError =>
      'A compra de pôsteres não está disponível no momento. Por favor, tente novamente mais tarde.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Envio';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Sobre nossos mapas de impressão personalizados\nImprima seu mapa do mundo personalizado. Personalize -o com suas próprias cores e entregue diretamente em sua casa.\n \n### Especificações:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientação da paisagem.\n- Micro tinta, gotículas para impressões precisas, cores de 8 bits, qualidade de impressão quase fotográfica,\n- Papel de cetim de 0,22 mm de espessura\n\n### Detalhes de envio:\nEnvio de Toronto, Canadá para qualquer lugar do mundo usando o Canada Post. Aguarde de 2 a 4 semanas para a entrega da maioria dos destinos. Todos os pedidos são enviados enrolados em uma caixa de tubo de papelão para o endereço de entrega enviado. Todo o pagamento é tratado pelo Apple Pay, ou Stripe.\n\n\n### Cancelamento/reembolso:\nOs pedidos são processados ​​imediatamente após serem enviados para a reviravolta mais rápida possível. Portanto, não há reembolso/cancelamento disponível.';

  @override
  String get posterDescriptionMarkdown =>
      '## Sobre nossos mapas de impressão personalizados\nImprima seu mapa do mundo personalizado. Personalize -o com suas próprias cores e entregue diretamente em sua casa.\n \n### Especificações:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientação da paisagem.\n- Micro tinta, gotículas para impressões precisas, cores de 8 bits, qualidade de impressão quase fotográfica,\n- Papel de cetim de 0,22 mm de espessura\n\n### Detalhes de envio:\nEnvio de Toronto, Canadá para qualquer lugar do mundo usando o Canada Post. Aguarde de 2 a 4 semanas para a entrega da maioria dos destinos. Todos os pedidos são enviados enrolados em uma caixa de tubo de papelão para o endereço de entrega enviado. Todo o pagamento é tratado pelo Google Pay ou Stripe.\n\n\n### Cancelamento/reembolso:\nOs pedidos são processados ​​imediatamente após serem enviados para a reviravolta mais rápida possível. Portanto, não há reembolso/cancelamento disponível.';

  @override
  String get posterCustomizeTitle => 'Personalize o pôster';

  @override
  String get enterShippingAddress => 'Digite o endereço de entrega';

  @override
  String get price => 'Preço';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + imposto';
  }

  @override
  String get showSelections => 'Mostrar seleção';

  @override
  String get posterNoRefunds =>
      'Nenhum reembolso está disponível após o seu pôster ter sido impresso.';

  @override
  String get posterReviewOrder => 'Revise seu pedido';

  @override
  String get email => 'E-mail';

  @override
  String get emailEmptyError => 'Por favor introduza o seu e-mail';

  @override
  String get fullName => 'Nome completo';

  @override
  String get fullNameEmptyError => 'porfavor digite seu nome completo';

  @override
  String get streetAddressEmptyError => 'Por favor, digite seu endereço de rua';

  @override
  String get cityEmptyError => 'Por favor, digite sua cidade';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Por favor, digite seu $fieldName';
  }

  @override
  String get country => 'País';

  @override
  String get countryEmptyError => 'Por favor, digite seu país';

  @override
  String get posterReviewOrderTitle => 'Revise seu pedido';

  @override
  String get buyNow => 'Compre Agora';

  @override
  String get secureCheckoutDisclaimer =>
      'Check -out seguro fornecido por listra';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Imposto';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Pôster de mapa visitado personalizado';

  @override
  String get shipping => 'Envio';

  @override
  String get posterOrderReceivedTitle => 'Pedido Recebido';

  @override
  String get posterOrderReceivedSubtitle => 'Recebemos seu pedido!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Verifique seu e-mail para mais atualizações. \nAguarde até 4 semanas para que seu pôster chegue. \nSe você tiver alguma dúvida, envie um email para [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Status do pedido de pôster impresso';

  @override
  String get moreInfo => 'Mais Informações';

  @override
  String get logoutConfirm => 'Você gostaria de sair do aplicativo?';

  @override
  String get emailNotAvailable => 'Esse e -mail foi recebido.';

  @override
  String get alphabetical => 'Alfabético';

  @override
  String get firstTimeLiveTutorial =>
      'Ao fornecer o seu país e cidade de origem, personalizará a sua experiência de aplicação.';

  @override
  String get firstTimeBeenTutorial =>
      'Seleccionando onde esteve permite-lhe visualizar o seu mapa de todos os países onde esteve e ver as estatísticas pessoais.';

  @override
  String get progressTooltipGoal =>
      'Os seus objectivos de viagem baseiam-se no número de países que \"Quer\" viajar em comparação com os países onde \"Já esteve\".';

  @override
  String get progressTooltipRank =>
      'Este número mostra a sua classificação em comparação com os viajantes de todo o mundo.  Pode aumentar a sua classificação ao viajar para mais países.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Este gráfico é baseado no número de países em que esteve em comparação com o total de países do mundo.';

  @override
  String get sortBy => 'Ordenar por';

  @override
  String get updateWishlist => 'Actualização da Lista de Desejos';

  @override
  String get mapInfo =>
      'Clicar sobre o país para seleccionar ter sido, querer ou viver. Pode também clicar no ícone encontrado no canto superior esquerdo para a visualização da lista.';

  @override
  String get oneTimePurchase => 'Tudo é uma compra única!';

  @override
  String get contact => 'Contacte';

  @override
  String get contactUs => 'Contacte-nos';

  @override
  String get noCitiesSelected => 'Ainda não seleccionou nenhuma cidade...';

  @override
  String get updateTravelGoal => 'Atualize a meta de viagem';

  @override
  String get travelGoalComplete =>
      'Parabéns! \n\ntap o botão + para adicionar mais países.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Não há conta associada ao email $email. Você gostaria de criá -lo agora?';
  }

  @override
  String get tryAgain => 'Tentar novamente';

  @override
  String get itineraries => 'Planos de viagem';

  @override
  String get itinerary => 'Plano de viagem';

  @override
  String get place => 'Locais';

  @override
  String get itinerariesDescription =>
      'Estes são os locais pelos quais manifestou interesse.\nUtilize este guia para ajudar a planear as suas próximas férias.';

  @override
  String get addMore => 'Adicionar mais';

  @override
  String get interests => 'Interesses';

  @override
  String get selection => 'Seleção';

  @override
  String get goal => 'Meta';

  @override
  String get noItineraries => 'Sem Itinerários';

  @override
  String get noItinerariesExplanation =>
      'Por favor, adicione alguns lugares, inspirações ou experiências para ver os seus itinerários gerarem automaticamente.';

  @override
  String get clusterPins => 'Agrupar Marcadores de Mapa';

  @override
  String get toggleRegions => 'Mostrar regiões ao ampliar';

  @override
  String get mapProjection => 'Projeção do mapa';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'O seu tipo de viajante:';

  @override
  String get yourHotelPreferences => 'Suas preferências de hotel:';

  @override
  String get budget => 'Econômico';

  @override
  String get midScale => 'Intermediário';

  @override
  String get luxury => 'Luxo';

  @override
  String get noTravellerType =>
      'Adicione itens à sua lista de desejos para descobrir que tipo de viajante você é.';

  @override
  String get unlockLived => 'Desbloquear Vivido';

  @override
  String get unlockLivedDescription =>
      'Selecione no mapa onde você morou anteriormente!';

  @override
  String get futureFeaturesDescription => '...e todos os recursos futuros';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Seu país mais visitado:';

  @override
  String get departureDate => 'Data de partida';

  @override
  String get returnDate => 'Data de retorno';

  @override
  String get hotels => 'Hotéis';

  @override
  String get food => 'Comida';

  @override
  String get travelDates => 'Datas de viagem';

  @override
  String get posterForMe => 'Para mim';

  @override
  String get posterSendGift => 'Enviar um presente';

  @override
  String get addSelections => 'Adicionar seleções';

  @override
  String get posterType => 'Tipo de pôster';

  @override
  String get help => 'Ajuda';

  @override
  String get tutorialMap =>
      'Toque em um país para selecionar: foi, quer e viveu.';

  @override
  String get tutorialMapList =>
      'Toque no ícone da lista (canto superior esquerdo) para selecionar por lista.';

  @override
  String get tutorialCountryDetails =>
      'Toque no país e depois em \"mais\" para selecionar por região.';

  @override
  String get tutorialItems =>
      'Deslize o botão de alternância para escolher como deseja selecionar os itens.';

  @override
  String get tutorialInspirations =>
      'Deslize para a direita ou para a esquerda para passar para o próximo cartão.';

  @override
  String get lifetime => 'Vitalício';

  @override
  String get chooseYourPlan => 'Escolha seu plano';

  @override
  String get requestARefund => 'Solicitar um reembolso';

  @override
  String get noPurchasesFound => 'Nenhuma compra encontrada.';

  @override
  String get noProductsAvailable => 'Nenhum produto disponível';

  @override
  String get posterLandingAppBar => 'Traga suas histórias para casa';

  @override
  String get posterLandingSubHeading => 'Sua viagem, sua história';

  @override
  String get posterLandingSubDescription =>
      'Suas viagens são mais do que viagens, são histórias, memórias e marcos. Transforme esses momentos inesquecíveis em um mapa-múndi personalizado, tão exclusivo quanto suas aventuras.';

  @override
  String get posterLandingPromoBullet1 =>
      '- Um mapa de suas conquistas: Destaque cada destino, desde sua primeira grande viagem até sua aventura mais ousada.';

  @override
  String get posterLandingPromoBullet2 =>
      '- Comemore cada viagem: Reviva suas viagens diariamente com um pôster lindamente elaborado e projetado para inspirar.';

  @override
  String get posterLandingPromoBullet3 =>
      '- Um presente que eles guardarão com carinho: surpreenda um companheiro de viagem com um mapa personalizado que mostra sua jornada, perfeito para aniversários, marcos ou simplesmente porque sim.';

  @override
  String get posterLandingHowItWorks => 'Como funciona!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personalize seu design:  Escolha cores, estilos e marque suas viagens (ou as deles!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Pré-visualize seu mapa:  Veja-o ganhar vida antes de fazer seu pedido.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Pagamento seguro: Rápido e seguro com o Apple Pay ou Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Pagamento seguro: Rápido e seguro com o Google Pay ou Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Pronto para exibição: Nós o enviaremos diretamente para sua porta (ou a porta deles).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiências de outros viajantes';

  @override
  String get posterLandingCustomerReview1 =>
      '“Este mapa é uma excelente forma de manter um registo de todos os sítios por onde viajei e de planear as nossas futuras viagens.  A qualidade é sólida e fica muito bem pendurado no meu escritório.  Até comprei um para o meu irmão e ele não parava de falar de como é fixe!” - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '“Viajei para mais de 150 portos enquanto trabalhava num cruzeiro. Este mapa é um ótimo complemento para a minha sala de estar como recordação de todos os anos no mar.” - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '“Excelente prenda para o dia da mãe. A minha mãe ficou super emocionada!” Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '“Imprimi um mapa dos sítios que queria visitar com a minha namorada. Foi uma óptima prenda de Natal. Também é de alta qualidade.” Brad J.';

  @override
  String get posterLandingSpecifications => 'Especificações';

  @override
  String get posterLandingSpecification1 =>
      '- Dimensões: 16“ x 20” (40,64cm x 50,8cm)';

  @override
  String get posterLandingSpecification2 => '- Orientação: Paisagem';

  @override
  String get posterLandingSpecification3 =>
      '- Qualidade de impressão: Micro gotas de tinta para impressões precisas.  Cor de 8 bits, qualidade de impressão quase fotográfica.';

  @override
  String get posterLandingSpecification4 =>
      '- Papel: Papel acetinado de 0,22 mm de espessura';

  @override
  String get posterLandingShippingHeader => 'Detalhes de envio';

  @override
  String get posterLandingShipping1 =>
      '- Envio a partir de Toronto, Canadá, para qualquer parte do mundo utilizando os Correios do Canadá.';

  @override
  String get posterLandingShipping2 =>
      '- Prazo de entrega de 2 a 4 semanas para a maioria dos destinos.';

  @override
  String get posterLandingShipping3 =>
      '- Todas as encomendas são enroladas numa caixa de cartão para o endereço de envio que indicar.';

  @override
  String get posterLandingCancellationHeader => 'Cancelamento/Reembolso:';

  @override
  String get posterLandingCancellationBody =>
      'Os reembolsos estão disponíveis antes de o seu cartaz ter sido enviado para a gráfica, o que pode demorar até 24 horas.  Depois de a encomenda ter sido processada, não é possível efetuar qualquer reembolso/cancelamento.  Receberá um e-mail quando a sua encomenda tiver sido impressa.';
}
