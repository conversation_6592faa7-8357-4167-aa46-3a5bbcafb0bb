// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Croatian (`hr`).
class AppLocalizationsHr extends AppLocalizations {
  AppLocalizationsHr([String locale = 'hr']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Jezik';

  @override
  String get pickEmailApp => 'Odaberite svoju aplikaciju e-pošte';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Posjetio/la sam $amount zemalja! Koliko si ti posjetio/la? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Posjetio/la sam $amount gradova! Koliko si ti posjetio/la? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Posjetio/la sam $amount $listName! Koliko si ti posjetio/la? www.visitedapp.com';
  }

  @override
  String get clear => 'Očisti';

  @override
  String get been => 'Posjetio';

  @override
  String get want => 'Želim';

  @override
  String get live => 'Živim';

  @override
  String get lived => 'Živio';

  @override
  String get water => 'Voda';

  @override
  String get land => 'Zemlja';

  @override
  String get borders => 'Granice';

  @override
  String get labels => 'Oznake';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inspiracija';

  @override
  String get inspirations => 'Inspiracije';

  @override
  String get delete => 'Izbriši';

  @override
  String get unlockVisitedUpsellTitle => 'Želite vidjeti više?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Otključajte sve značajke i uživajte u Visited i njegovoj potpunoj snazi';

  @override
  String get checkTheDetails => 'Provjerite Detalje';

  @override
  String get moreInspirationsComingSoon =>
      'Radimo na nabavljanju više slika. Provjerite ponovno uskoro!';

  @override
  String get unlockPremiumFeatures => 'Otključajte premium značajke';

  @override
  String get purchased => 'Kupljeno!';

  @override
  String get buy => 'Kupi';

  @override
  String get restorePurchases => 'Vrati Kupnju';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Jeste li sigurni?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Brisanje ove kartice je trajno. Ne postoji način za oporavak ove slike.';

  @override
  String get cancel => 'Odustani';

  @override
  String get map => 'Karta';

  @override
  String get progress => 'Napredak';

  @override
  String get myTravelGoal => 'Moj Cilj Putovanja';

  @override
  String goalRemaining(int remaining) {
    return '$remaining još!';
  }

  @override
  String get top => 'VRH';

  @override
  String get ofTheWorld => 'svijeta!';

  @override
  String get countries => 'zemlje';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Najbolje Zemlje Posjećene iz $country:';
  }

  @override
  String get login => 'Prijavi se';

  @override
  String get logout => 'Odjava';

  @override
  String get enterYourEmail => 'Unesite svoj email';

  @override
  String get privacyPolicy => 'Pravila o privatnosti';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-croatian/';

  @override
  String get termsOfUse => 'Uvjeti Korištenja';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-croatian/';

  @override
  String get errorTitle => 'Ups!';

  @override
  String get enterValidEmail => 'Molimo unesite valjan email';

  @override
  String get settings => 'Postavke';

  @override
  String get whereDoYouLive => 'Gdje živite?';

  @override
  String get whereHaveYouBeen => 'Gdje ste sve bili?';

  @override
  String get whereDoYouFlyFrom => 'Odakle letite?';

  @override
  String get next => 'Dalje';

  @override
  String get missingAirports =>
      'Ne vidite ono što tražite? Pošaljite nam <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Zračne Luke koje Nedostaju!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Dobrodošli u Visited';

  @override
  String get welcomeSubtitle => 'Čeka vas životna avantura';

  @override
  String get getStarted => 'Započnite';

  @override
  String get privacyAgreement => 'Dogovor o Privatnosti';

  @override
  String get privacyAgreementSubtitle =>
      'Molimo vas da prihvatite sljedeće stavke prije nego što nastavite koristiti Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Označavanjem ovog okvira potvrđujete da ste pročitali i pristajete na obvezu Arriving in High Heels [Pravila o privatnosti] (https://www.arrivinginhighheels.com/privacy-policy) i [Uvjete korištenja] (http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Pristajem primati elektroničke poruke tvrtke Arriving in High Heels koje sadrže informacije i ponude u vezi s proizvodima, aplikacijama i uslugama koje bi me mogle zanimati, uključujući obavijesti o prodaji, promocijama, ponudama i biltenima. Ovu privolu mogu povući u bilo kojem trenutku kako je opisano u Pravilima o privatnosti ili klikom na link \"odjava\" u elektroničkim porukama.';

  @override
  String get submit => 'Podnesi';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Morate se složiti s našim uvjetima i odlučiti da biste nastavili koristiti Visited.';

  @override
  String get deleteAccount => 'Izbriši Račun';

  @override
  String get removeAdsUpsell =>
      'Želite li isključiti oglase i otkazati pretplatu na marketing putem emaila?';

  @override
  String get deleteAccountWarning =>
      'Brisanjem vašeg računa uklonit će se svi vaši podaci s naših poslužitelja. Ovaj se postupak ne može poništiti.';

  @override
  String get about => 'O nama';

  @override
  String get popularity => 'Popularnost';

  @override
  String get regions => 'Regije';

  @override
  String get population => 'Stanovništvo';

  @override
  String get size => 'Veličina';

  @override
  String get coverage => 'Pokrivenost';

  @override
  String get percentOfCountryVisited => '% posjećenosti zemlje';

  @override
  String get visited => 'posjećeno';

  @override
  String get notes => 'Bilješke';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Uredi';

  @override
  String get onlyCountSovereign => 'Računaj samo suverene zemlje';

  @override
  String get countUkSeparately => 'Računaj UK Zemlje odvojeno';

  @override
  String get showLegend => 'Prikaži Legendu';

  @override
  String get showLivedPin => 'Prikaži Oznaku Živio';

  @override
  String get useMyColours => 'Koristi Moje Boje';

  @override
  String get mapColors => 'Boje Karte';

  @override
  String get traveller => 'Putnik';

  @override
  String get nightTraveller => 'Noćni Putnik';

  @override
  String get original => 'Original';

  @override
  String get explorer => 'Istraživač';

  @override
  String get weekender => 'Posjetitelj na Vikende';

  @override
  String get naturalist => 'Obožavatelj Prirode';

  @override
  String get historian => 'Povjesničar';

  @override
  String get thrillSeeker => 'Tragač za Uzbuđenjima';

  @override
  String get culturalBuff => 'Obožavatelj Kulture';

  @override
  String get myColors => 'Moje Boje';

  @override
  String get experiences => 'Iskustva';

  @override
  String get done => 'Gotovo';

  @override
  String get experiencesInstructions => 'Pritisnite gumb + da započnete!';

  @override
  String get continueText => 'Nastavi';

  @override
  String get experiencesDescription => 'Što volite raditi kada putujete?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Moja Karta Putovanja';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Vidio sam $percentage% svijeta';
  }

  @override
  String get requiresOnline =>
      'Nažalost, Visited zahtijeva aktivnu povezanost na mrežu. Otvorite aplikaciju za postavke i osigurajte da su omogućeni \\r Wi-Fi ili mobilni podaci i da je onemogućen način rada u zrakoplovu';

  @override
  String get list => 'Popis';

  @override
  String get more => 'Više';

  @override
  String get myCountrySelections => 'Odabiri Moje Zemlje';

  @override
  String get cities => 'Gradovi';

  @override
  String get citiesInstructions =>
      'Dodirnite bilo koju zemlju da biste započeli odabir gradova.';

  @override
  String get missingCitiesEmailTitle => 'Gradovi koji Nedostaju!';

  @override
  String get lists => 'Popisi';

  @override
  String get disputedTerritories => 'Sporna područja';

  @override
  String get sponsored => 'Sponzorirano';

  @override
  String get places => 'Mjesta';

  @override
  String get noListsError =>
      'Ups, trenutno nema raspoloživih popisa, pokušajte malo kasnije';

  @override
  String get noInspirationsError =>
      'Ups, trenutno nema dostupnih fotografija, pokušajte malo kasnije';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Vaše najčešće posjećene zemlje:';

  @override
  String get update => 'ažuriranje';

  @override
  String get signup => 'Prijavite se';

  @override
  String get loginWallSubtitle =>
      'Vytvořte si bezplatný účet pro plné znění Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Po zavření aplikace ztratíte všechny výběry.';

  @override
  String get createAccount => 'Vytvořit účet';

  @override
  String get continueWithoutAccount => 'Pokračovat bez účtu';

  @override
  String get inspirationPromotion =>
      'Inspirují se krásnou cestovatelou fotografií';

  @override
  String get saveStatsPromotion => 'Uložte si cestovní statistiky!';

  @override
  String get selectRegionsPromotion => 'Vybrat státy a provincie';

  @override
  String get experiencesPromotion => 'Sledujte zážitky po celém světě';

  @override
  String get missingListItem =>
      'Jesmo li nešto propustili? Dodirnite ovdje da nam pošaljete e -poštu kako biste dodali svoje omiljeno mjesto.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Nedostaju stavka s $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Posjetio sam $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Dijelite kartu';

  @override
  String get posterLandingPageTitle => 'Nabavite svoj poster';

  @override
  String get posterNotAvailableError =>
      'Kupnja plakata trenutno nije dostupna. Molimo pokušajte ponovo kasnije.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping isporuka';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## O našim karatama prilagođenih ispisa\nIspišite svoju personaliziranu kartu svijeta. Prilagodite ga vlastitim bojama i isporučite ga ravno u vaš dom.\n \n### Tehnički podaci:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Pejzažna orijentacija.\n- Mikro tinta, kapljice za precizne otiske, 8 bitne boje, gotovo kvaliteta ispisa fotografija,\n- Saten papir debljine 0,22 mm\n\n### Detalji dostave:\nDostava iz Toronta u Kanadi do bilo kojeg mjesta u svijetu koristeći Canada Post. Dopustite 2 do 4 tjedna za dostavu većine odredišta. Sve se narudžbe otpremaju u kartonsku kutiju cijevi na podnesenu adresu za dostavu. Sva plaćanja upravljaju Apple Pay, ili Stripe.\n\n\n### Otkazivanje/povrat:\nNarudžbe se obrađuju odmah nakon što su podneseni na najbrži mogući zaokret. Stoga nema dostupnog povrata/otkaza.';

  @override
  String get posterDescriptionMarkdown =>
      '## O našim karatama prilagođenih ispisa\nIspišite svoju personaliziranu kartu svijeta. Prilagodite ga vlastitim bojama i isporučite ga ravno u vaš dom.\n \n### Tehnički podaci:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Pejzažna orijentacija.\n- Mikro tinta, kapljice za precizne otiske, 8 bitne boje, gotovo kvaliteta ispisa fotografija,\n- Saten papir debljine 0,22 mm\n\n### Detalji dostave:\nDostava iz Toronta u Kanadi do bilo kojeg mjesta u svijetu koristeći Canada Post. Dopustite 2 do 4 tjedna za dostavu većine odredišta. Sve se narudžbe otpremaju u kartonsku kutiju cijevi na podnesenu adresu za dostavu. Sva plaćanja upravljaju Google Pay ili Stripe.\n\n\n### Otkazivanje/povrat:\nNarudžbe se obrađuju odmah nakon što su podneseni na najbrži mogući zaokret. Stoga nema dostupnog povrata/otkaza.';

  @override
  String get posterCustomizeTitle => 'Prilagodite plakat';

  @override
  String get enterShippingAddress => 'Unesite adresu za dostavu';

  @override
  String get price => 'Cijena';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + porez';
  }

  @override
  String get showSelections => 'Show odabir';

  @override
  String get posterNoRefunds => 'Nakon tiskanja plakata nisu dostupni povrat.';

  @override
  String get posterReviewOrder => 'Pregledajte svoju narudžbu';

  @override
  String get email => 'E -pošta';

  @override
  String get emailEmptyError => 'Unesite svoju e -poštu';

  @override
  String get fullName => 'Puno ime';

  @override
  String get fullNameEmptyError => 'Molimo unesite svoje puno ime';

  @override
  String get streetAddressEmptyError => 'Unesite svoju adresu ulice';

  @override
  String get cityEmptyError => 'Unesite svoj grad';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Unesite svoj $fieldName';
  }

  @override
  String get country => 'Zemlja';

  @override
  String get countryEmptyError => 'Unesite svoju zemlju';

  @override
  String get posterReviewOrderTitle => 'Pregledajte svoju narudžbu';

  @override
  String get buyNow => 'Kupi sada';

  @override
  String get secureCheckoutDisclaimer => 'Sigurna odjava koju pruža Stripe';

  @override
  String get total => 'Ukupno';

  @override
  String get tax => 'Porez';

  @override
  String get subtotal => 'Subtotalan';

  @override
  String get posterProductName => 'Prilagođeni plakat s kartom';

  @override
  String get shipping => 'dostava';

  @override
  String get posterOrderReceivedTitle => 'Narudžba primljena';

  @override
  String get posterOrderReceivedSubtitle => 'Primili smo vašu narudžbu!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Provjerite svoju e -poštu za više ažuriranja. \nMolimo dopustite do 4 tjedna da stigne vaš plakat. \nAko imate bilo kakvih pitanja, pošaljite nam e -poštu na [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Tiskani status narudžbe plakata';

  @override
  String get moreInfo => 'Više informacija';

  @override
  String get logoutConfirm => 'Želite li se prijaviti iz aplikacije?';

  @override
  String get emailNotAvailable => 'Ta je e -pošta uzeta.';

  @override
  String get alphabetical => 'Abecedan';

  @override
  String get firstTimeLiveTutorial =>
      'Osiguravanje vaše matične zemlje i grada personalizirat će vaše iskustvo aplikacija.';

  @override
  String get firstTimeBeenTutorial =>
      'Odabir mjesta na kojem ste bili omogućavali vam da pregledate svoju kartu svih zemalja na kojima ste bili i vidjeli osobne statistike.';

  @override
  String get progressTooltipGoal =>
      'Vaši ciljevi putovanja temelje se na broju zemalja koje \"želite\" putovati u usporedbi sa zemljama u kojima ste \"bili\".';

  @override
  String get progressTooltipRank =>
      'Ovaj broj pokazuje kako se rangirate u usporedbi s putnicima širom svijeta. Svoj rang možete povećati putovanjem u više zemalja.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Ovaj se grafikon temelji na broju zemalja u kojima ste bili u usporedbi s ukupnim zemljama svijeta.';

  @override
  String get sortBy => 'Sortirati';

  @override
  String get updateWishlist => 'Ažuriraj listu želja';

  @override
  String get mapInfo =>
      'Kliknite na zemlju da biste odabrali, želite ili živjeli. Također možete kliknuti na ikonu koja se nalazi u gornjem lijevom kutu za prikaz popisa.';

  @override
  String get oneTimePurchase => 'Sve je jednokratna kupnja!';

  @override
  String get contact => 'Kontakt';

  @override
  String get contactUs => 'Kontaktirajte nas';

  @override
  String get noCitiesSelected => 'Još niste odabrali nijedan gradovi ...';

  @override
  String get updateTravelGoal => 'Ažurirajte cilj putovanja';

  @override
  String get travelGoalComplete =>
      'Čestitamo! \n\nyou završili ste svoj cilj putovanja! \n\ntap gumb + za dodavanje više zemalja.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Ne postoji račun povezan s e -poštom $email. Želite li ga stvoriti sada?';
  }

  @override
  String get tryAgain => 'Pokušajte ponovno';

  @override
  String get itineraries => 'Planovi putovanja';

  @override
  String get itinerary => 'Plan putovanja';

  @override
  String get place => 'Mjesto';

  @override
  String get itinerariesDescription =>
      'Ovo su mjesta za koja ste izrazili zanimanje.\nKoristite ovaj vodič kao pomoć pri planiranju vašeg sljedećeg odmora.';

  @override
  String get addMore => 'Dodaj još';

  @override
  String get interests => 'Interesi';

  @override
  String get selection => 'Izbor';

  @override
  String get goal => 'Cilj';

  @override
  String get noItineraries => 'Nema Itinerara';

  @override
  String get noItinerariesExplanation =>
      'Molimo dodajte neka mjesta, inspiracije ili iskustva kako biste vidjeli automatsko generiranje vaših itinerara.';

  @override
  String get clusterPins => 'Grupiranje pinova';

  @override
  String get toggleRegions => 'Prikaži regije pri zumiranju';

  @override
  String get mapProjection => 'Mapa projekcija';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Ekvirektangularni';

  @override
  String get yourTravellerType => 'Vaš tip putnika:';

  @override
  String get yourHotelPreferences => 'Vaše hotelske preferencije:';

  @override
  String get budget => 'Proračun';

  @override
  String get midScale => 'Srednja klasa';

  @override
  String get luxury => 'Luksuz';

  @override
  String get noTravellerType =>
      'Dodajte stavke na svoju listu želja kako biste otkrili kakav ste putnik.';

  @override
  String get unlockLived => 'Otključaj Proživljeno';

  @override
  String get unlockLivedDescription =>
      'Odaberite na karti gdje ste prethodno živjeli!';

  @override
  String get futureFeaturesDescription => '...i sve buduće značajke';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Vaša najčešće posjećena zemlja:';

  @override
  String get departureDate => 'Datum polaska';

  @override
  String get returnDate => 'Datum povratka';

  @override
  String get hotels => 'Hoteli';

  @override
  String get food => 'Hrana';

  @override
  String get travelDates => 'Datumi putovanja';

  @override
  String get posterForMe => 'Za mene';

  @override
  String get posterSendGift => 'Pošalji dar';

  @override
  String get addSelections => 'Dodaj odabire';

  @override
  String get posterType => 'Vrsta plakata';

  @override
  String get help => 'Pomoć';

  @override
  String get tutorialMap => 'Dodirnite državu za odabir: bio, želio i živio.';

  @override
  String get tutorialMapList =>
      'Dodirnite ikonu popisa (gornji lijevi kut) za odabir po popisu.';

  @override
  String get tutorialCountryDetails =>
      'Dodirnite zemlju, a zatim \"više\" za odabir po regiji.';

  @override
  String get tutorialItems =>
      'Pomaknite prekidač da odaberete kako želite odabrati stavke.';

  @override
  String get tutorialInspirations =>
      'Prijeđite prstom udesno ili ulijevo za prelazak na sljedeću karticu.';

  @override
  String get lifetime => 'Doživotno';

  @override
  String get chooseYourPlan => 'Odaberite svoj plan';

  @override
  String get requestARefund => 'Zatražite povrat novca';

  @override
  String get noPurchasesFound => 'Nema pronađenih kupnji.';

  @override
  String get noProductsAvailable => 'Nema dostupnih proizvoda';

  @override
  String get posterLandingAppBar => 'Donesite svoje priče kući';

  @override
  String get posterLandingSubHeading => 'Vaše putovanje, vaša priča';

  @override
  String get posterLandingSubDescription =>
      'Vaša su putovanja više od putovanja, ona su priče, sjećanja i prekretnice. Pretvorite te nezaboravne trenutke u personaliziranu kartu svijeta koja je jedinstvena kao i vaše avanture.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Karta vaših postignuća: Istaknite svako odredište, od vašeg prvog velikog putovanja do vaše najhrabrije avanture.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Proslavite svako putovanje: svakodnevno ponovno proživite svoja putovanja s prekrasno izrađenim posterom dizajniranim da vas inspirira.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Poklon koji će cijeniti: Iznenadite suputnika prilagođenom kartom koja prikazuje njihovo putovanje, savršenom za rođendane, prekretnice ili jednostavno zato.';

  @override
  String get posterLandingHowItWorks => 'Kako radi!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Prilagodite svoj dizajn: Odaberite boje, stilove i označite svoja putovanja (ili njihova!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Pregledajte svoju kartu: pogledajte kako oživljava prije vaše narudžbe.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Sigurno plaćanje: Brzo i sigurno uz Apple Pay ili Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Sigurno plaćanje: Brzo i sigurno uz Google Pay ili Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Spremno za izlaganje: Poslat ćemo ga ravno na vaša (ili njihova) vrata.';

  @override
  String get posterLandingCustomerReviewsHeader => 'Iskustva suputnika';

  @override
  String get posterLandingCustomerReview1 =>
      'Ova je karta sjajan način da pratim kamo god sam putovao i planiram naša buduća putovanja. Kvaliteta je solidna i izgleda odlično dok visi u mom uredu. Čak sam nabavio jednu za svog brata i on nije mogao, prestani pričati o tome kako je super! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Putovao sam u više od 150 luka dok sam radio na krstarenju. Ova je karta izvrstan dodatak mojoj dnevnoj sobi kao uspomena na sve godine provedene na moru. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Sjajan poklon za Majčin dan. Moja mama je bila dirnuta! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Ispisao sam kartu mjesta koja sam htio posjetiti sa svojom curom. Bio je to izvrstan božićni dar. Također visoke kvalitete. Brad J.';

  @override
  String get posterLandingSpecifications => 'Tehnički podaci';

  @override
  String get posterLandingSpecification1 =>
      '• Dimenzije: 16\" x 20\" (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '• Orijentacija: pejzaž';

  @override
  String get posterLandingSpecification3 =>
      '• Kvaliteta ispisa: Mikro tinta, kapljice za precizne ispise.  8-bitna boja, kvaliteta ispisa gotovo fotografija.';

  @override
  String get posterLandingSpecification4 =>
      '• Papir: satin papir debljine 0,22 mm';

  @override
  String get posterLandingShippingHeader => 'Detalji o otpremi';

  @override
  String get posterLandingShipping1 =>
      '• Dostava iz Toronta, Kanada, bilo gdje u svijetu putem Kanadske pošte.';

  @override
  String get posterLandingShipping2 =>
      '• Pričekajte 2-4 tjedna za dostavu na većinu odredišta.';

  @override
  String get posterLandingShipping3 =>
      '• Sve se narudžbe zamotaju u kartonsku cijevnu kutiju na adresu za dostavu koju pošaljete.';

  @override
  String get posterLandingCancellationHeader => 'Otkazivanje/povrat:';

  @override
  String get posterLandingCancellationBody =>
      'Povrati su dostupni prije nego što se vaš poster pošalje u tiskaru, što može potrajati do 24 sata.  Nakon što se vaša narudžba obradi, povrat/otkazivanje nije moguće.  Primit ćete e-poruku kada vaša narudžba bude ispisana.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
