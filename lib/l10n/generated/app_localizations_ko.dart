// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => '언어';

  @override
  String get pickEmailApp => '이메일 앱을 선택하세요';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return '저는 $amount개의 국가를 방문했어요! 당신은 몇 개를 방문했나요? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return '저는 $amount개의 도시를 방문했어요! 당신은 몇 개를 방문했나요? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return '저는 $amount개의 $listName를 방문했어요! 당신은 몇 개를 방문했나요? www.visitedapp.com';
  }

  @override
  String get clear => '지우기';

  @override
  String get been => '가본';

  @override
  String get want => '원하다';

  @override
  String get live => '라이브';

  @override
  String get lived => '살았습니다';

  @override
  String get water => '물';

  @override
  String get land => '땅';

  @override
  String get borders => '국경';

  @override
  String get labels => '레이블';

  @override
  String get legend => '범례';

  @override
  String get inspiration => '영감';

  @override
  String get inspirations => '영감들';

  @override
  String get delete => '삭제';

  @override
  String get unlockVisitedUpsellTitle => '더 보고 싶으십니까?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      '모든 기능을 잠금 해제하시고 Visited를 온전히 즐기십시오';

  @override
  String get checkTheDetails => '자세히 보기';

  @override
  String get moreInspirationsComingSoon =>
      '더 많은 이미지를 가져 오고 있습니다. 나중에 다시 확인하십시오!';

  @override
  String get unlockPremiumFeatures => '프리미엄 기능 잠금 해제';

  @override
  String get purchased => '구매 됐습니다!';

  @override
  String get buy => '구매하기';

  @override
  String get restorePurchases => '구매 복구하기';

  @override
  String get ok => '확인';

  @override
  String get areYouSure => '확실합니까?';

  @override
  String get deleteInspirationConfirmMessage =>
      '본 카드 삭제는 영구적입니다. 이미지를 복구 할 방법은 없습니다.';

  @override
  String get cancel => '취소';

  @override
  String get map => '지도';

  @override
  String get progress => '진행';

  @override
  String get myTravelGoal => '내 여행 목표';

  @override
  String goalRemaining(int remaining) {
    return '$remaining 남았습니다!';
  }

  @override
  String get top => '상위';

  @override
  String get ofTheWorld => '세계에서 … !';

  @override
  String get countries => '나라들';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return '$country에서 제일 많이 방문한 나라들 :';
  }

  @override
  String get login => '로그인';

  @override
  String get logout => '로그아웃';

  @override
  String get enterYourEmail => '이메일을 입력하십시오';

  @override
  String get privacyPolicy => '개인정보보호정책';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-korean/';

  @override
  String get termsOfUse => '이용약관';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-korean/';

  @override
  String get errorTitle => '아이고!';

  @override
  String get enterValidEmail => '유효한 이메일을 입력하십시오';

  @override
  String get settings => '설정';

  @override
  String get whereDoYouLive => '어디에 사십니까?';

  @override
  String get whereHaveYouBeen => '어디를 방문 해보셨습니까?';

  @override
  String get whereDoYouFlyFrom => '어디서 비행기를 타십니까?';

  @override
  String get next => '다음';

  @override
  String get missingAirports =>
      '찾고 있는것이 없나요? ****************************으로 이메일 보내주세요.';

  @override
  String get missingAirportsEmailTitle => '공항을 찾지 못했습니다!';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => 'Visited에 오신것을 환영합니다';

  @override
  String get welcomeSubtitle => '일생의 모험이 기다립니다';

  @override
  String get getStarted => '시작하기';

  @override
  String get privacyAgreement => '개인정보보호정책';

  @override
  String get privacyAgreementSubtitle => 'Visited를 계속 사용하기 전에 당므에 동의하십시오.';

  @override
  String get privacyAgreementTermsMarkdown =>
      '이 상자를 체크하므로써 Arriving in High Heels의 [개인정보정책](https://www.arrivinginhighheels.com/privacy-policy)과 [이용약권](https://www.arrivinginhighheels.com/terms-of-use)을 확인하였고 동의함을 인정합니다.';

  @override
  String get privacyAgreementOptIn =>
      '본인은 판매, 판촉, 세일 및 뉴스레터를 포함한 내가 관심이 있을 수 있는 제품, 앱 및 서비스에 대한 정보와 판매에 대한 이메일을 Arriving in High Heels로부터 수신하는 데 동의합니다. 본인은 개인정보보호정책에 설명 된대로, 혹은 이메일의 \"구독 취소\" 링크를 클릭하여 언제든지 본 동의를 철회 할 수 있습니다.';

  @override
  String get submit => '제출';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired => 'Visited를 계속 사용하려면 약관에 모두 동의해주십시오.';

  @override
  String get deleteAccount => '계정 삭제';

  @override
  String get removeAdsUpsell => '대신 관고를 해제하고 이메일 마케팅 수신을 취소 하시겠습니까?';

  @override
  String get deleteAccountWarning =>
      '계정을 삭제하므로서 귀하의 모든 정보를 본사의 서버에서 삭제합니다. 이것은 취소 할 수 없습니다.';

  @override
  String get about => '정보';

  @override
  String get popularity => '인기도';

  @override
  String get regions => '지역';

  @override
  String get population => '인구';

  @override
  String get size => '면적';

  @override
  String get coverage => '범위';

  @override
  String get percentOfCountryVisited => '방문 한 나라 %';

  @override
  String get visited => '방문하다';

  @override
  String get notes => '메모';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => '맞춤 설정';

  @override
  String get onlyCountSovereign => '주권 국가만 세기';

  @override
  String get countUkSeparately => '영국 나라 따로 보기';

  @override
  String get showLegend => '범례 보기';

  @override
  String get showLivedPin => '라이브 핀 보기';

  @override
  String get useMyColours => '내 색깔 사용하기';

  @override
  String get mapColors => '지도 색깔';

  @override
  String get traveller => '여행자';

  @override
  String get nightTraveller => '심야 여행자';

  @override
  String get original => '독창자';

  @override
  String get explorer => '탐험가';

  @override
  String get weekender => '주말 여행자';

  @override
  String get naturalist => '자여주의자';

  @override
  String get historian => '사학자';

  @override
  String get thrillSeeker => '스릴 추구자';

  @override
  String get culturalBuff => '사회 애호가';

  @override
  String get myColors => '내 색깔 사용하기';

  @override
  String get experiences => '경험들';

  @override
  String get done => '완료';

  @override
  String get experiencesInstructions => '\"+ 버튼을 눌러 시작하십시오!\"';

  @override
  String get continueText => '계속하기';

  @override
  String get experiencesDescription => '여행 할 때 무엇을 즐기십니까?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-%ec%97%ac%ed%96%89-%ec%95%b1/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => '내 여행 지도';

  @override
  String percentOfWorldSeen(int percentage) {
    return '난 지구의 $percentage%를 봤다!';
  }

  @override
  String get requiresOnline =>
      '죄송합니다, Visited는 활성화된 네트워크 연결이 필요합니다. 세팅 앱을 열어 와이파이나 데이터가 활성화 되어 있고 비행기 모드가 비활성화 되어있는지 확인 하십시오.';

  @override
  String get list => '목록';

  @override
  String get more => '더';

  @override
  String get myCountrySelections => '내 선택된 나라들';

  @override
  String get cities => '도시들';

  @override
  String get citiesInstructions => '아무 나라를 눌러 도시들을 선택하십시오.';

  @override
  String get missingCitiesEmailTitle => '도시들을 찾지 못했습니다!';

  @override
  String get lists => '기울기';

  @override
  String get disputedTerritories => '분쟁 지역';

  @override
  String get sponsored => '스폰서';

  @override
  String get places => '위치들';

  @override
  String get noListsError => '죄송합니다, 현재 목록을 이용 할 수 없습니다, 나중에 다시 시도 하십시오.';

  @override
  String get noInspirationsError =>
      '죄송합니다, 현재 사진을 이용 할 수 없습니다, 나중에 다시 시도 하십시오.';

  @override
  String get mostFrequentlyVisitedCountries => '가장 자주 방문하는 국가:';

  @override
  String get update => '최신 정보';

  @override
  String get signup => '가입하기';

  @override
  String get loginWallSubtitle => 'Visited 의 전체 버전을 경험하기 위해 무료 계정을 만듭니다.';

  @override
  String get loseAllSelectionsWarning => '앱을 닫은 후 모든 선택을 잃게 됩니다.';

  @override
  String get createAccount => '계정 만들기';

  @override
  String get continueWithoutAccount => '계정 없이 계속';

  @override
  String get inspirationPromotion => '아름다운 여행 사진에 영감을 얻으십시오.';

  @override
  String get saveStatsPromotion => '여행 통계를 저장하세요!';

  @override
  String get selectRegionsPromotion => '주 및 지방 선택';

  @override
  String get experiencesPromotion => '전 세계 체험 추적';

  @override
  String get missingListItem =>
      '우리는 뭔가 놓쳤습니까? 좋아하는 장소가 추가되도록 이메일을 보내려면 여기를 탭하십시오.';

  @override
  String missingListItemEmailTitle(String list) {
    return '$list에서 누락 된 항목';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return '$amount $listName 을 방문했습니다.';
  }

  @override
  String get orderPoster => '포스터';

  @override
  String get shareMap => '맵을 공유하십시오';

  @override
  String get posterLandingPageTitle => '포스터 받기';

  @override
  String get posterNotAvailableError =>
      '포스터 구매는 현재 사용할 수 없습니다. 나중에 다시 시도 해주십시오.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping 배송';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## 맞춤형 인쇄 맵에 대해\n개인화 된 세계지도를 인쇄하십시오. 자신의 색상으로 사용자 정의하고 집으로 바로 전달하십시오.\n \n### 명세서:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- 조경 방향.\n- 마이크로 잉크, 정확한 인쇄 용 드롭 레트, 8 비트 컬러, 거의 사진 인쇄 품질,\n- 0.22mm 두께의 새틴 종이\n\n### 배송 정보:\n캐나다 포스트를 사용하여 캐나다 토론토에서 전 세계 어디에서나 배송. 대부분의 목적지로 배달하려면 2 ~ 4 주를 허용하십시오. 모든 주문은 골판지 튜브 상자에 제출 된 배송 주소로 배송됩니다. \n 지불 처리 방법은 어떻게 처리됩니까? 모든 지불은 Apple Pay, 또는 Stripe에 의해 처리됩니다.\n\n\n### 취소/환불 :\n주문은 가능한 가장 빠른 처리 시간을 위해 제출 된 직후에 처리됩니다. 따라서 환불/취소를 사용할 수 없습니다.';

  @override
  String get posterDescriptionMarkdown =>
      '## 맞춤형 인쇄 맵에 대해\n개인화 된 세계지도를 인쇄하십시오. 자신의 색상으로 사용자 정의하고 집으로 바로 전달하십시오.\n \n### 명세서:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- 조경 방향.\n- 마이크로 잉크, 정확한 인쇄 용 드롭 레트, 8 비트 컬러, 거의 사진 인쇄 품질,\n- 0.22mm 두께의 새틴 종이\n\n### 배송 정보:\n캐나다 포스트를 사용하여 캐나다 토론토에서 전 세계 어디에서나 배송. 대부분의 목적지로 배달하려면 2 ~ 4 주를 허용하십시오. 모든 주문은 골판지 튜브 상자에 제출 된 배송 주소로 배송됩니다. \n 지불 처리 방법은 어떻게 처리됩니까? 모든 지불은 Google Pay 또는 Stripe에 의해 처리됩니다.\n\n\n### 취소/환불 :\n주문은 가능한 가장 빠른 처리 시간을 위해 제출 된 직후에 처리됩니다. 따라서 환불/취소를 사용할 수 없습니다.';

  @override
  String get posterCustomizeTitle => '포스터를 사용자 정의하십시오';

  @override
  String get enterShippingAddress => '배송 주소를 입력하십시오';

  @override
  String get price => '가격';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + 세금';
  }

  @override
  String get showSelections => '선택을 표시하십시오';

  @override
  String get posterNoRefunds => '포스터가 인쇄 된 후에는 환불을 사용할 수 없습니다.';

  @override
  String get posterReviewOrder => '주문 확인하기';

  @override
  String get email => '이메일';

  @override
  String get emailEmptyError => '이메일을 입력하십시오';

  @override
  String get fullName => '전체 이름';

  @override
  String get fullNameEmptyError => '당신의 성명을 입력 해주세요';

  @override
  String get streetAddressEmptyError => '거리 주소를 입력하십시오';

  @override
  String get cityEmptyError => '당신의 도시에 들어가십시오';

  @override
  String fieldEmptyError(Object fieldName) {
    return '$fieldName을 입력하십시오.';
  }

  @override
  String get country => '국가';

  @override
  String get countryEmptyError => '당신의 나라를 입력하십시오';

  @override
  String get posterReviewOrderTitle => '주문 확인하기';

  @override
  String get buyNow => '지금 구매하십시오';

  @override
  String get secureCheckoutDisclaimer => 'Stripe에서 제공하는 보안 체크 아웃';

  @override
  String get total => '총';

  @override
  String get tax => '세';

  @override
  String get subtotal => '소계';

  @override
  String get posterProductName => '사용자 정의 방문 맵 포스터';

  @override
  String get shipping => '배송';

  @override
  String get posterOrderReceivedTitle => '받은 주문';

  @override
  String get posterOrderReceivedSubtitle => '우리는 당신의 주문을 받았습니다!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      '이메일에서 더 많은 업데이트를 확인하세요. \n포스터가 도착하는 데 최대 4주가 소요됩니다. \n질문이 있으시면 [<EMAIL>](<EMAIL>)으로 이메일을 보내주세요.';

  @override
  String get posterOrderReceivedEmailSubject => '인쇄 된 포스터 주문 상태';

  @override
  String get moreInfo => '추가 정보';

  @override
  String get logoutConfirm => '앱에서 로그 아웃 하시겠습니까?';

  @override
  String get emailNotAvailable => '그 이메일이 가져 왔습니다.';

  @override
  String get alphabetical => '알파벳';

  @override
  String get firstTimeLiveTutorial => '본국과 도시를 제공하면 앱 경험을 개인화합니다.';

  @override
  String get firstTimeBeenTutorial =>
      '당신이 가진 곳을 선택하면 모든 국가의지도를보고 개인 통계를 볼 수 있습니다.';

  @override
  String get progressTooltipGoal =>
      '여행 목표는 \"원했던\"국가에 비해 여행하기를 원하는 국가의 수를 기준으로합니다.';

  @override
  String get progressTooltipRank =>
      '이 숫자는 전 세계 여행자에 비해 순위를 매기는 방법을 보여줍니다. 더 많은 국가로 여행함으로써 순위를 높일 수 있습니다.';

  @override
  String get progressTooltipPercentageOfWorld =>
      '이 그래프는 세계 전체 국가와 비교 한 국가의 수를 기반으로합니다.';

  @override
  String get sortBy => '정렬 기준';

  @override
  String get updateWishlist => '위시리스트 업데이트';

  @override
  String get mapInfo =>
      '국가를 클릭하여 선택, 원하거나 살고 있습니다. 목록보기의 왼쪽 상단 코너에있는 아이콘을 클릭 할 수도 있습니다.';

  @override
  String get oneTimePurchase => '모든 것이 한 번 구매입니다!';

  @override
  String get contact => '연락하다';

  @override
  String get contactUs => '문의하기';

  @override
  String get noCitiesSelected => '아직 도시를 선택하지 않았지만 ...';

  @override
  String get updateTravelGoal => '여행 목표를 업데이트하십시오';

  @override
  String get travelGoalComplete =>
      '축하합니다! \n\nyou 여행 목표를 마쳤습니다! \n\ntap + 버튼을 찍어 더 많은 국가를 추가하십시오.';

  @override
  String loginEmailNotFoundError(String email) {
    return '이메일 $email과 관련된 계정이 없습니다. 지금 만들고 싶습니까?';
  }

  @override
  String get tryAgain => '다시 시도하기';

  @override
  String get itineraries => '여행 계획';

  @override
  String get itinerary => '여행 계획';

  @override
  String get place => '장소';

  @override
  String get itinerariesDescription =>
      '회원님이 관심을 표명한 장소입니다.\n이 가이드를 참고하여 다음 휴가를 계획하세요.';

  @override
  String get addMore => '더 보기';

  @override
  String get interests => '이해';

  @override
  String get selection => '선택';

  @override
  String get goal => '목표';

  @override
  String get noItineraries => '여행 계획 없음';

  @override
  String get noItinerariesExplanation =>
      '일정을 자동으로 생성하려면 몇 군데의 장소, 영감 또는 경험을 추가해 주세요.';

  @override
  String get clusterPins => '클러스터 지도 마커';

  @override
  String get toggleRegions => '지역 확대/축소';

  @override
  String get mapProjection => '지도 투영';

  @override
  String get mercator => '메르카토르';

  @override
  String get equirectangular => '등적 직사각형';

  @override
  String get yourTravellerType => '당신의 여행자 유형:';

  @override
  String get yourHotelPreferences => '호텔 선호도:';

  @override
  String get budget => '예산';

  @override
  String get midScale => '중간 규모';

  @override
  String get luxury => '럭셔리';

  @override
  String get noTravellerType => '버킷 리스트에 항목을 추가하여 어떤 유형의 여행자인지 알아보세요.';

  @override
  String get unlockLived => '거주 해제';

  @override
  String get unlockLivedDescription => '이전에 살았던 곳을 지도에서 선택하세요!';

  @override
  String get futureFeaturesDescription => '...그리고 모든 미래 기능';

  @override
  String get yourMostFrequentlyVisitedCountry => '가장 자주 방문하는 국가:';

  @override
  String get departureDate => '출발 날짜';

  @override
  String get returnDate => '귀국 날짜';

  @override
  String get hotels => '호텔';

  @override
  String get food => '음식';

  @override
  String get travelDates => '여행 날짜';

  @override
  String get posterForMe => '나를 위한';

  @override
  String get posterSendGift => '선물 보내기';

  @override
  String get addSelections => '선택 항목 추가';

  @override
  String get posterType => '포스터 유형';

  @override
  String get help => '돕다';

  @override
  String get tutorialMap => '국가를 탭하여 과거, 원함, 살았음 중에서 선택하세요.';

  @override
  String get tutorialMapList => '목록별로 선택하려면 목록 아이콘(왼쪽 상단)을 탭하세요.';

  @override
  String get tutorialCountryDetails => '국가를 탭한 다음 \'더 보기\'를 탭하여 지역별로 선택하세요.';

  @override
  String get tutorialItems => '토글을 밀어 항목 선택 방법을 선택하세요.';

  @override
  String get tutorialInspirations => '다음 카드로 이동하려면 오른쪽이나 왼쪽으로 스와이프하세요.';

  @override
  String get lifetime => '生涯';

  @override
  String get chooseYourPlan => 'プランを選択してください';

  @override
  String get requestARefund => '返金をリクエストする';

  @override
  String get noPurchasesFound => '購入が見つかりません。';

  @override
  String get noProductsAvailable => '利用可能な製品はありません';

  @override
  String get posterLandingAppBar => '당신의 이야기를 집으로';

  @override
  String get posterLandingSubHeading => '당신의 여행, 당신의 이야기';

  @override
  String get posterLandingSubDescription =>
      '당신의 여행은 단순한 여행 그 이상입니다. 이야기이자 추억이며, 중요한 이정표입니다. 잊지 못할 순간들을 당신의 모험만큼이나 특별한 나만의 세계 지도로 만들어 보세요.';

  @override
  String get posterLandingPromoBullet1 =>
      '• 당신의 성취를 담은 지도: 첫 번째 큰 여행부터 가장 대담한 모험까지, 모든 목적지를 강조하세요.';

  @override
  String get posterLandingPromoBullet2 =>
      '• 모든 여정을 기념하세요: 영감을 주는 아름답게 제작된 포스터로 매일 여행을 다시 경험하세요.';

  @override
  String get posterLandingPromoBullet3 =>
      '• 소중한 선물: 생일, 중요한 순간, 또는 그저 특별한 날에 어울리는, 여정을 담은 맞춤 지도로 함께 여행하는 사람을 놀라게 하세요.';

  @override
  String get posterLandingHowItWorks => '이용 방법!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. 디자인 맞춤 설정: 색상과 스타일을 선택하고 당신의 여행(또는 상대방의 여행!)을 표시하세요.';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. 지도 미리보기: 주문 전에 지도가 실제로 구현되는 것을 확인하세요.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. 안전한 결제: Apple Pay 또는 Stripe로 빠르고 안전하게 결제하세요.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. 안전한 결제: Google Pay 또는 Stripe로 빠르고 안전하게 결제하세요.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. 진열 준비 완료: 귀하의 집(또는 상대방 집)까지 바로 배송해 드립니다.';

  @override
  String get posterLandingCustomerReviewsHeader => '동료 여행자의 경험담';

  @override
  String get posterLandingCustomerReview1 =>
      '“이 지도는 제가 여행한 모든 곳을 추적하고 앞으로의 여행을 계획할 수 있는 좋은 방법입니다.  품질도 좋고 사무실에 걸어두면 멋져 보이죠.  동생에게도 하나 사줬는데 얼마나 멋진지 입을 다물지 못하더라고요!” - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '“크루즈 업계에서 일하면서 150개 이상의 항구를 여행했습니다. 이 지도는 바다에서 보낸 세월을 추억하며 거실에 걸어두고 싶어요.” - 베티 K.';

  @override
  String get posterLandingCustomerReview3 =>
      '“어버이날을 위한 멋진 선물입니다. 엄마가 정말 감동하셨어요!” 사만다 W.';

  @override
  String get posterLandingCustomerReview4 =>
      '“여자친구와 함께 가보고 싶은 곳의 지도를 인쇄했어요. 훌륭한 크리스마스 선물이었어요. 품질도 좋았어요.” Brad J.';

  @override
  String get posterLandingSpecifications => '사양';

  @override
  String get posterLandingSpecification1 => '- 치수: 16“ x 20”(40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '- 방향: 가로';

  @override
  String get posterLandingSpecification3 =>
      '- 인쇄 품질: 마이크로 잉크, 정밀한 인쇄를 위한 잉크 방울.  8비트 컬러, 거의 사진 인쇄 품질.';

  @override
  String get posterLandingSpecification4 => '- 용지: 0.22mm 두께의 새틴 용지';

  @override
  String get posterLandingShippingHeader => '배송 세부 정보';

  @override
  String get posterLandingShipping1 =>
      '- 캐나다 토론토에서 캐나다 우체국을 사용하여 전 세계 어디로든 배송합니다.';

  @override
  String get posterLandingShipping2 => '- 대부분의 목적지로 배송하는 데 2~4주가 소요됩니다.';

  @override
  String get posterLandingShipping3 =>
      '- 모든 주문은 제출한 배송 주소로 골판지 튜브 상자에 말아서 배송됩니다.';

  @override
  String get posterLandingCancellationHeader => '취소/환불:';

  @override
  String get posterLandingCancellationBody =>
      '포스터가 프린터로 전송되기 전에 환불이 가능하며, 최대 24시간이 소요될 수 있습니다.  주문이 처리된 후에는 환불/취소가 불가능합니다.  주문이 인쇄되면 이메일을 받게 됩니다.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
