// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Italian (`it`).
class AppLocalizationsIt extends AppLocalizations {
  AppLocalizationsIt([String locale = 'it']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Lingua';

  @override
  String get pickEmailApp => 'Scegli la tua app di posta elettronica';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Ho visitato $amount paesi! Quanti ne hai visitati tu? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Ho visitato $amount città! Quante ne hai visitate tu? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Ho visitato $amount $listName! Quanti ne hai visitati tu? www.visitedapp.com';
  }

  @override
  String get clear => 'Cancella';

  @override
  String get been => 'Già stato';

  @override
  String get want => 'Voglio';

  @override
  String get live => 'Vivere';

  @override
  String get lived => 'Vivevo';

  @override
  String get water => 'Acqua';

  @override
  String get land => 'Terra';

  @override
  String get borders => 'Frontiere';

  @override
  String get labels => 'Etichette';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Ispirazione';

  @override
  String get inspirations => 'Ispirazioni';

  @override
  String get delete => 'Elimina';

  @override
  String get unlockVisitedUpsellTitle => 'Vuoi vedere di più?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Sblocca tutte le funzionalità e goditi Visited in tutta la sua forza';

  @override
  String get checkTheDetails => 'Controlla i Dettagli';

  @override
  String get moreInspirationsComingSoon =>
      'Stiamo lavorando per ottenere più immagini. Ricontrolla presto!';

  @override
  String get unlockPremiumFeatures => 'Sblocca le funzionalità premium';

  @override
  String get purchased => 'Acquistato!';

  @override
  String get buy => 'Acquista';

  @override
  String get restorePurchases => 'Ripristinare acquisto';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Sei sicuro?';

  @override
  String get deleteInspirationConfirmMessage =>
      'L\'eliminazione di questa carta è definitiva. Non c\'è modo di recuperare questa immagine.';

  @override
  String get cancel => 'Annulla';

  @override
  String get map => 'Mappa';

  @override
  String get progress => 'Progresso';

  @override
  String get myTravelGoal => 'll mio Obiettivo di Viaggio';

  @override
  String goalRemaining(int remaining) {
    return '$remaining da visitare!';
  }

  @override
  String get top => 'CIMA';

  @override
  String get ofTheWorld => 'del mondo!';

  @override
  String get countries => 'paesi';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Principali Paesi Visitati da $country:';
  }

  @override
  String get login => 'Accedi';

  @override
  String get logout => 'Disconnettersi';

  @override
  String get enterYourEmail => 'Inserisci il tuo indirizzo email';

  @override
  String get privacyPolicy => 'Politica sulla Privacy';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-italian/';

  @override
  String get termsOfUse => 'Termini di Utilizzo';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-italian/';

  @override
  String get errorTitle => 'Ops!';

  @override
  String get enterValidEmail => 'Si prega di inserire una email valida';

  @override
  String get settings => 'Impostazioni';

  @override
  String get whereDoYouLive => 'Dove vivi?';

  @override
  String get whereHaveYouBeen => 'Dove sei stato?';

  @override
  String get whereDoYouFlyFrom => 'Da dove voli?';

  @override
  String get next => 'Il prossimo';

  @override
  String get missingAirports =>
      'Non trovi quello che stai cercando? Inviaci una <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aeroporti Mancanti!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Benvenuto in Visited';

  @override
  String get welcomeSubtitle => 'L\'avventura di una vita ti aspetta';

  @override
  String get getStarted => 'Inizia';

  @override
  String get privacyAgreement => 'Accordo sulla Privacy';

  @override
  String get privacyAgreementSubtitle =>
      'Si prega di accettare i seguenti elementi prima di continuare a utilizzare Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Selezionando questa casella, dichiari di aver letto e di accettare di essere vincolato dall\'[Informativa sulla privacy] di Arriving in High Heels(https://www.arrivinginhighheels.com/privacy-policy) e dai [Termini di Utilizzo](https://www.arrivinginhighheels.com/privacy-policy) . //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Accetto di ricevere messaggi elettronici da Arriving in High Heels contenenti informazioni e offerte relative a prodotti, applicazioni e servizi che potrebbero interessarmi, inclusa la notifica di vendite, promozioni, offerte e newsletter. Posso revocare questo consenso in qualsiasi momento come descritto nella Politica sulla Privacy o facendo clic sul collegamento \"cancella iscrizione\" nei messaggi elettronici.';

  @override
  String get submit => 'Invia';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Devi accettare entrambi i nostri termini per continuare a utilizzare Visited.';

  @override
  String get deleteAccount => 'Elimina l\'account';

  @override
  String get removeAdsUpsell =>
      'Desideri disattivare gli annunci e annullare l\'iscrizione alle email di marketing?';

  @override
  String get deleteAccountWarning =>
      'L\'eliminazione del tuo account rimuoverà tutte le tue informazioni dai nostri server.\n \n Questo processo non può essere annullato.';

  @override
  String get about => 'Su di noi';

  @override
  String get popularity => 'Popolarità';

  @override
  String get regions => 'Regioni';

  @override
  String get population => 'Popolazione';

  @override
  String get size => 'Dimensione';

  @override
  String get coverage => 'Copertura';

  @override
  String get percentOfCountryVisited => '% del paese visitato';

  @override
  String get visited => 'visitato';

  @override
  String get notes => 'Appunti';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Personalizza';

  @override
  String get onlyCountSovereign => 'Conta Solo i Paesi Sovrani';

  @override
  String get countUkSeparately => 'Conta Separatamente i Paesi del Regno Unito';

  @override
  String get showLegend => 'Mostra Legenda';

  @override
  String get showLivedPin => 'Mostra il Pin vissuto';

  @override
  String get useMyColours => 'Usa i Miei Colori';

  @override
  String get mapColors => 'Colori Della Mappa';

  @override
  String get traveller => 'Il Viaggiatore';

  @override
  String get nightTraveller => 'Il Viaggiatore Notturno';

  @override
  String get original => 'L\'originale';

  @override
  String get explorer => 'L\'esploratore';

  @override
  String get weekender => 'Il Weekender';

  @override
  String get naturalist => 'Il Naturalista';

  @override
  String get historian => 'Lo Storico';

  @override
  String get thrillSeeker => 'Il Cercatore di Emozioni';

  @override
  String get culturalBuff => 'Il Buff Culturale';

  @override
  String get myColors => 'I Miei Colori';

  @override
  String get experiences => 'Esperienze';

  @override
  String get done => 'Fatto';

  @override
  String get experiencesInstructions => 'Tocca il pulsante + per iniziare!';

  @override
  String get continueText => 'Continua';

  @override
  String get experiencesDescription => 'Cosa ti piace fare quando viaggi?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/app-di-viaggio-visited/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'La Mia Mappa di Viaggio';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Ho visto il $percentage% del mondo';
  }

  @override
  String get requiresOnline =>
      'Spiacenti, Visited richiede una connessione di rete attiva. Apri l\'app delle impostazioni e assicurati che \\r Wi-Fi o Dati cellulare siano abilitati e che la modalità aereo sia disabilitata';

  @override
  String get list => 'Elenco';

  @override
  String get more => 'Di più';

  @override
  String get myCountrySelections => 'Le Mie Nazioni Selezionate';

  @override
  String get cities => 'Città';

  @override
  String get citiesInstructions =>
      'Tocca qualsiasi paese per iniziare a selezionare le città.';

  @override
  String get missingCitiesEmailTitle => 'Città Mancanti!';

  @override
  String get lists => 'Elenchi';

  @override
  String get disputedTerritories => 'Territori Contesi';

  @override
  String get sponsored => 'Sponsorizzato';

  @override
  String get places => 'Posti';

  @override
  String get noListsError =>
      'Opps, nessun elenco disponibile in questo momento, per favore riprova un po\' più tardi';

  @override
  String get noInspirationsError =>
      'Ops, al momento non sono disponibili foto, si prega di riprovare un po\' più tardi';

  @override
  String get mostFrequentlyVisitedCountries => 'I tuoi paesi più visitati:';

  @override
  String get update => 'Aggiornare';

  @override
  String get signup => 'Iscrizione';

  @override
  String get loginWallSubtitle =>
      'Crea un account gratuito per sperimentare la versione completa di Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Perderai tutte le tue selezioni dopo aver chiuso l\'app.';

  @override
  String get createAccount => 'Crea account';

  @override
  String get continueWithoutAccount => 'Continuare senza un account';

  @override
  String get inspirationPromotion =>
      'Ispiri con una bella fotografia di viaggio';

  @override
  String get saveStatsPromotion => 'Salva le tue statistiche di viaggio!';

  @override
  String get selectRegionsPromotion => 'Selezionare stati e province';

  @override
  String get experiencesPromotion =>
      'Tieni traccia delle esperienze in tutto il mondo';

  @override
  String get missingListItem =>
      'Ci siamo persi qualcosa? Tocca qui per inviarci un\'e -mail per aggiungere il tuo posto preferito.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Elemento mancante da $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Ho visitato $amount $listName';
  }

  @override
  String get orderPoster => 'Manifesto';

  @override
  String get shareMap => 'Condividi la mappa';

  @override
  String get posterLandingPageTitle => 'Ottieni il tuo poster';

  @override
  String get posterNotAvailableError =>
      'L\'acquisto di poster non è disponibile in questo momento. Per favore riprova più tardi.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping spedizione';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Sulle nostre mappe di stampa personalizzate\nStampa la tua mappa del mondo personalizzata. Personalizzalo con i tuoi colori e fallo consegnare direttamente a casa tua.\n \n### Specifiche:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientamento orizzontale.\n- Micro inchiostro, goccioline per stampe precise, colore a 8 bit, qualità quasi fotografica,\n- Carta satinata di spessore 0,22 mm\n\n### Dettagli di spedizione:\nSpedizione da Toronto, in Canada, in qualsiasi parte del mondo utilizzando Canada Post. Si prega di consentire da 2 a 4 settimane per la consegna nella maggior parte delle destinazioni. Tutti gli ordini vengono spediti arrotolati in una scatola di tubo di cartone all\'indirizzo di spedizione inviato. Tutto il pagamento è gestito da Apple Pay, o Stripe.\n\n\n### Cancellazione/rimborso:\nGli ordini vengono elaborati immediatamente dopo essere stati presentati per la svolta più rapida possibile. Pertanto, non è disponibile alcun rimborso/cancellazione.';

  @override
  String get posterDescriptionMarkdown =>
      '## Sulle nostre mappe di stampa personalizzate\nStampa la tua mappa del mondo personalizzata. Personalizzalo con i tuoi colori e fallo consegnare direttamente a casa tua.\n \n### Specifiche:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientamento orizzontale.\n- Micro inchiostro, goccioline per stampe precise, colore a 8 bit, qualità quasi fotografica,\n- Carta satinata di spessore 0,22 mm\n\n### Dettagli di spedizione:\nSpedizione da Toronto, in Canada, in qualsiasi parte del mondo utilizzando Canada Post. Si prega di consentire da 2 a 4 settimane per la consegna nella maggior parte delle destinazioni. Tutti gli ordini vengono spediti arrotolati in una scatola di tubo di cartone all\'indirizzo di spedizione inviato. Tutto il pagamento è gestito da Google Pay o Stripe.\n\n\n### Cancellazione/rimborso:\nGli ordini vengono elaborati immediatamente dopo essere stati presentati per la svolta più rapida possibile. Pertanto, non è disponibile alcun rimborso/cancellazione.';

  @override
  String get posterCustomizeTitle => 'Personalizza poster';

  @override
  String get enterShippingAddress => 'Immettere l\'indirizzo di spedizione';

  @override
  String get price => 'Prezzo';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + Tax';
  }

  @override
  String get showSelections => 'Selezione di mostra';

  @override
  String get posterNoRefunds =>
      'Non sono disponibili rimborsi dopo che il poster è stato stampato.';

  @override
  String get posterReviewOrder => 'Rivedere il tuo ordine';

  @override
  String get email => 'E-mail';

  @override
  String get emailEmptyError => 'Inserisci la tua email';

  @override
  String get fullName => 'Nome e cognome';

  @override
  String get fullNameEmptyError => 'Inserisci il tuo nome e cognome';

  @override
  String get streetAddressEmptyError => 'Inserisci il tuo indirizzo di strada';

  @override
  String get cityEmptyError => 'Inserisci la tua città';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Inserisci il tuo $fieldName';
  }

  @override
  String get country => 'Paese';

  @override
  String get countryEmptyError => 'Inserisci il tuo paese';

  @override
  String get posterReviewOrderTitle => 'Rivedere il tuo ordine';

  @override
  String get buyNow => 'Acquista ora';

  @override
  String get secureCheckoutDisclaimer => 'Checkout sicuro fornito da Stripe';

  @override
  String get total => 'Totale';

  @override
  String get tax => 'Imposta';

  @override
  String get subtotal => 'totale parziale';

  @override
  String get posterProductName => 'Poster mappa visitato personalizzato';

  @override
  String get shipping => 'Spedizione';

  @override
  String get posterOrderReceivedTitle => 'Ordine Ricevuto';

  @override
  String get posterOrderReceivedSubtitle => 'Abbiamo ricevuto il tuo ordine!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Controlla la tua email per ulteriori aggiornamenti. \nConsenti a un arrivo fino a 4 settimane per arrivare il tuo poster. \nSe hai domande, ti preghiamo di inviarci un\'e -mail a [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Stato dell\'ordine poster stampato';

  @override
  String get moreInfo => 'Maggiori informazioni';

  @override
  String get logoutConfirm => 'Vorresti disconnettersi dall\'app?';

  @override
  String get emailNotAvailable => 'Quell\'e -mail è stata presa.';

  @override
  String get alphabetical => 'Alfabetico';

  @override
  String get firstTimeLiveTutorial =>
      'L\'indicazione del paese e della città di provenienza personalizza l\'esperienza dell\'applicazione.';

  @override
  String get firstTimeBeenTutorial =>
      'Selezionando dove si è stati è possibile visualizzare la mappa di tutti i Paesi in cui si è stati e vedere le statistiche personali.';

  @override
  String get progressTooltipGoal =>
      'I vostri obiettivi di viaggio si basano sul numero di Paesi che \"volete\" visitare rispetto ai Paesi in cui siete stati.';

  @override
  String get progressTooltipRank =>
      'Questo numero mostra la vostra posizione rispetto ai viaggiatori di tutto il mondo.  È possibile aumentare la propria posizione viaggiando in più Paesi.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Questo grafico si basa sul numero di Paesi in cui si è stati rispetto al totale dei Paesi del mondo.';

  @override
  String get sortBy => 'Ordina per';

  @override
  String get updateWishlist => 'Aggiornare la lista dei desideri';

  @override
  String get mapInfo =>
      'Fare clic sul Paese per selezionare \"sono stato\", \"voglio\" o \"vivo\". È anche possibile fare clic sull\'icona nell\'angolo in alto a sinistra per visualizzare l\'elenco.';

  @override
  String get oneTimePurchase => 'Tutto è un acquisto unico!';

  @override
  String get contact => 'Contatto';

  @override
  String get contactUs => 'Contattateci';

  @override
  String get noCitiesSelected => 'Non hai ancora selezionato nessuna città...';

  @override
  String get updateTravelGoal => 'Aggiorna l\'obiettivo di viaggio';

  @override
  String get travelGoalComplete =>
      'Congratulazioni! \\ N \\ Nyou ha completato il tuo obiettivo di viaggio! \n\ntap il pulsante + per aggiungere più paesi.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Non esiste un account associato l\'e -mail $email. Ti piacerebbe crearlo adesso?';
  }

  @override
  String get tryAgain => 'Riprova';

  @override
  String get itineraries => 'Piani di viaggio';

  @override
  String get itinerary => 'Piano di viaggio';

  @override
  String get place => 'Luoghi';

  @override
  String get itinerariesDescription =>
      'Questi sono i luoghi per i quali avete espresso interesse.\nUtilizzate questa guida per pianificare la vostra prossima vacanza.';

  @override
  String get addMore => 'Aggiungi altro';

  @override
  String get interests => 'Interessi';

  @override
  String get selection => 'Selezione';

  @override
  String get goal => 'Obiettivo';

  @override
  String get noItineraries => 'Nessun Itinerario';

  @override
  String get noItinerariesExplanation =>
      'Per favore, aggiungi alcuni luoghi, ispirazioni o esperienze per vedere i tuoi itinerari generarsi automaticamente.';

  @override
  String get clusterPins => 'Raggruppa Marcatori Mappa';

  @override
  String get toggleRegions => 'Mostra regioni con zoom avanti';

  @override
  String get mapProjection => 'Proiezione mappa';

  @override
  String get mercator => 'Mercatore';

  @override
  String get equirectangular => 'Equirettangolare';

  @override
  String get yourTravellerType => 'Il tuo tipo di viaggiatore:';

  @override
  String get yourHotelPreferences => 'Le tue preferenze per l\'hotel:';

  @override
  String get budget => 'Economico';

  @override
  String get midScale => 'Media gamma';

  @override
  String get luxury => 'Lusso';

  @override
  String get noTravellerType =>
      'Aggiungi elementi alla tua lista dei desideri per scoprire che tipo di viaggiatore sei.';

  @override
  String get unlockLived => 'Sblocca Vissuto';

  @override
  String get unlockLivedDescription =>
      'Seleziona sulla mappa dove hai vissuto in passato!';

  @override
  String get futureFeaturesDescription => '...e tutte le funzionalità future';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Il tuo paese più visitato:';

  @override
  String get departureDate => 'Data di partenza';

  @override
  String get returnDate => 'Data di ritorno';

  @override
  String get hotels => 'Hotel';

  @override
  String get food => 'Cibo';

  @override
  String get travelDates => 'Date di viaggio';

  @override
  String get posterForMe => 'Per me';

  @override
  String get posterSendGift => 'Invia un regalo';

  @override
  String get addSelections => 'Aggiungi selezioni';

  @override
  String get posterType => 'Tipo di poster';

  @override
  String get help => 'Aiuto';

  @override
  String get tutorialMap =>
      'Tocca un paese per selezionare: stato, desiderato e vissuto.';

  @override
  String get tutorialMapList =>
      'Tocca l\'icona dell\'elenco (angolo in alto a sinistra) per selezionare dall\'elenco.';

  @override
  String get tutorialCountryDetails =>
      'Tocca il Paese e poi \"Altro\" per selezionare per regione.';

  @override
  String get tutorialItems =>
      'Fai scorrere l\'interruttore per scegliere come selezionare gli elementi.';

  @override
  String get tutorialInspirations =>
      'Scorri verso destra o verso sinistra per passare alla carta successiva.';

  @override
  String get lifetime => 'A vita';

  @override
  String get chooseYourPlan => 'Scegli il tuo piano';

  @override
  String get requestARefund => 'Richiedi un rimborso';

  @override
  String get noPurchasesFound => 'Nessun acquisto trovato.';

  @override
  String get noProductsAvailable => 'Nessun prodotto disponibile';

  @override
  String get posterLandingAppBar => 'Porta le tue storie a casa';

  @override
  String get posterLandingSubHeading => 'I tuoi viaggi, la tua storia';

  @override
  String get posterLandingSubDescription =>
      'I tuoi viaggi sono più che semplici viaggi: sono storie, ricordi e traguardi. Trasforma quei momenti indimenticabili in una mappa del mondo personalizzata, unica come le tue avventure.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Una mappa dei tuoi successi: evidenzia ogni destinazione, dal tuo primo grande viaggio alla tua avventura più audace.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebra ogni viaggio: rivivi i tuoi viaggi ogni giorno con un poster splendidamente realizzato, pensato per ispirare.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Un regalo che conserveranno con cura: sorprendi un compagno di viaggio con una mappa personalizzata che mostra il suo viaggio, perfetta per compleanni, traguardi o semplicemente per un momento speciale.';

  @override
  String get posterLandingHowItWorks => 'Come funziona!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personalizza il tuo design: scegli colori, stili e segna i tuoi viaggi (o i loro!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Visualizza l\'anteprima della mappa: guardala prendere vita prima di ordinare.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Pagamento sicuro: veloce e sicuro con Apple Pay o Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Pagamento sicuro: veloce e sicuro con Google Pay o Stripe. ';

  @override
  String get posterLandingHowItWorksStep4 =>
      ' 4. Pronto per essere esposto: lo spediremo direttamente a casa tua (o a casa loro).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Esperienze dei compagni di viaggio';

  @override
  String get posterLandingCustomerReview1 =>
      'Questa mappa è un ottimo modo per tenere traccia di tutti i luoghi in cui sono stato e pianificare i nostri viaggi futuri. La qualità è ottima e sta benissimo appesa in ufficio. Ne ho persino presa una per mio fratello e non la smetteva di parlare di quanto sia bella! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Ho visitato oltre 150 porti mentre lavoravo in crociera. Questa mappa è un\'aggiunta fantastica al mio soggiorno come ricordo di tutti gli anni trascorsi in mare. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Un regalo fantastico per la festa della mamma. Mia mamma è rimasta davvero commossa! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Ho stampato una mappa dei luoghi che volevo visitare con la mia ragazza. È stato un regalo di Natale fantastico. Anche di alta qualità. Brad J.';

  @override
  String get posterLandingSpecifications => 'Specifiche';

  @override
  String get posterLandingSpecification1 => '• Dimensioni: 40,64 cm x 50,8 cm';

  @override
  String get posterLandingSpecification2 => '• Orientamento: Orizzontale';

  @override
  String get posterLandingSpecification3 =>
      '- Qualità di stampa: Micro-inchiostro, gocce per stampe precise. Colore a 8 bit, qualità di stampa quasi fotografica.';

  @override
  String get posterLandingSpecification4 =>
      '- Carta: Carta satinata da 0,22 mm di spessore';

  @override
  String get posterLandingShippingHeader => 'Dettagli sulla spedizione';

  @override
  String get posterLandingShipping1 =>
      '- Spedizione da Toronto, Canada, a qualsiasi parte del mondo tramite Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '- La consegna è prevista in 2-4 settimane per la maggior parte delle destinazioni.';

  @override
  String get posterLandingShipping3 =>
      '- Tutti gli ordini vengono spediti in una scatola di cartone all\'indirizzo di spedizione indicato.';

  @override
  String get posterLandingCancellationHeader => 'Cancellazione/Rimborso:';

  @override
  String get posterLandingCancellationBody =>
      'I rimborsi sono disponibili prima dell\'invio del poster alla tipografia, che può richiedere fino a 24 ore.  Dopo l\'elaborazione dell\'ordine, non è possibile effettuare alcun rimborso/annullamento.  Riceverete un\'e-mail quando il vostro ordine sarà stato stampato.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
