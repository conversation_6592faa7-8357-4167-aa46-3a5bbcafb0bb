// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Danish (`da`).
class AppLocalizationsDa extends AppLocalizations {
  AppLocalizationsDa([String locale = 'da']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Sprog';

  @override
  String get pickEmailApp => 'Vælg din e-mail-app';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Jeg har besøgt $amount lande! Hvor mange har du besøgt? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Jeg har besøgt $amount byer! Hvor mange har du besøgt? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Jeg har besøgt $amount $listName! Hvor mange har du besøgt? www.visitedapp.com';
  }

  @override
  String get clear => 'Ryd';

  @override
  String get been => 'Været';

  @override
  String get want => 'Ønsker';

  @override
  String get live => 'Live';

  @override
  String get lived => 'Levede';

  @override
  String get water => 'Vand';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Grænser';

  @override
  String get labels => 'Mærkater';

  @override
  String get legend => 'Legende';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirationer';

  @override
  String get delete => 'Slet';

  @override
  String get unlockVisitedUpsellTitle => 'Ønsker du at se mere?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Lås op for alle funktioner, og nyd Visited i sin fulde version';

  @override
  String get checkTheDetails => 'Tjek detaljerne';

  @override
  String get moreInspirationsComingSoon =>
      'Vi arbejder på at få flere billeder i appen. Kom snart tilbage!';

  @override
  String get unlockPremiumFeatures => 'Lås op for premiumfunktioner';

  @override
  String get purchased => 'Købt!';

  @override
  String get buy => 'Køb';

  @override
  String get restorePurchases => 'Gendan køb';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Er du sikker?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Sletning af dette kort er permanent. Du vil ikke kunne gendanne dette billede igen.';

  @override
  String get cancel => 'Fortryd';

  @override
  String get map => 'Kort';

  @override
  String get progress => 'Fremskridt';

  @override
  String get myTravelGoal => 'Mit rejsemål';

  @override
  String goalRemaining(int remaining) {
    return '$remaining tilbage!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'af verden!';

  @override
  String get countries => 'lande';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Mest populære lande besøgt fra $country:';
  }

  @override
  String get login => 'Log på';

  @override
  String get logout => 'Log ud';

  @override
  String get enterYourEmail => 'Indtast din e-mail';

  @override
  String get privacyPolicy => 'Fortrolighedspolitik';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-danish/';

  @override
  String get termsOfUse => 'Betingelser for brug';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-danish/';

  @override
  String get errorTitle => 'Hov!';

  @override
  String get enterValidEmail => 'Indtast venligst en gyldig e-mail';

  @override
  String get settings => 'Indstillinger';

  @override
  String get whereDoYouLive => 'Hvor bor du?';

  @override
  String get whereHaveYouBeen => 'Hvor har du været?';

  @override
  String get whereDoYouFlyFrom => 'Hvor flyver du fra?';

  @override
  String get next => 'Næste';

  @override
  String get missingAirports =>
      'Kan du ikke finde det, du leder efter? Send os en e-mail på <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Manglende lufthavne!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Velkommen til Visited';

  @override
  String get welcomeSubtitle => 'Et eventyr for livet venter';

  @override
  String get getStarted => 'Kom i gang';

  @override
  String get privacyAgreement => 'Privatlivsaftale';

  @override
  String get privacyAgreementSubtitle =>
      'Accepter venligst følgende punkter, inden du fortsætter med at bruge Visited';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Ved at markere dette felt godkender du, at du har læst og accepterer at være bundet af Arriving in High Heels \'[Privacy Policy] (https://www.arrivinginhighheels.com/privacy-policy) og [Betingelser for brug] (http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Jeg accepterer at modtage elektroniske beskeder fra Arriving in High Heels, der indeholder information og tilbud med hensyn til produkter, applikationer og tjenester, der kan være af interesse for mig, herunder anmeldelse af salg, kampagner, tilbud og nyhedsbreve. Jeg kan når som helst trække dette samtykke tilbage som beskrevet i fortrolighedspolitikken eller ved at klikke på linket \"afmeld\" i de elektroniske beskeder.';

  @override
  String get submit => 'Indsend';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Du skal acceptere begge vores vilkår og vælge at fortsætte med at bruge Visited.';

  @override
  String get deleteAccount => 'Slet konto';

  @override
  String get removeAdsUpsell =>
      'Ønsker du at fravælge reklamer og afmelde dig e-mail markedsføring i stedet?';

  @override
  String get deleteAccountWarning =>
      'Sletning af din konto fjerner alle dine oplysninger fra vores servere.\n \n Denne proces kan ikke fortrydes.';

  @override
  String get about => 'Om';

  @override
  String get popularity => 'Popularitet';

  @override
  String get regions => 'Regioner';

  @override
  String get population => 'Befolkning';

  @override
  String get size => 'Størrelse';

  @override
  String get coverage => 'Dækning';

  @override
  String get percentOfCountryVisited => '% af det besøgte land';

  @override
  String get visited => 'besøgte';

  @override
  String get notes => 'Bemærkninger';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Tilpas';

  @override
  String get onlyCountSovereign => 'Tæller kun selvstændigt land';

  @override
  String get countUkSeparately => 'Tæl britiske lande separat';

  @override
  String get showLegend => 'Vis legende';

  @override
  String get showLivedPin => 'Vis levede stift';

  @override
  String get useMyColours => 'Brug mine farver';

  @override
  String get mapColors => 'Kortfarver';

  @override
  String get traveller => 'Den rejsende';

  @override
  String get nightTraveller => 'Natterejseren';

  @override
  String get original => 'Den originale';

  @override
  String get explorer => 'Den opdagelsesrejsende';

  @override
  String get weekender => 'Den weekendrejsende';

  @override
  String get naturalist => 'Naturforskeren';

  @override
  String get historian => 'Historikeren';

  @override
  String get thrillSeeker => 'Spændingssøgeren';

  @override
  String get culturalBuff => 'Den kulturelle';

  @override
  String get myColors => 'Mine farver';

  @override
  String get experiences => 'Oplevelser';

  @override
  String get done => 'Færdig';

  @override
  String get experiencesInstructions =>
      'Tryk på + knappen for at komme i gang!';

  @override
  String get continueText => 'Fortsæt';

  @override
  String get experiencesDescription => 'Hvad elsker du at lave, når du rejser?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Mit rejsekort';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Jeg har set $percentage% af verdenen';
  }

  @override
  String get requiresOnline =>
      'Beklager, Visited kræver en aktiv netværksforbindelse. Åbn din indstillinger i appen og sørg for, at enten Wi-Fi eller mobildata er aktiveret, og at flytilstand er deaktiveret';

  @override
  String get list => 'Liste';

  @override
  String get more => 'Mere';

  @override
  String get myCountrySelections => 'Mine valg af lande';

  @override
  String get cities => 'Byer';

  @override
  String get citiesInstructions =>
      'Tryk på et hvilket som helst land for at vælge byer.';

  @override
  String get missingCitiesEmailTitle => 'Manglende byer!';

  @override
  String get lists => 'Lister';

  @override
  String get disputedTerritories => 'Omstridte territorier';

  @override
  String get sponsored => 'Sponsoreret';

  @override
  String get places => 'Steder';

  @override
  String get noListsError =>
      'Hov! Der er ingen lister tilgængelige på dette tidspunkt, prøv igen lidt senere';

  @override
  String get noInspirationsError =>
      'Hov! der er ingen fotos tilgængelige lige nu, prøv igen lidt senere';

  @override
  String get mostFrequentlyVisitedCountries => 'Dine mest besøgte lande:';

  @override
  String get update => 'Opdatering';

  @override
  String get signup => 'Tilmelde';

  @override
  String get loginWallSubtitle =>
      'Opret en gratis konto for at opleve den fulde version af Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Du mister alle dine valg, når du har lukket appen.';

  @override
  String get createAccount => 'Opret firma';

  @override
  String get continueWithoutAccount => 'Fortsæt uden et firma';

  @override
  String get inspirationPromotion =>
      'Bliv inspireret af smuk rejsefotografering';

  @override
  String get saveStatsPromotion => 'Gem din rejsestatistik!';

  @override
  String get selectRegionsPromotion => 'Vælg stater og provinser';

  @override
  String get experiencesPromotion => 'Spor oplevelser over hele verden';

  @override
  String get missingListItem =>
      'Savnede vi noget? Tryk her for at sende os en e -mail for at få dit yndlingssted tilføjet.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Manglende vare fra $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Jeg har besøgt $amount $listName';
  }

  @override
  String get orderPoster => 'Plakat';

  @override
  String get shareMap => 'Del kort';

  @override
  String get posterLandingPageTitle => 'Få din plakat';

  @override
  String get posterNotAvailableError =>
      'Plakatkøb er ikke tilgængelig lige nu. Prøv igen senere.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping forsendelse';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Om vores brugerdefinerede printkort\nUdskriv dit personaliserede verdenskort. Tilpas det med dine helt egne farver, og få den leveret direkte til dit hjem.\n \n### Specifikationer:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landskabsorientering.\n- Mikroblæk, dråber til præcise tryk, 8 bit farve, næsten fotoprintkvalitet,\n- 0,22 mm tyk satin\n\n### Forsendelsesdetaljer:\nForsendelse fra Toronto, Canada til hvor som helst i verden ved hjælp af Canada Post. Tillad venligst 2 til 4 uger til levering til de fleste destinationer. Alle ordrer sendes rullet op i en paprørboks til den indsendte forsendelsesadresse. Al betaling håndteres af Apple Pay, eller Stripe.\n\n\n### Annullering/refusion:\nOrdrer behandles umiddelbart efter at de er blevet indsendt for den hurtigste omdrejning. Derfor er der ingen refusion/annullering tilgængelig.';

  @override
  String get posterDescriptionMarkdown =>
      '## Om vores brugerdefinerede printkort\nUdskriv dit personaliserede verdenskort. Tilpas det med dine helt egne farver, og få den leveret direkte til dit hjem.\n \n### Specifikationer:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landskabsorientering.\n- Mikroblæk, dråber til præcise tryk, 8 bit farve, næsten fotoprintkvalitet,\n- 0,22 mm tyk satin\n\n### Forsendelsesdetaljer:\nForsendelse fra Toronto, Canada til hvor som helst i verden ved hjælp af Canada Post. Tillad venligst 2 til 4 uger til levering til de fleste destinationer. Alle ordrer sendes rullet op i en paprørboks til den indsendte forsendelsesadresse. Al betaling håndteres af Google Pay eller Stripe.\n\n\n### Annullering/refusion:\nOrdrer behandles umiddelbart efter at de er blevet indsendt for den hurtigste omdrejning. Derfor er der ingen refusion/annullering tilgængelig.';

  @override
  String get posterCustomizeTitle => 'Tilpas plakat';

  @override
  String get enterShippingAddress => 'Indtast forsendelsesadresse';

  @override
  String get price => 'Pris';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + skat';
  }

  @override
  String get showSelections => 'Vis valg';

  @override
  String get posterNoRefunds =>
      'Ingen refusion er tilgængelige, efter at din plakat er trykt.';

  @override
  String get posterReviewOrder => 'Gennemgå din ordre';

  @override
  String get email => 'E -mail';

  @override
  String get emailEmptyError => 'Indtast din e -mail';

  @override
  String get fullName => 'Fulde navn';

  @override
  String get fullNameEmptyError => 'Vær sød at skrive dit fulde navn';

  @override
  String get streetAddressEmptyError => 'Indtast din gadeadresse';

  @override
  String get cityEmptyError => 'Indtast din by';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Indtast venligst dit $fieldName';
  }

  @override
  String get country => 'Land';

  @override
  String get countryEmptyError => 'Indtast dit land';

  @override
  String get posterReviewOrderTitle => 'Gennemgå din ordre';

  @override
  String get buyNow => 'Køb nu';

  @override
  String get secureCheckoutDisclaimer => 'Sikker kasse leveret af Stripe';

  @override
  String get total => 'i alt';

  @override
  String get tax => 'Skat';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Brugerdefineret besøgte kortplakat';

  @override
  String get shipping => 'Forsendelse';

  @override
  String get posterOrderReceivedTitle => 'Ordre modtaget';

  @override
  String get posterOrderReceivedSubtitle => 'Vi modtog din ordre!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Tjek din e -mail for flere opdateringer. \nTillad venligst op til 4 uger for din plakat at ankomme. \nHvis du har spørgsmål, så mail os på [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Trykt plakatordre status';

  @override
  String get moreInfo => 'Mere information';

  @override
  String get logoutConfirm => 'Vil du logge ud af appen?';

  @override
  String get emailNotAvailable => 'Denne e -mail er taget.';

  @override
  String get alphabetical => 'Alfabetisk';

  @override
  String get firstTimeLiveTutorial =>
      'Hvis du angiver dit hjemland og din by, bliver din oplevelse af appen mere personlig.';

  @override
  String get firstTimeBeenTutorial =>
      'Hvis du vælger, hvor du har været, kan du få vist kortet over alle de lande, du har været i, og se personlig statistik.';

  @override
  String get progressTooltipGoal =>
      'Dine rejsemål er baseret på antallet af lande, som du \"ønsker\" at rejse til, sammenlignet med de lande, hvor du har \"været\".';

  @override
  String get progressTooltipRank =>
      'Dette tal viser, hvordan du ligger i forhold til rejsende i hele verden.  Du kan øge din placering ved at rejse til flere lande.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Denne graf er baseret på antallet af lande, du har været i, i forhold til det samlede antal lande i verden.';

  @override
  String get sortBy => 'Sortere efter';

  @override
  String get updateWishlist => 'Opdater ønskeliste';

  @override
  String get mapInfo =>
      'Klik på landet for at vælge været, ønsker eller bor. Du kan også klikke på ikonet i øverste venstre hjørne for at få vist listen.';

  @override
  String get oneTimePurchase => 'Alt er et engangskøb!';

  @override
  String get contact => 'Kontakt';

  @override
  String get contactUs => 'Kontakt os';

  @override
  String get noCitiesSelected => 'Du har ikke valgt nogen byer endnu...';

  @override
  String get updateTravelGoal => 'Opdater rejsemål';

  @override
  String get travelGoalComplete =>
      'Tillykke! \n\nYou har afsluttet dit rejsemål! \n\ntap + -knappen for at tilføje flere lande.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Der er ingen konto tilknyttet e -mailen $email. Vil du oprette det nu?';
  }

  @override
  String get tryAgain => 'Prøv igen';

  @override
  String get itineraries => 'Rejseplaner';

  @override
  String get itinerary => 'Rejseplan';

  @override
  String get place => 'Sted';

  @override
  String get itinerariesDescription =>
      'Dette er steder, du har udtrykt interesse for.\nBrug denne guide til at planlægge din næste ferie.';

  @override
  String get addMore => 'Tilføj mere';

  @override
  String get interests => 'Interesser';

  @override
  String get selection => 'Udvælgelse';

  @override
  String get goal => 'Mål';

  @override
  String get noItineraries => 'Ingen Ruter';

  @override
  String get noItinerariesExplanation =>
      'Tilføj venligst nogle steder, inspirationer eller oplevelser for at se dine rejseplaner generere automatisk.';

  @override
  String get clusterPins => 'Grupper pins';

  @override
  String get toggleRegions => 'Vis regioner ved zoom';

  @override
  String get mapProjection => 'Kortprojektion';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Afstandsgetrouwe';

  @override
  String get yourTravellerType => 'Din rejsende type:';

  @override
  String get yourHotelPreferences => 'Dine hotelpræferencer:';

  @override
  String get budget => 'Budget';

  @override
  String get midScale => 'Mellemklasse';

  @override
  String get luxury => 'Luksus';

  @override
  String get noTravellerType =>
      'Tilføj elementer til din ønskeliste for at opdage, hvilken type rejsende du er.';

  @override
  String get unlockLived => 'Lås Op Levet';

  @override
  String get unlockLivedDescription =>
      'Vælg hvor du tidligere har boet på kortet!';

  @override
  String get futureFeaturesDescription => '...og alle fremtidige funktioner';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Dit mest besøgte land:';

  @override
  String get departureDate => 'Afrejsedato';

  @override
  String get returnDate => 'Returdato';

  @override
  String get hotels => 'Hoteller';

  @override
  String get food => 'Mad';

  @override
  String get travelDates => 'Rejsedatoer';

  @override
  String get posterForMe => 'Til mig';

  @override
  String get posterSendGift => 'Send en gave';

  @override
  String get addSelections => 'Tilføj valg';

  @override
  String get posterType => 'Plakat type';

  @override
  String get help => 'Hjælp';

  @override
  String get tutorialMap =>
      'Tryk på et land for at vælge: været, ønsket og levet.';

  @override
  String get tutorialMapList =>
      'Tryk på listeikonet (øverst til venstre) for at vælge efter liste.';

  @override
  String get tutorialCountryDetails =>
      'Tryk på landet og derefter \"mere\" for at vælge efter region.';

  @override
  String get tutorialItems =>
      'Skub til/fra-knappen for at vælge, hvordan du vil vælge elementer.';

  @override
  String get tutorialInspirations =>
      'Stryg til højre eller venstre for at gå til næste kort.';

  @override
  String get lifetime => 'Livstid';

  @override
  String get chooseYourPlan => 'Vælg din plan';

  @override
  String get requestARefund => 'Anmod om refusion';

  @override
  String get noPurchasesFound => 'Ingen køb fundet.';

  @override
  String get noProductsAvailable => 'Ingen produkter tilgængelige';

  @override
  String get posterLandingAppBar => 'Bring dine historier hjem';

  @override
  String get posterLandingSubHeading => 'Din rejse, din historie';

  @override
  String get posterLandingSubDescription =>
      'Dine rejser er mere end rejser, de er historier, minder og milepæle. Gør disse uforglemmelige øjeblikke til et personligt verdenskort, der er lige så unikt som dine eventyr.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Et kort over dine præstationer: Fremhæv hver destination, fra din første store tur til dit mest vovede eventyr.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Fejr hver rejse: Genoplev dine rejser dagligt med en smukt udformet plakat designet til at inspirere.';

  @override
  String get posterLandingPromoBullet3 =>
      '• En gave, de vil værdsætte: Overrask en medrejsende med et tilpasset kort, der viser deres rejse, perfekt til fødselsdage, milepæle eller bare fordi.';

  @override
  String get posterLandingHowItWorks => 'Hvordan det virker!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Tilpas dit design: Vælg farver, stilarter og marker dine rejser (eller deres!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Se forhåndsvisning af dit kort: Se det komme til live før din ordre.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Sikker betaling: Hurtig og sikker med Apple Pay eller Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Sikker betaling: Hurtig og sikker med Google Pay eller Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Klar til visning: Vi sender den direkte til din dør (eller deres).';

  @override
  String get posterLandingCustomerReviewsHeader => 'Erfaringer fra medrejsende';

  @override
  String get posterLandingCustomerReview1 =>
      'Dette kort er en fantastisk måde at holde styr på overalt, hvor jeg har rejst og planlægge vores fremtidige ture. Kvaliteten er solid, og det ser fantastisk ud at hænge på mit kontor. Jeg fik endda et til min bror, og han kunne ikke lade være med at tale om, hvor fedt det er! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Jeg har rejst til over 150 havne, mens jeg arbejdede på krydstogt. Dette kort er en fantastisk tilføjelse til min stue som et minde om alle årene på havet. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Fantastisk gave til mors dag. Min mor blev super rørt! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Tryktede et kort over steder, jeg ville besøge med min kæreste. Det var en fantastisk julegave. Også høj kvalitet. Brad J.';

  @override
  String get posterLandingSpecifications => 'Specifikationer';

  @override
  String get posterLandingSpecification1 =>
      '• Mål: 16\" x 20\" (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '• Orientering: Landskab';

  @override
  String get posterLandingSpecification3 =>
      '• Udskriftskvalitet: Mikroblæk, dråber til præcise udskrifter.  8 bit farve, næsten foto printkvalitet.';

  @override
  String get posterLandingSpecification4 => '• Papir: 0,22 mm tykt satinpapir';

  @override
  String get posterLandingShippingHeader => 'Forsendelsesdetaljer';

  @override
  String get posterLandingShipping1 =>
      '• Forsendelse fra Toronto, Canada til hvor som helst i verden med Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Tillad 2-4 uger til levering til de fleste destinationer.';

  @override
  String get posterLandingShipping3 =>
      '• Alle ordrer rulles sammen i en paprørsæske til den leveringsadresse, du angiver.';

  @override
  String get posterLandingCancellationHeader => 'Annullering/Refusion:';

  @override
  String get posterLandingCancellationBody =>
      'Refusion er tilgængelig, før din plakat er sendt til printeren, hvilket kan tage op til 24 timer.  Efter din ordre er blevet behandlet, er der ingen refusion/annullering tilgængelig.  Du modtager en e-mail, når din ordre er udskrevet.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
