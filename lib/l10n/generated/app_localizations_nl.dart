// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Dutch Flemish (`nl`).
class AppLocalizationsNl extends AppLocalizations {
  AppLocalizationsNl([String locale = 'nl']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Taal';

  @override
  String get pickEmailApp => 'Kies je e-mail app';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Ik heb $amount landen bezocht! Hoeveel heb jij er bezocht? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Ik heb $amount steden bezocht! Hoeveel heb jij er bezocht? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Ik heb $amount $listName bezocht! Ho<PERSON>el heb jij er bezocht? www.visitedapp.com';
  }

  @override
  String get clear => 'Duidelijk';

  @override
  String get been => 'geweest';

  @override
  String get want => 'Wil';

  @override
  String get live => 'Leven';

  @override
  String get lived => 'Woonde';

  @override
  String get water => 'Water';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Grenzen';

  @override
  String get labels => 'Labels';

  @override
  String get legend => 'Legende';

  @override
  String get inspiration => 'Inspiratie';

  @override
  String get inspirations => 'Inspiraties';

  @override
  String get delete => 'Verwijderen';

  @override
  String get unlockVisitedUpsellTitle => 'Wil je meer zien?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Ontgrendel alle functies en geniet van Visited in zijn volle kracht';

  @override
  String get checkTheDetails => 'Bekijk de details';

  @override
  String get moreInspirationsComingSoon =>
      'We zijn bezig om meer afbeeldingen te krijgen. Check snel terug!';

  @override
  String get unlockPremiumFeatures => 'Ontgrendel premium functies';

  @override
  String get purchased => 'Gekocht!';

  @override
  String get buy => 'Kopen';

  @override
  String get restorePurchases => 'Herstel aankoop';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Weet u het zeker?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Het verwijderen van deze kaart is permanent. Er is geen manier om dit beeld te herstellen.';

  @override
  String get cancel => 'Annuleren';

  @override
  String get map => 'Kaart';

  @override
  String get progress => 'Vooruitgang';

  @override
  String get myTravelGoal => 'Mijn reisdoel';

  @override
  String goalRemaining(int remaining) {
    return '$remaining  Nog meer te gaan!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'van de wereld!';

  @override
  String get countries => 'landen';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Top Bezochte Landen van $country:';
  }

  @override
  String get login => 'Inloggen';

  @override
  String get logout => 'Uitloggen';

  @override
  String get enterYourEmail => 'Voer uw e-mail';

  @override
  String get privacyPolicy => 'Privacybeleid';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-dutch/';

  @override
  String get termsOfUse => 'Gebruiksvoorwaarden';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-dutch/';

  @override
  String get errorTitle => 'Oeps!';

  @override
  String get enterValidEmail => 'Voer een geldig e-mailadres in';

  @override
  String get settings => 'Instellingen';

  @override
  String get whereDoYouLive => 'Waar woon je?';

  @override
  String get whereHaveYouBeen => 'Waar ben je geweest?';

  @override
  String get whereDoYouFlyFrom => 'Waar vlieg je vandaan?';

  @override
  String get next => 'Volgende';

  @override
  String get missingAirports =>
      'Ziet u niet wat u zoekt? Stuur ons een e-<NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Ontbrekende vliegvelden!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Welkom bij Visited';

  @override
  String get welcomeSubtitle => 'Het avontuur van uw leven wacht op u';

  @override
  String get getStarted => 'Aan de slag';

  @override
  String get privacyAgreement => 'Privacy Overeenkomst';

  @override
  String get privacyAgreementSubtitle =>
      'Gelieve in te stemmen met de volgende items voordat u verder gaat met het gebruik Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Door het aanvinken van dit vakje, erkent u dat u hebt gelezen en ermee akkoord gebonden te zijn door Arriving in High Heels \'[Privacy Policy](https://www.arrivinginhighheels.com/privacy-policy) en [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Ik ga akkoord met het ontvangen van elektronische berichten van Arriving in High Heels met informatie en aanbiedingen met betrekking tot producten, toepassingen en diensten die van belang kunnen zijn voor mij, met inbegrip van kennisgeving van de verkoop, promoties, aanbiedingen en nieuwsbrieven. Ik kan deze toestemming te allen tijde intrekken zoals beschreven in de Privacy Policy of door te klikken op de \"unsubscribe\" link in de elektronische berichten.';

  @override
  String get submit => 'Submit';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'U moet akkoord gaan met zowel onze voorwaarden en opt om door te gaan met het gebruik van Bezocht.';

  @override
  String get deleteAccount => 'Verwijder account';

  @override
  String get removeAdsUpsell =>
      'Wilt u zich afmelden voor advertenties en u in plaats daarvan afmelden voor e-mailmarketing?';

  @override
  String get deleteAccountWarning =>
      'Door uw account te verwijderen, wordt al uw informatie van onze servers verwijderd.\n Dit proces kan niet ongedaan worden gemaakt.';

  @override
  String get about => 'Over';

  @override
  String get popularity => 'Populariteit';

  @override
  String get regions => 'Regio\'s';

  @override
  String get population => 'Bevolking';

  @override
  String get size => 'Omvang';

  @override
  String get coverage => 'Dekking';

  @override
  String get percentOfCountryVisited => '% van het land bezocht';

  @override
  String get visited => 'bezocht';

  @override
  String get notes => 'Opmerkingen';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Aanpassen';

  @override
  String get onlyCountSovereign => 'Tel alleen Soevereine Landen';

  @override
  String get countUkSeparately => 'Landen van het V.K. afzonderlijk tellen';

  @override
  String get showLegend => 'Toon Legende';

  @override
  String get showLivedPin => 'Toon Levende Speld';

  @override
  String get useMyColours => 'Gebruik mijn kleuren';

  @override
  String get mapColors => 'Kaart Kleuren';

  @override
  String get traveller => 'De reiziger';

  @override
  String get nightTraveller => 'De nachtreiziger';

  @override
  String get original => 'De Oorspronkelijke';

  @override
  String get explorer => 'De ontdekkingsreiziger';

  @override
  String get weekender => 'De weekganger';

  @override
  String get naturalist => 'De natuurliefhebber';

  @override
  String get historian => 'De Geschiedkundige';

  @override
  String get thrillSeeker => 'De sensatie zoeker';

  @override
  String get culturalBuff => 'De cultuurliefhebber';

  @override
  String get myColors => 'Mijn Kleuren';

  @override
  String get experiences => 'Ervaringen';

  @override
  String get done => 'Gedaan';

  @override
  String get experiencesInstructions => 'Tik op de + knop om te beginnen!';

  @override
  String get continueText => 'Doorgaan';

  @override
  String get experiencesDescription => 'Wat doe je graag als je op reis bent?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Mijn reiskaart';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Ik heb $percentage% van de wereld gezien';
  }

  @override
  String get requiresOnline =>
      'Sorry, Visited vereist een actieve netwerkverbinding. Open uw instellingen en zorg ervoor dat ofwel Wi-Fi ofwel mobiele data is ingeschakeld en dat vliegtuigmodus is uitgeschakeld.';

  @override
  String get list => 'Lijst';

  @override
  String get more => 'Meer';

  @override
  String get myCountrySelections => 'Mijn land selecties';

  @override
  String get cities => 'Steden';

  @override
  String get citiesInstructions =>
      'Tik op een land om te beginnen met het selecteren van steden.';

  @override
  String get missingCitiesEmailTitle => 'Ontbrekende steden!';

  @override
  String get lists => 'Lijsten';

  @override
  String get disputedTerritories => 'Betwiste gebieden';

  @override
  String get sponsored => 'Gesponsord';

  @override
  String get places => 'Plaatsen';

  @override
  String get noListsError =>
      'Opps, op dit moment zijn geen lijsten beschikbaar, probeer het later nog eens';

  @override
  String get noInspirationsError =>
      'Opps, op dit moment zijn er geen foto\'s beschikbaar, probeer het later nog eens';

  @override
  String get mostFrequentlyVisitedCountries => 'Uw meest bezochte landen:';

  @override
  String get update => 'Bijwerken';

  @override
  String get signup => 'Aanmelden';

  @override
  String get loginWallSubtitle =>
      'Maak een gratis account aan om de volledige versie van Visited te ervaren';

  @override
  String get loseAllSelectionsWarning =>
      'U verliest al uw selecties na het sluiten van de app.';

  @override
  String get createAccount => 'Account aanmaken';

  @override
  String get continueWithoutAccount => 'Doorgaan zonder account';

  @override
  String get inspirationPromotion =>
      'Laat je inspireren met prachtige reisfotografie';

  @override
  String get saveStatsPromotion => 'Sla uw reisstatistieken op!';

  @override
  String get selectRegionsPromotion => 'Selecteer Staten en provincies';

  @override
  String get experiencesPromotion => 'Volg ervaringen over de hele wereld';

  @override
  String get missingListItem =>
      'Hebben we iets gemist? Tik hier om ons een e -mail te sturen om uw favoriete plek te laten toevoegen.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Ontbrekend item van $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Ik heb $amount $listName bezocht';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Deel kaart';

  @override
  String get posterLandingPageTitle => 'Krijg je poster';

  @override
  String get posterNotAvailableError =>
      'Posteraankoop is momenteel niet beschikbaar. Probeer het later opnieuw.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping verzending';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Over onze aangepaste printkaarten\nDruk uw gepersonaliseerde wereldkaart af. Pas het aan met uw eigen kleuren en laat het rechtstreeks bij uw huis worden afgeleverd.\n \n### Specificaties:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landschap oriëntatie.\n- Micro -inkt, druppels voor precieze prints, 8 bit kleur, bijna fotoafdrukkwaliteit,\n- 0,22 mm dik satijnen papier\n\n### Verzendgegevens:\nVerzending van Toronto, Canada naar overal ter wereld met behulp van Canada Post. Houd rekening met de meeste bestemmingen 2 tot 4 weken voor levering. Alle bestellingen worden verzonden opgerold in een kartonnen buisvak naar het verzonden adres. Alle betaling wordt afgehandeld door Apple Pay, of Stripe.\n\n\n### Annulering/terugbetaling:\nBestellingen worden onmiddellijk verwerkt nadat ze zijn ingediend voor de snelst mogelijke ommekeer. Daarom is er geen terugbetaling/annulering beschikbaar.';

  @override
  String get posterDescriptionMarkdown =>
      '## Over onze aangepaste printkaarten\nDruk uw gepersonaliseerde wereldkaart af. Pas het aan met uw eigen kleuren en laat het rechtstreeks bij uw huis worden afgeleverd.\n \n### Specificaties:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landschap oriëntatie.\n- Micro -inkt, druppels voor precieze prints, 8 bit kleur, bijna fotoafdrukkwaliteit,\n- 0,22 mm dik satijnen papier\n\n### Verzendgegevens:\nVerzending van Toronto, Canada naar overal ter wereld met behulp van Canada Post. Houd rekening met de meeste bestemmingen 2 tot 4 weken voor levering. Alle bestellingen worden verzonden opgerold in een kartonnen buisvak naar het verzonden adres. Alle betaling wordt afgehandeld door Google Pay of Stripe.\n\n\n### Annulering/terugbetaling:\nBestellingen worden onmiddellijk verwerkt nadat ze zijn ingediend voor de snelst mogelijke ommekeer. Daarom is er geen terugbetaling/annulering beschikbaar.';

  @override
  String get posterCustomizeTitle => 'Poster aanpassen';

  @override
  String get enterShippingAddress => 'Voer het verzendadres in';

  @override
  String get price => 'Prijs';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + belasting';
  }

  @override
  String get showSelections => 'Tonen selectie';

  @override
  String get posterNoRefunds =>
      'Er zijn geen terugbetalingen beschikbaar nadat uw poster is afgedrukt.';

  @override
  String get posterReviewOrder => 'Controleer uw bestelling';

  @override
  String get email => 'E -mail';

  @override
  String get emailEmptyError => 'Vul alstublieft uw e-mailadres in';

  @override
  String get fullName => 'Voor-en achternaam';

  @override
  String get fullNameEmptyError => 'Vul alstublieft uw volledige naam in';

  @override
  String get streetAddressEmptyError => 'Voer uw straatadres in';

  @override
  String get cityEmptyError => 'Betreed uw stad';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Voer uw $fieldName in';
  }

  @override
  String get country => 'Land';

  @override
  String get countryEmptyError => 'Betreed uw land';

  @override
  String get posterReviewOrderTitle => 'Controleer uw bestelling';

  @override
  String get buyNow => 'Nu kopen';

  @override
  String get secureCheckoutDisclaimer => 'Beveilig kassa verstrekt door Stripe';

  @override
  String get total => 'Totaal';

  @override
  String get tax => 'Belasting';

  @override
  String get subtotal => 'Subtotaal';

  @override
  String get posterProductName => 'Custom bezochte kaartposter';

  @override
  String get shipping => 'Verzenden';

  @override
  String get posterOrderReceivedTitle => 'bestelling ontvangen';

  @override
  String get posterOrderReceivedSubtitle =>
      'We hebben uw bestelling ontvangen!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Check je e-mail voor meer updates. \nHet kan tot 4 weken duren voordat uw poster arriveert. \nAls je vragen hebt, stuur dan een e-mail naar [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Gedrukte poster bestelstatus';

  @override
  String get moreInfo => 'Meer informatie';

  @override
  String get logoutConfirm => 'Wilt u zich uitloggen bij de app?';

  @override
  String get emailNotAvailable => 'Die e -mail is genomen.';

  @override
  String get alphabetical => 'Alfabetisch';

  @override
  String get firstTimeLiveTutorial =>
      'Het opgeven van uw land en stad zal uw app-ervaring personaliseren.';

  @override
  String get firstTimeBeenTutorial =>
      'Als u selecteert waar u bent geweest, kunt u uw kaart bekijken van alle landen waar u bent geweest en uw persoonlijke statistieken bekijken.';

  @override
  String get progressTooltipGoal =>
      'Uw reisdoelen zijn gebaseerd op het aantal landen dat u \"wilt\" reizen in vergelijking met landen waar u \"bent geweest\".';

  @override
  String get progressTooltipRank =>
      'Dit getal laat zien hoe u zich verhoudt tot reizigers over de hele wereld.  Je kunt je rang verhogen door naar meer landen te reizen.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Deze grafiek is gebaseerd op het aantal landen waar je geweest bent in vergelijking met het totale aantal landen in de wereld.';

  @override
  String get sortBy => 'Sorteren op';

  @override
  String get updateWishlist => 'Verlanglijstje bijwerken';

  @override
  String get mapInfo =>
      'Klik op het land om te selecteren geweest, willen of wonen. U kunt ook op het pictogram linksboven klikken voor de lijstweergave.';

  @override
  String get oneTimePurchase => 'Alles is een eenmalige aankoop!';

  @override
  String get contact => 'Contact';

  @override
  String get contactUs => 'Contact opnemen';

  @override
  String get noCitiesSelected => 'U heeft nog geen steden geselecteerd...';

  @override
  String get updateTravelGoal => 'Update het reisdoel';

  @override
  String get travelGoalComplete =>
      'Gefeliciteerd! \n\nij hebt uw reisdoel voltooid! \n\ntap de + knop om meer landen toe te voegen.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Er is geen account dat de e -mail $email heeft gekoppeld. Wil je het nu maken?';
  }

  @override
  String get tryAgain => 'Opnieuw proberen';

  @override
  String get itineraries => 'Reisplannen';

  @override
  String get itinerary => 'Reisplan';

  @override
  String get place => 'Plaats';

  @override
  String get itinerariesDescription =>
      'Dit zijn plaatsen waarin u interesse heeft getoond.\nGebruik deze gids om uw volgende vakantie te plannen.';

  @override
  String get addMore => 'Meer toevoegen';

  @override
  String get interests => 'Belangen';

  @override
  String get selection => 'Selectie';

  @override
  String get goal => 'Doel';

  @override
  String get noItineraries => 'Geen Routes';

  @override
  String get noItinerariesExplanation =>
      'Voeg alsjeblieft enkele plaatsen, inspiraties of ervaringen toe om te zien hoe je reisroutes automatisch worden gegenereerd.';

  @override
  String get clusterPins => 'Cluster Pins';

  @override
  String get toggleRegions => 'Regio\'s weergeven bij inzoomen';

  @override
  String get mapProjection => 'Kaartprojectie';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangl.';

  @override
  String get yourTravellerType => 'Uw reizigerstype:';

  @override
  String get yourHotelPreferences => 'Uw hotelvoorkeuren:';

  @override
  String get budget => 'Budget';

  @override
  String get midScale => 'Middelklasse';

  @override
  String get luxury => 'Luxe';

  @override
  String get noTravellerType =>
      'Voeg items toe aan je verlanglijstje om te ontdekken wat voor soort reiziger je bent.';

  @override
  String get unlockLived => 'Ontgrendel Geleefd';

  @override
  String get unlockLivedDescription =>
      'Selecteer op de kaart waar je eerder hebt gewoond!';

  @override
  String get futureFeaturesDescription => '...en alle toekomstige functies';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Uw meest bezochte land:';

  @override
  String get departureDate => 'Vertrekdatum';

  @override
  String get returnDate => 'Retourdatum';

  @override
  String get hotels => 'Hotels';

  @override
  String get food => 'Eten';

  @override
  String get travelDates => 'Reisdata';

  @override
  String get posterForMe => 'Voor mij';

  @override
  String get posterSendGift => 'Stuur een cadeau';

  @override
  String get addSelections => 'Voeg selecties toe';

  @override
  String get posterType => 'Poster Type';

  @override
  String get help => 'Hulp';

  @override
  String get tutorialMap =>
      'Tik op een land om te selecteren: geweest, willen en geleefd.';

  @override
  String get tutorialMapList =>
      'Tik op het lijstpictogram (linkerbovenhoek) om op lijst te selecteren.';

  @override
  String get tutorialCountryDetails =>
      'Tik op het land en vervolgens op ‘meer’ om per regio te selecteren.';

  @override
  String get tutorialItems =>
      'Verschuif de schakelaar om te kiezen hoe u items wilt selecteren.';

  @override
  String get tutorialInspirations =>
      'Veeg naar rechts of links om naar de volgende kaart te gaan.';

  @override
  String get lifetime => 'Levenslang';

  @override
  String get chooseYourPlan => 'Kies je plan';

  @override
  String get requestARefund => 'Vraag een terugbetaling aan';

  @override
  String get noPurchasesFound => 'Geen aankopen gevonden.';

  @override
  String get noProductsAvailable => 'Geen producten beschikbaar';

  @override
  String get posterLandingAppBar => 'Neem je verhalen mee naar huis';

  @override
  String get posterLandingSubHeading => 'Jouw reis, jouw verhaal';

  @override
  String get posterLandingSubDescription =>
      'Je reizen zijn meer dan alleen maar uitstapjes, het zijn verhalen, herinneringen en mijlpalen. Verander die onvergetelijke momenten in een gepersonaliseerde wereldkaart die net zo uniek is als je avonturen.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Een kaart van je prestaties: Markeer elke bestemming, van je eerste grote reis tot je meest gewaagde avontuur.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Vier elke reis: Beleef je reizen dagelijks opnieuw met een prachtig vormgegeven poster die inspireert.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Een cadeau dat ze zullen koesteren: Verras een medereiziger met een gepersonaliseerde kaart die hun reis laat zien, perfect voor verjaardagen, mijlpalen of gewoon zomaar.';

  @override
  String get posterLandingHowItWorks => 'Zo werkt het!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personaliseer je ontwerp: Kies kleuren, stijlen en markeer je reizen (of die van hen!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Bekijk een voorbeeld van je kaart: Zie hem tot leven komen voordat je bestelt.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Veilig betalen: Snel en veilig met Apple Pay of Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Veilig betalen: Snel en veilig met Google Pay of Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Klaar om te presenteren: We bezorgen het direct bij u (of bij hen) thuis.';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Ervaringen van medereizigers';

  @override
  String get posterLandingCustomerReview1 =>
      'Deze kaart is een geweldige manier om al mijn reizen bij te houden en onze toekomstige reizen te plannen. De kwaliteit is goed en hij hangt fantastisch in mijn kantoor. Ik heb er zelfs een voor mijn broer gekocht en hij kon er niet over ophouden hoe cool hij is! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Ik heb tijdens mijn werk op een cruiseschip meer dan 150 havens bezocht. Deze kaart is een geweldige aanvulling op mijn woonkamer als herinnering aan al die jaren op zee. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Geweldig cadeau voor Moederdag. Mijn moeder was er superblij mee! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Ik heb een kaart afgedrukt van plaatsen die ik met mijn vriendin wilde bezoeken. Het was een geweldig kerstcadeau. En ook van hoge kwaliteit. Brad J.';

  @override
  String get posterLandingSpecifications => 'Specificaties';

  @override
  String get posterLandingSpecification1 =>
      '• Afmetingen: 40,64 x 50,8 cm (16\" x 20\")';

  @override
  String get posterLandingSpecification2 => '• Oriëntatie: Liggend';

  @override
  String get posterLandingSpecification3 =>
      '• Afdrukkwaliteit: Micro-inkt, druppels voor nauwkeurige afdrukken. 8-bits kleur, bijna fotokwaliteit.';

  @override
  String get posterLandingSpecification4 =>
      '• Papier: 0,22 mm dik satijnpapier';

  @override
  String get posterLandingShippingHeader => 'Verzendgegevens';

  @override
  String get posterLandingShipping1 =>
      '• Verzending vanuit Toronto, Canada naar overal ter wereld met Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Houd rekening met een levertijd van 2-4 weken naar de meeste bestemmingen.';

  @override
  String get posterLandingShipping3 =>
      '• Alle bestellingen worden opgerold in een kartonnen doos verzonden naar het door u opgegeven verzendadres.';

  @override
  String get posterLandingCancellationHeader => 'Annulering/Restitutie:';

  @override
  String get posterLandingCancellationBody =>
      'Terugbetaling is mogelijk voordat uw poster naar de drukker is verzonden, wat tot 24 uur kan duren. Nadat uw bestelling is verwerkt, is restitutie/annulering niet meer mogelijk. U ontvangt een e-mail wanneer uw bestelling is afgedrukt.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
