// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Sprache';

  @override
  String get pickEmailApp => 'Wähle deine E-Mail-App';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Ich habe $amount Länder besucht! Wie viele hast du besucht? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Ich habe $amount Städte besucht! Wie viele hast du besucht? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Ich habe $amount $listName besucht! Wie viele hast du besucht? www.visitedapp.com';
  }

  @override
  String get clear => 'Löschen';

  @override
  String get been => 'Besucht';

  @override
  String get want => 'Wunsch';

  @override
  String get live => 'Leben';

  @override
  String get lived => 'Lebte';

  @override
  String get water => 'Wasser';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Grenzen';

  @override
  String get labels => 'Labels';

  @override
  String get legend => 'Legende';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirationen';

  @override
  String get delete => 'Löschen';

  @override
  String get unlockVisitedUpsellTitle => 'Wollen Sie mehr sehen?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Schalten Sie alle Funktionen frei und genießen Sie Visited in seiner vollen Stärke';

  @override
  String get checkTheDetails => 'Prüfen Sie die Details';

  @override
  String get moreInspirationsComingSoon =>
      'Wir arbeiten daran, mehr Bilder zu bekommen. Schauen Sie bald wieder vorbei!';

  @override
  String get unlockPremiumFeatures => 'Premium-Funktionen freischalten';

  @override
  String get purchased => 'Gekauft!';

  @override
  String get buy => 'Kaufen';

  @override
  String get restorePurchases => 'Kauf wiederherstellen';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Sind Sie sicher?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Die Löschung dieser Karte ist permanent. Es gibt keine Möglichkeit, dieses Bild wiederherzustellen.';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get map => 'Karte';

  @override
  String get progress => 'Fortschritt';

  @override
  String get myTravelGoal => 'Mein Reiseziel';

  @override
  String goalRemaining(int remaining) {
    return 'Noch $remaining!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'der Welt!';

  @override
  String get countries => 'Länder';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Meist besuchte Länder von $country:';
  }

  @override
  String get login => 'Einloggen';

  @override
  String get logout => 'Abmelden';

  @override
  String get enterYourEmail => 'Ihre E-Mail eingeben';

  @override
  String get privacyPolicy => 'Datenschutz';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-german/';

  @override
  String get termsOfUse => 'Nutzungsbedingungen';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-german/';

  @override
  String get errorTitle => 'Hoppla!';

  @override
  String get enterValidEmail => 'Bitte geben Sie eine gültige E-Mail ein';

  @override
  String get settings => 'Einstellungen';

  @override
  String get whereDoYouLive => 'Wo leben Sie?';

  @override
  String get whereHaveYouBeen => 'Wo waren Sie schon einmal?';

  @override
  String get whereDoYouFlyFrom => 'Von wo aus fliegen Sie?';

  @override
  String get next => 'Weiter';

  @override
  String get missingAirports =>
      'Sie finden nicht, wonach Sie suchen? Schicken Sie uns eine E-<NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Fehlende Flughäfen!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Willkommen bei Visited';

  @override
  String get welcomeSubtitle => 'Das Abenteuer Ihres Lebens erwartet Sie';

  @override
  String get getStarted => 'Erste Schritte';

  @override
  String get privacyAgreement => 'Datenschutzvereinbarung';

  @override
  String get privacyAgreementSubtitle =>
      'Bitte stimmen Sie den folgenden Punkten zu, bevor Sie Visited weiter nutzen.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Indem Sie dieses Kästchen ankreuzen, bestätigen Sie, dass Sie die [Datenschutzbestimmungen](https://www.arrivinginhighheels.com/privacy-policy) und [Nutzungsbedingungen](https://www.arrivinginhighheels.com/terms-of-use) von Arriving in High Heels gelesen haben und damit einverstanden sind.';

  @override
  String get privacyAgreementOptIn =>
      'Ich stimme zu, elektronische Nachrichten von Arriving in High Heels zu erhalten, die Informationen und Angebote in Bezug auf Produkte, Anwendungen und Dienstleistungen enthalten, die für mich von Interesse sein könnten, einschließlich Benachrichtigungen über Verkäufe, Aktionen, Angebote und Newsletter. Ich kann diese Zustimmung jederzeit widerrufen, wie in der Datenschutzrichtlinie beschrieben oder indem ich auf den Link \"Abmelden\" in den elektronischen Nachrichten klicke.';

  @override
  String get submit => 'Senden';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Sie müssen unseren beiden Bedingungen zustimmen, um Visited weiter nutzen zu können.';

  @override
  String get deleteAccount => 'Konto löschen';

  @override
  String get removeAdsUpsell =>
      'Möchten Sie keine Werbung mehr erhalten und sich vom E-Mail-Marketing abmelden?';

  @override
  String get deleteAccountWarning =>
      'Wenn Sie Ihr Konto löschen, werden alle Ihre Daten von unseren Servern entfernt.\n \n Dieser Vorgang kann nicht rückgängig gemacht werden.';

  @override
  String get about => 'Über';

  @override
  String get popularity => 'Beliebtheit';

  @override
  String get regions => 'Regionen';

  @override
  String get population => 'Einwohnerzahl';

  @override
  String get size => 'Größe';

  @override
  String get coverage => 'Abdeckung';

  @override
  String get percentOfCountryVisited => '% des Landes besucht';

  @override
  String get visited => 'besucht';

  @override
  String get notes => 'Notizen';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Anpassen';

  @override
  String get onlyCountSovereign => 'Nur souveräne Länder zählen';

  @override
  String get countUkSeparately => 'Länder in GB separat zählen';

  @override
  String get showLegend => 'Legende anzeigen';

  @override
  String get showLivedPin => 'Gelebten Pin anzeigen';

  @override
  String get useMyColours => 'Meine Farben verwenden';

  @override
  String get mapColors => 'Kartenfarben';

  @override
  String get traveller => 'Der Reisende';

  @override
  String get nightTraveller => 'Der Nacht-Reisende';

  @override
  String get original => 'Das Original';

  @override
  String get explorer => 'Der Entdecker';

  @override
  String get weekender => 'Der Wochenendreisende';

  @override
  String get naturalist => 'Der Naturliebhaber';

  @override
  String get historian => 'Der Historiker';

  @override
  String get thrillSeeker => 'Der Abenteuerfreak';

  @override
  String get culturalBuff => 'Der Kulturliebhaber';

  @override
  String get myColors => 'Meine Farben';

  @override
  String get experiences => 'Erlebnisse';

  @override
  String get done => 'Erledigt';

  @override
  String get experiencesInstructions =>
      'Tippen Sie auf die Schaltfläche +, um loszulegen!';

  @override
  String get continueText => 'Weiter';

  @override
  String get experiencesDescription => 'Was machen Sie gerne, wenn Sie reisen?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-reise-app/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Meine Reisekarte';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Ich habe $percentage% der Welt gesehen';
  }

  @override
  String get requiresOnline =>
      'Sorry, Visited erfordert eine aktive Netzwerkverbindung. Bitte öffnen Sie Ihre Einstellungen und stellen Sie sicher, dass entweder Wi-Fi oder Mobilfunkdaten aktiviert sind und der Flugzeugmodus deaktiviert ist';

  @override
  String get list => 'Liste';

  @override
  String get more => 'Mehr';

  @override
  String get myCountrySelections => 'Meine Länderauswahl';

  @override
  String get cities => 'Städte';

  @override
  String get citiesInstructions =>
      'Tippen Sie auf ein beliebiges Land, um die Auswahl der Städte zu starten.';

  @override
  String get missingCitiesEmailTitle => 'Fehlende Städte!';

  @override
  String get lists => 'Listen';

  @override
  String get disputedTerritories => 'Umstrittene Territorien';

  @override
  String get sponsored => 'Gesponsert';

  @override
  String get places => 'Orte';

  @override
  String get noListsError =>
      'Ups, zur Zeit sind keine Listen verfügbar, bitte versuchen Sie es etwas später';

  @override
  String get noInspirationsError =>
      'Ups, im Moment sind keine Fotos verfügbar, bitte versuchen Sie es etwas später';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Ihre am häufigsten besuchten Länder:';

  @override
  String get update => 'Aktualisieren';

  @override
  String get signup => 'Anmeldung';

  @override
  String get loginWallSubtitle =>
      'Erstellen Sie ein kostenloses Konto, um die Vollversion von Visited zu erleben';

  @override
  String get loseAllSelectionsWarning =>
      'Sie verlieren alle Ihre Auswahlen nach dem Schließen der App.';

  @override
  String get createAccount => 'Konto erstellen';

  @override
  String get continueWithoutAccount => 'Ohne Konto fortfahren';

  @override
  String get inspirationPromotion =>
      'Lassen Sie sich von wunderschöner Reisefotografie inspirieren';

  @override
  String get saveStatsPromotion => 'Speichern Sie Ihre Reisestatistiken!';

  @override
  String get selectRegionsPromotion => 'Ausgewählte Bundesländer und Provinzen';

  @override
  String get experiencesPromotion =>
      'Verfolgen Sie Erlebnisse auf der ganzen Welt';

  @override
  String get missingListItem =>
      'Haben wir etwas vermisst? Tippen Sie hier, um uns eine E -Mail zu senden, um Ihren Lieblingsort hinzuzufügen.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Fehlendes Element von $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Ich habe $amount $listName besucht';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Karte teilen';

  @override
  String get posterLandingPageTitle => 'Hol dir dein Poster';

  @override
  String get posterNotAvailableError =>
      'Der Kauf von Poster ist derzeit nicht verfügbar. Bitte versuchen Sie es später erneut.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Versand';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Über unsere benutzerdefinierten Druckkarten\nDrucken Sie Ihre personalisierte Weltkarte. Passen Sie es mit Ihren eigenen Farben an und lassen Sie es direkt zu Ihrem Zuhause liefern.\n \n### Spezifikationen:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landschaftsorientierung.\n- Mikrotinte, Tröpfchen für präzise Drucke, 8 -Bit -Farbe, fast Fotodruckqualität,\n- 0,22 mm dickes Satinpapier\n\n### Versanddetails:\nVersand aus Toronto, Kanada bis überall auf der Welt mit Canada Post. Bitte erlauben Sie 2 bis 4 Wochen für die Lieferung an die meisten Ziele. Alle Bestellungen werden in einer Kartonkaste an die eingereichte Versandadresse verschickt. Die gesamte Zahlung wird von Apple Pay, oder Stripe geleistet.\n\n\n### Stornierung/Rückerstattung:\nBestellungen werden unmittelbar nach der Einreichung für die schnellste Turnaround bearbeitet. Daher gibt es keine Rückerstattung/Stornierung.';

  @override
  String get posterDescriptionMarkdown =>
      '## Über unsere benutzerdefinierten Druckkarten\nDrucken Sie Ihre personalisierte Weltkarte. Passen Sie es mit Ihren eigenen Farben an und lassen Sie es direkt zu Ihrem Zuhause liefern.\n \n### Spezifikationen:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landschaftsorientierung.\n- Mikrotinte, Tröpfchen für präzise Drucke, 8 -Bit -Farbe, fast Fotodruckqualität,\n- 0,22 mm dickes Satinpapier\n\n### Versanddetails:\nVersand aus Toronto, Kanada bis überall auf der Welt mit Canada Post. Bitte erlauben Sie 2 bis 4 Wochen für die Lieferung an die meisten Ziele. Alle Bestellungen werden in einer Kartonkaste an die eingereichte Versandadresse verschickt. Die gesamte Zahlung wird von Google Pay oder Stripe geleistet.\n\n\n### Stornierung/Rückerstattung:\nBestellungen werden unmittelbar nach der Einreichung für die schnellste Turnaround bearbeitet. Daher gibt es keine Rückerstattung/Stornierung.';

  @override
  String get posterCustomizeTitle => 'POLPER anpassen';

  @override
  String get enterShippingAddress => 'Geben Sie die Versandadresse ein';

  @override
  String get price => 'Preis';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + Steuer';
  }

  @override
  String get showSelections => 'Auswahl zeigen';

  @override
  String get posterNoRefunds =>
      'Nachdem Ihr Poster gedruckt wurde, sind keine Rückerstattungen verfügbar.';

  @override
  String get posterReviewOrder => 'Überprüfen Sie ihre Bestellung';

  @override
  String get email => 'Email';

  @override
  String get emailEmptyError => 'Bitte geben Sie ihre E-Mail-Adresse ein';

  @override
  String get fullName => 'Vollständiger Name';

  @override
  String get fullNameEmptyError => 'bitte tragen Sie Ihren vollen Namen ein';

  @override
  String get streetAddressEmptyError =>
      'Bitte geben Sie Ihre Straßenadresse ein';

  @override
  String get cityEmptyError => 'Bitte betreten Sie Ihre Stadt';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Bitte geben Sie Ihren $fieldName ein';
  }

  @override
  String get country => 'Land';

  @override
  String get countryEmptyError => 'Bitte betreten Sie Ihr Land';

  @override
  String get posterReviewOrderTitle => 'Überprüfen Sie ihre Bestellung';

  @override
  String get buyNow => 'Kaufe jetzt';

  @override
  String get secureCheckoutDisclaimer =>
      'Sichere Kasse von Stripe bereitgestellt';

  @override
  String get total => 'Gesamt';

  @override
  String get tax => 'Steuer';

  @override
  String get subtotal => 'Zwischensumme';

  @override
  String get posterProductName => 'Benutzerdefiniertes Kartenplakat';

  @override
  String get shipping => 'Versand';

  @override
  String get posterOrderReceivedTitle => 'Bestellung erhalten';

  @override
  String get posterOrderReceivedSubtitle =>
      'Wir haben Ihre Bestellung erhalten!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Weitere Updates finden Sie in Ihren E -Mails. \nBitte erlauben Sie bis zu 4 Wochen, damit Ihr Poster ankommt. \nWenn Sie Fragen haben, senden Sie uns bitte eine E -Mail an [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Gedruckter Posterbestellstatus';

  @override
  String get moreInfo => 'Mehr Informationen';

  @override
  String get logoutConfirm => 'Möchten Sie sich aus der App abmelden?';

  @override
  String get emailNotAvailable => 'Diese E -Mail wurde genommen.';

  @override
  String get alphabetical => 'Alphabetisch';

  @override
  String get firstTimeLiveTutorial =>
      'Wenn Sie Ihr Heimatland und Ihre Stadt angeben, wird Ihre App personalisiert.';

  @override
  String get firstTimeBeenTutorial =>
      'Wenn du auswählst, wo du schon gewesen bist, kannst du deine Karte mit allen Ländern, in denen du gewesen bist, anzeigen und deine persönlichen Statistiken einsehen.';

  @override
  String get progressTooltipGoal =>
      'Ihre Reiseziele basieren auf der Anzahl der Länder, die Sie \"bereisen möchten\", im Vergleich zu den Ländern, in denen Sie \"gewesen sind\".';

  @override
  String get progressTooltipRank =>
      'Diese Zahl zeigt, wie Sie im Vergleich zu anderen Reisenden auf der Welt abschneiden.  Sie können Ihren Rang erhöhen, indem Sie mehr Länder bereisen.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Dieses Diagramm basiert auf der Anzahl der Länder, die Sie bereist haben, im Vergleich zu allen Ländern der Welt.';

  @override
  String get sortBy => 'Sortieren nach';

  @override
  String get updateWishlist => 'Wunschzettel aktualisieren';

  @override
  String get mapInfo =>
      'Klicken Sie auf das Land, um \"war\", \"möchte\" oder \"lebt\" auszuwählen. Sie können auch auf das Symbol in der oberen linken Ecke klicken, um die Liste zu sehen.';

  @override
  String get oneTimePurchase => 'Alles ist ein einmaliger Kauf!';

  @override
  String get contact => 'Kontakt';

  @override
  String get contactUs => 'Kontaktieren Sie uns';

  @override
  String get noCitiesSelected => 'Sie haben noch keine Städte ausgewählt...';

  @override
  String get updateTravelGoal => 'Reiseziel aktualisieren';

  @override
  String get travelGoalComplete =>
      'Herzlichen Glückwunsch! \n\nyou haben Ihr Reiseziel abgeschlossen! \n\ntap die + Taste, um weitere Länder hinzuzufügen.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Die E -Mail $email ist kein Konto zugeordnet. Möchten Sie es jetzt erstellen?';
  }

  @override
  String get tryAgain => 'Volver a intentarlo';

  @override
  String get itineraries => 'Planes de viaje';

  @override
  String get itinerary => 'Plan de viaje';

  @override
  String get place => 'Lugar';

  @override
  String get itinerariesDescription =>
      'Estos son los lugares por los que has expresado interés.\nUtilice esta guía para planificar sus próximas vacaciones.';

  @override
  String get addMore => 'Añadir más';

  @override
  String get interests => 'Intereses';

  @override
  String get selection => 'Auswahl';

  @override
  String get goal => 'Ziel';

  @override
  String get noItineraries => 'Keine Reiserouten';

  @override
  String get noItinerariesExplanation =>
      'Bitte fügen Sie einige Orte, Inspirationen oder Erlebnisse hinzu, um Ihre Reiserouten automatisch zu generieren.';

  @override
  String get clusterPins => 'Gruppenmarkierungen auf der Karte';

  @override
  String get toggleRegions => ' Regionen beim Zoomen anzeigen';

  @override
  String get mapProjection => 'Kartenprojektion';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Äquidistant';

  @override
  String get yourTravellerType => 'Ihr Reisendertyp:';

  @override
  String get yourHotelPreferences => 'Ihre Hotelvorlieben:';

  @override
  String get budget => 'Günstig';

  @override
  String get midScale => 'Mittelklasse';

  @override
  String get luxury => 'Luxus';

  @override
  String get noTravellerType =>
      'Füge Artikel zu deiner Wunschliste hinzu, um herauszufinden, welcher Reisetyp du bist.';

  @override
  String get unlockLived => 'Leben Freischalten';

  @override
  String get unlockLivedDescription =>
      'Wählen Sie auf der Karte aus, wo Sie zuvor gelebt haben!';

  @override
  String get futureFeaturesDescription => '...und alle zukünftigen Funktionen';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Ihr am häufigsten besuchtes Land:';

  @override
  String get departureDate => 'Abreisedatum';

  @override
  String get returnDate => 'Rückreisedatum';

  @override
  String get hotels => 'Hotels';

  @override
  String get food => 'Essen';

  @override
  String get travelDates => 'Reisedaten';

  @override
  String get posterForMe => 'Für mich';

  @override
  String get posterSendGift => 'Geschenk senden';

  @override
  String get addSelections => 'Auswahlen hinzufügen';

  @override
  String get posterType => 'Postertyp';

  @override
  String get help => 'Helfen';

  @override
  String get tutorialMap =>
      'Tippen Sie auf ein Land, um auszuwählen: gewesen, gewollt und gelebt.';

  @override
  String get tutorialMapList =>
      'Tippen Sie auf das Listensymbol (obere linke Ecke), um nach Liste auszuwählen.';

  @override
  String get tutorialCountryDetails =>
      'Tippen Sie auf das Land und dann auf \"mehr\", um eine Auswahl nach Region vorzunehmen.';

  @override
  String get tutorialItems =>
      'Schieben Sie den Schalter, um auszuwählen, wie Sie Elemente auswählen möchten.';

  @override
  String get tutorialInspirations =>
      'Wischen Sie nach rechts oder links, um zur nächsten Karte zu gelangen.';

  @override
  String get lifetime => 'Lebenslang';

  @override
  String get chooseYourPlan => 'Wählen Sie Ihren Plan';

  @override
  String get requestARefund => 'Eine Rückerstattung anfordern';

  @override
  String get noPurchasesFound => 'Keine Käufe gefunden.';

  @override
  String get noProductsAvailable => 'Keine Produkte verfügbar';

  @override
  String get posterLandingAppBar => 'Bring deine Geschichten nach Hause';

  @override
  String get posterLandingSubHeading => 'Deine Reise, deine Geschichte';

  @override
  String get posterLandingSubDescription =>
      'Deine Reisen sind mehr als nur Reisen – sie sind Geschichten, Erinnerungen und Meilensteine. Verwandle diese unvergesslichen Momente in eine personalisierte Weltkarte, die so einzigartig ist wie deine Abenteuer.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Eine Karte deiner Erfolge: Markiere jedes Reiseziel – von deiner ersten großen Reise bis zu deinem waghalsigsten Abenteuer.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Feiere jede Reise: Erlebe deine Reisen täglich neu mit einem wunderschön gestalteten Poster, das inspiriert.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Ein Geschenk, das sie schätzen werden: Überrasche einen Mitreisenden mit einer individuellen Karte, die seine Reise zeigt – perfekt für Geburtstage, Meilensteine ​​oder einfach so.';

  @override
  String get posterLandingHowItWorks => 'So funktioniert’s!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Individualisiere dein Design: Wähle Farben und Stile und markiere deine (oder ihre) Reisen!';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Vorschau deiner Karte: Erlebe sie vor deiner Bestellung.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Sichere Zahlung: Schnell und sicher mit Apple Pay oder Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Sichere Zahlung: Schnell und sicher mit Google Pay oder Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Bereit zur Ausstellung: Wir liefern es direkt an Ihre (oder ihre) Tür.';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Erfahrungen von Mitreisenden';

  @override
  String get posterLandingCustomerReview1 =>
      '„Diese Karte ist eine tolle Möglichkeit, meine Reisen im Blick zu behalten und zukünftige Reisen zu planen. Die Qualität ist solide und sie sieht fantastisch aus in meinem Büro. Ich habe sogar eine für meinen Bruder gekauft, und er konnte gar nicht mehr aufhören, davon zu schwärmen, wie cool sie ist!“ – John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '„Ich habe während meiner Arbeit auf Kreuzfahrten über 150 Häfen bereist. Diese Karte ist eine tolle Ergänzung für mein Wohnzimmer und eine Erinnerung an all die Jahre auf See.“ – Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      '„Ein tolles Geschenk zum Muttertag. Meine Mutter war total gerührt!“ Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '„Ich habe eine Karte mit Orten ausgedruckt, die ich mit meiner Freundin besuchen wollte. Es war ein tolles Weihnachtsgeschenk. Und die Qualität ist auch sehr hochwertig.“ Brad J.';

  @override
  String get posterLandingSpecifications => 'Spezifikationen';

  @override
  String get posterLandingSpecification1 =>
      '• Maße: 40,64 cm x 50,8 cm (16\" x 20\")';

  @override
  String get posterLandingSpecification2 => '• Ausrichtung: Querformat';

  @override
  String get posterLandingSpecification3 =>
      '• Druckqualität: Mikrotinte, Tröpfchen für präzise Drucke. 8-Bit-Farbe, fast Fotodruckqualität.';

  @override
  String get posterLandingSpecification4 =>
      '• Papier: 0,22 mm dickes Satinpapier';

  @override
  String get posterLandingShippingHeader => 'Versanddetails';

  @override
  String get posterLandingShipping1 =>
      '• Versand von Toronto, Kanada, weltweit mit Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Lieferzeit für die meisten Ziele: 2–4 Wochen.';

  @override
  String get posterLandingShipping3 =>
      '• Alle Bestellungen werden gerollt in einer Pappröhre an die von Ihnen angegebene Lieferadresse versandt.';

  @override
  String get posterLandingCancellationHeader => 'Stornierung/Rückerstattung:';

  @override
  String get posterLandingCancellationBody =>
      'Rückerstattungen sind möglich, bevor Ihr Poster gedruckt wurde. Dies kann bis zu 24 Stunden dauern. Nach Bearbeitung Ihrer Bestellung ist keine Rückerstattung/Stornierung mehr möglich. Sie erhalten eine E-Mail, sobald Ihre Bestellung gedruckt wurde.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
