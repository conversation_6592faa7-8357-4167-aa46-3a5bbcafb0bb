// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Ukrainian (`uk`).
class AppLocalizationsUk extends AppLocalizations {
  AppLocalizationsUk([String locale = 'uk']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Мова';

  @override
  String get pickEmailApp => 'Виберіть свою поштову програму';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Я відвідав/ла $amount країн! Скільки відвідали ви? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Я відвідав/ла $amount міст! Скільки відвідали ви? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Я відвідав/ла $amount $listName! Скільки відвідали ви? www.visitedapp.com';
  }

  @override
  String get clear => 'Ясно';

  @override
  String get been => 'Був';

  @override
  String get want => 'Хочу';

  @override
  String get live => 'Жити';

  @override
  String get lived => 'Жив';

  @override
  String get water => 'Вода';

  @override
  String get land => 'Земля';

  @override
  String get borders => 'Кордони';

  @override
  String get labels => 'Етикетки';

  @override
  String get legend => 'Легенда';

  @override
  String get inspiration => 'Натхнення';

  @override
  String get inspirations => 'Натхнення';

  @override
  String get delete => 'Видалити';

  @override
  String get unlockVisitedUpsellTitle => 'Хочете побачити більше?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Розблокуйте всі функції та насолоджуйтесь Visited в повному обсязі';

  @override
  String get checkTheDetails => 'Дізнатися деталі';

  @override
  String get moreInspirationsComingSoon =>
      'Ми працюємо над тим, щоб отримати більше зображень. Перевірте пізніше!';

  @override
  String get unlockPremiumFeatures => 'Розблокуйте преміум функції';

  @override
  String get purchased => 'Придбано!';

  @override
  String get buy => 'Купити';

  @override
  String get restorePurchases => 'Відновити покупку';

  @override
  String get ok => 'Добре / ОК';

  @override
  String get areYouSure => 'Ти впевнений?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Видалення цієї картки є остаточним. Неможливо відновити це зображення';

  @override
  String get cancel => 'Скасувати';

  @override
  String get map => 'Карта';

  @override
  String get progress => 'Прогрес';

  @override
  String get myTravelGoal => 'Моя мета подорожі';

  @override
  String goalRemaining(int remaining) {
    return '$remaining залишилося ще!';
  }

  @override
  String get top => 'ТОП';

  @override
  String get ofTheWorld => 'світу!';

  @override
  String get countries => 'країн';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Найбільш популярні країни для подорожі від $country';
  }

  @override
  String get login => 'Увійти';

  @override
  String get logout => 'Вийти';

  @override
  String get enterYourEmail => 'Введіть свою електронну пошту';

  @override
  String get privacyPolicy => 'Політика конфіденційності';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-ukranian';

  @override
  String get termsOfUse => 'Умови користування';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-ukranian/';

  @override
  String get errorTitle => 'Ой!';

  @override
  String get enterValidEmail => 'Введіть дійсний електронний адрес';

  @override
  String get settings => 'Налаштування';

  @override
  String get whereDoYouLive => 'Де ви проживаєте?';

  @override
  String get whereHaveYouBeen => 'Де ви були?';

  @override
  String get whereDoYouFlyFrom => 'Звідки ти вилітаєш?';

  @override
  String get next => 'Далі';

  @override
  String get missingAirports =>
      'Не знайши, що шукали? Напишіть нам на електронну пошту <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Відсутні аеропорти!';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => 'Ласкаво просимо до Visited';

  @override
  String get welcomeSubtitle => 'Вас чекає найкраща пригода житя';

  @override
  String get getStarted => 'Розпочати';

  @override
  String get privacyAgreement => 'Угода про конфіденційність';

  @override
  String get privacyAgreementSubtitle =>
      'Будь ласка, погодьтеся з наступними пунктами, перш ніж продовжити користування Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Поставивши галочку в цьому пункті, ви підтверджуєте, що прочитали та погоджуєтесь дотримуватися політики конфіденційності Arriving in High Heels (https://www.arrivinginhighheels.com/privacy-policy) та Умови використання (http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Я погоджуюсь на отримування електронних повідомлень від компанії Arriving in High Heels, що містять інформацію та пропозиції щодо продуктів, додатків та послуг, які можуть мене зацікавити, включаючи повідомлення про продажі, акції, пропозиції та інформаційні бюлетені. Я можу відкликати цю згоду в будь-який час, як це описано в Політиці конфіденційності або натиснувши «скасувати підписку» в електронних повідомленнях.';

  @override
  String get submit => 'Подати';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Ви маєте погодитися з нашими умовами, щоб надалі користуватися Visited';

  @override
  String get deleteAccount => 'Видалити аккаунт';

  @override
  String get removeAdsUpsell =>
      'Ви хочете відмовитись від реклами та відписатися від рекламної россилки на пошті?';

  @override
  String get deleteAccountWarning =>
      'Видалення вашого аккаунту видалить всю вашу інформацію з наших серверів.\n Цей процес не можна скасувати.';

  @override
  String get about => 'Про';

  @override
  String get popularity => 'Популярність';

  @override
  String get regions => 'Регіони';

  @override
  String get population => 'Населення';

  @override
  String get size => 'Розмір';

  @override
  String get coverage => 'Покриття';

  @override
  String get percentOfCountryVisited => '% відвідання країни';

  @override
  String get visited => 'Відвідано';

  @override
  String get notes => 'Примітки';

  @override
  String get kmSquared => 'км²';

  @override
  String get customize => 'Налаштувати';

  @override
  String get onlyCountSovereign => 'Підрахувати лише суверенні країни';

  @override
  String get countUkSeparately => 'Підрахувати країни Великобританії окремо';

  @override
  String get showLegend => 'Показати історію';

  @override
  String get showLivedPin => 'Показати місце проживання';

  @override
  String get useMyColours => 'Використати мої кольори';

  @override
  String get mapColors => 'Карта кольорів';

  @override
  String get traveller => 'Мандрівник';

  @override
  String get nightTraveller => 'Нічний мандрівник';

  @override
  String get original => 'Оригінальний';

  @override
  String get explorer => 'Провідник';

  @override
  String get weekender => 'Вихідник';

  @override
  String get naturalist => 'Натураліст';

  @override
  String get historian => 'Історик';

  @override
  String get thrillSeeker => 'Шукач гострих відчуттів';

  @override
  String get culturalBuff => 'Культурний';

  @override
  String get myColors => 'Мої кольори';

  @override
  String get experiences => 'Досвід';

  @override
  String get done => 'Готово';

  @override
  String get experiencesInstructions => 'Натисніть кнопку +, щоб розпочати!';

  @override
  String get continueText => 'Продовжити';

  @override
  String get experiencesDescription => 'Чим ви любите займатись, подорожуючи?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Моя карта подорожей';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Я бачив $percentage% світу';
  }

  @override
  String get requiresOnline =>
      'На жаль, для користування Visited потрібне активне підключення до мережі. Відкрийте свої налаштування і переконайтеся, що ввімкнено Wi-Fi або сотові дані та вимкнено режим польоту';

  @override
  String get list => 'Список';

  @override
  String get more => 'Більше';

  @override
  String get myCountrySelections => 'Мій вибір країн';

  @override
  String get cities => 'Міста';

  @override
  String get citiesInstructions =>
      'Натисніть будь-яку країну, щоб почати вибрати міста.';

  @override
  String get missingCitiesEmailTitle => 'Відсутні міста!';

  @override
  String get lists => 'Списки';

  @override
  String get disputedTerritories => 'Спорні території';

  @override
  String get sponsored => 'Спонсорований';

  @override
  String get places => 'Місця';

  @override
  String get noListsError =>
      'Упс, наразі списків немає, спробуйте трохи пізніше';

  @override
  String get noInspirationsError =>
      'Упс, на даний момент фотографій немає, спробуйте трохи пізніше';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Ваші найчастіше відвідувані країни:';

  @override
  String get update => 'Оновлення';

  @override
  String get signup => 'Зареєструватися';

  @override
  String get loginWallSubtitle =>
      'Створіть безкоштовний обліковий запис, щоб випробувати повну версію Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Ви втратите всі вибрані фрагменти після закриття програми.';

  @override
  String get createAccount => 'Створити обліковий запис';

  @override
  String get continueWithoutAccount => 'Продовжити без облікового запису';

  @override
  String get inspirationPromotion =>
      'Надихайтеся красивою фотографією подорожей';

  @override
  String get saveStatsPromotion => 'Збережіть статистику подорожей!';

  @override
  String get selectRegionsPromotion => 'Вибір штатів і провінцій';

  @override
  String get experiencesPromotion => 'Відстеження досвіду по всьому світу';

  @override
  String get missingListItem =>
      'Ми щось пропустили? Натисніть тут, щоб надіслати нам електронний лист, щоб додати улюблене місце.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Відсутній елемент із $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Я відвідав $amount $listName';
  }

  @override
  String get orderPoster => 'Плакат';

  @override
  String get shareMap => 'Поділитися картою';

  @override
  String get posterLandingPageTitle => 'Отримайте свій постер';

  @override
  String get posterNotAvailableError =>
      'Зараз закупівля плакатів недоступна. Будь-ласка спробуйте пізніше.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping доставка';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Про наші спеціальні карти друку\nРоздрукуйте свою персоналізовану карту світу. Налаштуйте його власними кольорами та передайте його прямо до вашого будинку.\n \n### Технічні умови:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Пейзажна орієнтація.\n- Мікро чорнила, краплі для точних відбитків, 8 -бітний колір, майже якість друку,\n- Затиновий папір товщиною 0,22 мм\n\n### Деталі доставки:\nДоставка з Торонто, Канада до будь -якого місця світу, використовуючи Canada Post. Будь ласка, дозвольте від 2 до 4 тижнів для доставки до більшості напрямків. Усі замовлення відправляються в коробку картонної трубки до поданої адреси доставки. Весь платіж обробляється Apple Pay, або Stripe.\n\n\n### Скасування/повернення коштів:\nЗамовлення обробляються відразу після подання на найшвидший поворот. Тому відшкодування/скасування немає.';

  @override
  String get posterDescriptionMarkdown =>
      '## Про наші спеціальні карти друку\nРоздрукуйте свою персоналізовану карту світу. Налаштуйте його власними кольорами та передайте його прямо до вашого будинку.\n \n### Технічні умови:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Пейзажна орієнтація.\n- Мікро чорнила, краплі для точних відбитків, 8 -бітний колір, майже якість друку,\n- Затиновий папір товщиною 0,22 мм\n\n### Деталі доставки:\nДоставка з Торонто, Канада до будь -якого місця світу, використовуючи Canada Post. Будь ласка, дозвольте від 2 до 4 тижнів для доставки до більшості напрямків. Усі замовлення відправляються в коробку картонної трубки до поданої адреси доставки. Весь платіж обробляється Google Pay або Stripe.\n\n\n### Скасування/повернення коштів:\nЗамовлення обробляються відразу після подання на найшвидший поворот. Тому відшкодування/скасування немає.';

  @override
  String get posterCustomizeTitle => 'Налаштувати плакат';

  @override
  String get enterShippingAddress => 'Введіть адресу доставки';

  @override
  String get price => 'Ціна';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + податок';
  }

  @override
  String get showSelections => 'Показати вибір';

  @override
  String get posterNoRefunds =>
      'Повернення коштів не доступні після надрукування вашого плаката.';

  @override
  String get posterReviewOrder => 'Перегляньте своє замовлення';

  @override
  String get email => 'Електронна пошта';

  @override
  String get emailEmptyError => 'Введіть свій електронний лист';

  @override
  String get fullName => 'Повне ім\'я';

  @override
  String get fullNameEmptyError => 'Будь ласка, введіть своє повне ім’я';

  @override
  String get streetAddressEmptyError =>
      'Будь ласка, введіть свою адресу вулиці';

  @override
  String get cityEmptyError => 'Будь ласка, введіть своє місто';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Будь ласка, введіть свій $fieldName';
  }

  @override
  String get country => 'Країна';

  @override
  String get countryEmptyError => 'Будь ласка, введіть свою країну';

  @override
  String get posterReviewOrderTitle => 'Перегляньте своє замовлення';

  @override
  String get buyNow => 'Купити зараз';

  @override
  String get secureCheckoutDisclaimer => 'Безпечний каси, наданий Stripe';

  @override
  String get total => 'Загальний';

  @override
  String get tax => 'Податковий';

  @override
  String get subtotal => 'Суттєвий';

  @override
  String get posterProductName => 'Плакат на замовлення на замовлення';

  @override
  String get shipping => 'Доставка';

  @override
  String get posterOrderReceivedTitle => 'замовлення отримано';

  @override
  String get posterOrderReceivedSubtitle => 'Ми отримали ваше замовлення!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Перегляньте електронну пошту, щоб отримати більше оновлень. \nБудь ласка, дозвольте до 4 тижнів, щоб ваш плакат надійшов. \nЯкщо у вас виникнуть запитання, будь ласка, напишіть нам на електронну пошту [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Друковано статус замовлення плакатів';

  @override
  String get moreInfo => 'Більше інформації';

  @override
  String get logoutConfirm => 'Чи хотіли б ви вийти з програми?';

  @override
  String get emailNotAvailable => 'Цей електронний лист взяв.';

  @override
  String get alphabetical => 'Алфавітний';

  @override
  String get firstTimeLiveTutorial =>
      'Вказавши країну та місто проживання, ви зможете персоналізувати свій досвід роботи з додатком.';

  @override
  String get firstTimeBeenTutorial =>
      'Вибір місця, де ви були, дозволяє переглянути карту всіх країн, в яких ви побували, і побачити особисту статистику.';

  @override
  String get progressTooltipGoal =>
      'Ваші цілі подорожі базуються на кількості країн, які ви \"хочете\" відвідати, порівняно з країнами, де ви вже \"були\".';

  @override
  String get progressTooltipRank =>
      'Ця кількість показує ваше місце серед мандрівників по всьому світу.  Ви можете підвищити свій рейтинг, відвідавши більше країн.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Цей графік базується на кількості країн, в яких ви були, у порівнянні з загальною кількістю країн світу.';

  @override
  String get sortBy => 'Сортувати за';

  @override
  String get updateWishlist => 'Оновити список бажань';

  @override
  String get mapInfo =>
      'Натисніть на країну, щоб вибрати \"був\", \"хочу\" або \"живу\". Ви також можете натиснути на іконку у верхньому лівому кутку для перегляду списку.';

  @override
  String get oneTimePurchase => 'Все це - одноразова покупка!';

  @override
  String get contact => 'Контакти';

  @override
  String get contactUs => 'Зв\'язатися з нами';

  @override
  String get noCitiesSelected => 'Ви ще не вибрали жодного міста...';

  @override
  String get updateTravelGoal => 'Оновлення цілі подорожі';

  @override
  String get travelGoalComplete =>
      'Вітаємо! \n\nyou виконали свою мету подорожей! \n\ntap кнопка +, щоб додати більше країн.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Немає облікового запису, пов’язаного з електронною поштою $email. Ви хотіли б створити це зараз?';
  }

  @override
  String get tryAgain => 'Спробуйте ще раз';

  @override
  String get itineraries => 'Плани подорожей';

  @override
  String get itinerary => 'План подорожі';

  @override
  String get place => 'Місце';

  @override
  String get itinerariesDescription =>
      'Це місця, які вас зацікавили.\nСкористайтеся цим путівником, щоб спланувати свою наступну відпустку.';

  @override
  String get addMore => 'Додати більше';

  @override
  String get interests => 'Інтереси';

  @override
  String get selection => 'Відбір';

  @override
  String get goal => 'Ціль';

  @override
  String get noItineraries => 'Немає Маршрутів';

  @override
  String get noItinerariesExplanation =>
      'Пожалуйста, додайте деякі місця, натхнення або досвіди, щоб побачити, як автоматично генеруються ваші маршрути.';

  @override
  String get clusterPins => 'групувати мітки на карті';

  @override
  String get toggleRegions => 'Показувати регіони під час збільшення масштабу';

  @override
  String get mapProjection => 'Проекція карти';

  @override
  String get mercator => 'Меркатор';

  @override
  String get equirectangular => 'рівнопрямокутний';

  @override
  String get yourTravellerType => 'Ваш тип мандрівника:';

  @override
  String get yourHotelPreferences => 'Ваші уподобання щодо готелів:';

  @override
  String get budget => 'Бюджетний';

  @override
  String get midScale => 'Середній рівень';

  @override
  String get luxury => 'Розкішний';

  @override
  String get noTravellerType =>
      'Додайте елементи до свого списку бажань, щоб дізнатися, який ви мандрівник.';

  @override
  String get unlockLived => 'Розблокувати Пережите';

  @override
  String get unlockLivedDescription => 'Виберіть на карті, де ви раніше жили!';

  @override
  String get futureFeaturesDescription => '...і всі майбутні функції';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Країна, яку ви найчастіше відвідуєте:';

  @override
  String get departureDate => 'Дата відправлення';

  @override
  String get returnDate => 'Дата повернення';

  @override
  String get hotels => 'Готелі';

  @override
  String get food => 'Їжа';

  @override
  String get travelDates => 'Дати подорожі';

  @override
  String get posterForMe => 'Для мене';

  @override
  String get posterSendGift => 'Надіслати подарунок';

  @override
  String get addSelections => 'Додати вибір';

  @override
  String get posterType => 'Тип постера';

  @override
  String get help => 'Довідка';

  @override
  String get tutorialMap => 'Торкніться країни, щоб вибрати: був, хотів і жив.';

  @override
  String get tutorialMapList =>
      'Торкніться значка списку (верхній лівий кут), щоб вибрати список.';

  @override
  String get tutorialCountryDetails =>
      'Натисніть на країну, а потім на «більше», щоб вибрати регіон.';

  @override
  String get tutorialItems =>
      'Посуньте перемикач, щоб вибрати спосіб вибору елементів.';

  @override
  String get tutorialInspirations =>
      'Проведіть пальцем праворуч або ліворуч, щоб перейти до наступної картки.';

  @override
  String get lifetime => 'Довічно';

  @override
  String get chooseYourPlan => 'Виберіть свій план';

  @override
  String get requestARefund => 'Запросити повернення коштів';

  @override
  String get noPurchasesFound => 'Покупки не знайдені.';

  @override
  String get noProductsAvailable => 'Немає доступних продуктів';

  @override
  String get posterLandingAppBar => 'Принесіть свої історії додому';

  @override
  String get posterLandingSubHeading => 'Ваша подорож, ваша історія';

  @override
  String get posterLandingSubDescription =>
      'Ваші подорожі – це більше, ніж подорожі, це історії, спогади та віхи. Перетворіть ці незабутні моменти на персоналізовану карту світу, таку ж унікальну, як і ваші пригоди.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Карта ваших досягнень: виділіть кожне місце призначення, від першої великої подорожі до найсміливішої пригоди.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Святкуйте кожну подорож: переживайте свої подорожі щодня за допомогою красиво створеного плаката, створеного для надихання.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Подарунок, який вони будуть цінувати: здивуйте попутника власною картою, яка демонструє їхню подорож, ідеально підходить для днів народження, пам’ятних подій або просто так.';

  @override
  String get posterLandingHowItWorks => 'Як це працює!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Налаштуйте свій дизайн: виберіть кольори, стилі та позначте свої подорожі (або їхні!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Перегляньте свою карту: подивіться, як вона оживає, перш ніж зробити замовлення.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Безпечна оплата: швидка та безпечна за допомогою Apple Pay або Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Безпечна оплата: швидка та безпечна за допомогою Google Pay або Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Готовий до показу: ми відправимо його прямо до ваших (або їхніх) дверей.';

  @override
  String get posterLandingCustomerReviewsHeader => 'Досвід попутників';

  @override
  String get posterLandingCustomerReview1 =>
      '«Ця карта - чудовий спосіб відстежувати всі місця, де я побував, і планувати наші майбутні подорожі.  Якість відмінна, і вона чудово виглядає, коли висить у мене в офісі.  Я навіть купив таку для свого брата, і він не може перестати говорити про те, яка вона класна!» - Джон К.';

  @override
  String get posterLandingCustomerReview2 =>
      '«Працюючи в круїзі, я відвідав понад 150 портів. Ця карта - чудове доповнення до моєї вітальні як пам\'ять про всі роки, проведені в морі». - Бетті К. ';

  @override
  String get posterLandingCustomerReview3 =>
      '«Чудовий подарунок на День матері. Моя мама була дуже зворушена!» Саманта В.';

  @override
  String get posterLandingCustomerReview4 =>
      '«Роздрукував карту місць, які хотів відвідати зі своєю дівчиною. Це був чудовий різдвяний подарунок. До того ж, дуже якісний». Бред Дж.';

  @override
  String get posterLandingSpecifications => 'Технічні характеристики';

  @override
  String get posterLandingSpecification1 =>
      '- Розміри: 16« x 20» (40,64 см x 50,8 см)';

  @override
  String get posterLandingSpecification2 => '- Орієнтація: Пейзаж';

  @override
  String get posterLandingSpecification3 =>
      '- Якість друку: Мікрочорнило, краплі для точних відбитків. 8-бітний колір, майже фотографічна якість друку.';

  @override
  String get posterLandingSpecification4 =>
      '- Папір: Сатиновий папір товщиною 0,22 мм';

  @override
  String get posterLandingShippingHeader => 'Деталі доставки';

  @override
  String get posterLandingShipping1 =>
      '- Відправлення з Торонто, Канада в будь-яку точку світу за допомогою Пошти Канади.';

  @override
  String get posterLandingShipping2 =>
      '- До більшості пунктів призначення доставка займає 2-4 тижні.';

  @override
  String get posterLandingShipping3 =>
      '- Усі замовлення надходять у картонній коробці на вказану вами адресу доставки.';

  @override
  String get posterLandingCancellationHeader => 'Скасування/повернення коштів:';

  @override
  String get posterLandingCancellationBody =>
      'Повернення коштів можливе до того, як ваш плакат буде відправлено до друку, що може зайняти до 24 годин.  Після того, як ваше замовлення буде надруковано, повернення коштів/скасування замовлення неможливе.  Ви отримаєте електронного листа, коли ваше замовлення буде надруковано.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
