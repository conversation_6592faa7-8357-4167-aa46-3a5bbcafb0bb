// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Czech (`cs`).
class AppLocalizationsCs extends AppLocalizations {
  AppLocalizationsCs([String locale = 'cs']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Jazyk';

  @override
  String get pickEmailApp => 'Vyberte svou e-mailovou aplikaci';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Navštívil(a) jsem $amount zemí! Kolik jsi navštívil(a) ty? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Navštívil(a) jsem $amount měst! Kolik jsi navštívil(a) ty? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Navštívil(a) jsem $amount $listName! Kolik jsi navštívil(a) ty? www.visitedapp.com';
  }

  @override
  String get clear => 'Vymazat';

  @override
  String get been => 'Byl';

  @override
  String get want => 'Chtít';

  @override
  String get live => 'Žít';

  @override
  String get lived => 'Žil';

  @override
  String get water => 'Voda';

  @override
  String get land => 'Půda';

  @override
  String get borders => 'Hranice';

  @override
  String get labels => 'Štítky';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inspirace';

  @override
  String get inspirations => 'Inspirace';

  @override
  String get delete => 'Odstranit';

  @override
  String get unlockVisitedUpsellTitle => 'Chcete vidět víc?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Odemkněte všechny funkce a užívejte si Visited v plné síle';

  @override
  String get checkTheDetails => 'Zkontrolujte podrobnosti';

  @override
  String get moreInspirationsComingSoon =>
      'Pracujeme na získávání dalších obrázků. Vraťte se brzy zpět!';

  @override
  String get unlockPremiumFeatures => 'Odemkněte prémiové funkce';

  @override
  String get purchased => 'Zakoupené!';

  @override
  String get buy => 'Koupit';

  @override
  String get restorePurchases => 'Obnovit nákup';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Jste si jistý?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Odstranění této karty je trvalé. Tento obrázek není možné obnovit.';

  @override
  String get cancel => 'Zrušit';

  @override
  String get map => 'Mapa';

  @override
  String get progress => 'Pokrok';

  @override
  String get myTravelGoal => 'Můj cestovní cíl';

  @override
  String goalRemaining(int remaining) {
    return 'Zbývá $remaining!';
  }

  @override
  String get top => 'NEJ';

  @override
  String get ofTheWorld => 'ze světa!';

  @override
  String get countries => 'země';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Nejlepší navštívené země z $country:';
  }

  @override
  String get login => 'Přihlásit se';

  @override
  String get logout => 'Odhlásit';

  @override
  String get enterYourEmail => 'Zadejte svůj e-mail';

  @override
  String get privacyPolicy => 'Zásady ochrany osobních údajů';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-czech/';

  @override
  String get termsOfUse => 'Podmínky užívání';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-czech/';

  @override
  String get errorTitle => 'Ojoj!';

  @override
  String get enterValidEmail => 'Prosím zadejte platný e-mail';

  @override
  String get settings => 'Nastavení';

  @override
  String get whereDoYouLive => 'Kde bydlíte?';

  @override
  String get whereHaveYouBeen => 'Kde jste byli?';

  @override
  String get whereDoYouFlyFrom => 'Odkud létáte?';

  @override
  String get next => 'Další';

  @override
  String get missingAirports =>
      'Nevidíte, co hledáte? Zašlete nám e-mail <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Chybějící letiště!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Vítejte ve Visited';

  @override
  String get welcomeSubtitle => 'Čeká vás dobrodružství na celý život';

  @override
  String get getStarted => 'Začít';

  @override
  String get privacyAgreement => 'Dohoda o ochraně osobních údajů';

  @override
  String get privacyAgreementSubtitle =>
      'Před dalším používáním služby Visited odsouhlaste následující položky.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Zaškrtnutím tohoto políčka potvrzujete, že jste si přečetli a souhlasíte s tím, že budete vázáni dokumenty [Zásady ochrany osobních údajů] společnosti Arriving in High Heels (https://www.arrivinginhighheels.com/privacy-policy) a [Podmínky používání] ( http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Souhlasím se zasíláním elektronických zpráv od Arriving in High Heels obsahujících informace a nabídky týkající se produktů, aplikací a služeb, které by mě mohly zajímat, včetně upozornění na výprodeje, propagační akce, nabídky a informační věstníky. Tento souhlas mohu kdykoliv zrušit, a to podle Zásad ochrany osobních údajů, nebo kliknutím na odkaz \"zrušit odběr\" v elektronických zprávách.';

  @override
  String get submit => 'Odeslat';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Pro pokračování v používání Visite musíte souhlasit s našimi Podmínkami i Odběrem.';

  @override
  String get deleteAccount => 'Smazat účet';

  @override
  String get removeAdsUpsell =>
      'Přejete si místo toho zrušit zobrazování reklam a odhlásit se z e-mailového marketingu?';

  @override
  String get deleteAccountWarning =>
      'Odstraněním svého účtu odstraníte z našich serverů všechny vaše informace.\n Tento proces nelze vrátit.';

  @override
  String get about => 'O nás';

  @override
  String get popularity => 'Popularita';

  @override
  String get regions => 'Regiony';

  @override
  String get population => 'Populace';

  @override
  String get size => 'Velikost';

  @override
  String get coverage => 'Pokrytí';

  @override
  String get percentOfCountryVisited => '% navštívené země';

  @override
  String get visited => 'Navštívené';

  @override
  String get notes => 'Poznámky';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Přizpůsobit';

  @override
  String get onlyCountSovereign => 'Počítat pouze suverénně země';

  @override
  String get countUkSeparately => 'Počítat země UK samostatně';

  @override
  String get showLegend => 'Zobrazit legendu';

  @override
  String get showLivedPin => 'Zobrazit živý bod';

  @override
  String get useMyColours => 'Použít moje barvy';

  @override
  String get mapColors => 'Barvy mapy';

  @override
  String get traveller => 'Cestovatel';

  @override
  String get nightTraveller => 'Noční cestovatel';

  @override
  String get original => 'Originál';

  @override
  String get explorer => 'Průzkumník';

  @override
  String get weekender => 'Víkendár';

  @override
  String get naturalist => 'Přírodovědec';

  @override
  String get historian => 'Historik';

  @override
  String get thrillSeeker => 'Hledač vzrušení';

  @override
  String get culturalBuff => 'Milovník kultury';

  @override
  String get myColors => 'Mé barvy';

  @override
  String get experiences => 'Zážitky';

  @override
  String get done => 'Hotovo';

  @override
  String get experiencesInstructions => 'Začněte klepnutím na tlačítko +!';

  @override
  String get continueText => 'Pokračovat';

  @override
  String get experiencesDescription => 'Co rádi při cestování děláte?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Moje cestovní mapa';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Viděl jsem $percentage% světa';
  }

  @override
  String get requiresOnline =>
      'Omlouváme se, ale Visited vyžaduje aktivní připojení na internet. Otevřete nastavení a ujistěte se, že jsou Wi-Fi nebo mobilní data povolené a režim v letadle je deaktivován.';

  @override
  String get list => 'Seznam';

  @override
  String get more => 'Více';

  @override
  String get myCountrySelections => 'Výběr mých zemí';

  @override
  String get cities => 'Města';

  @override
  String get citiesInstructions =>
      'Klepnutím na libovolnou krajinu začnete vybírat města.';

  @override
  String get missingCitiesEmailTitle => 'Chybějící města!';

  @override
  String get lists => 'Seznamy';

  @override
  String get disputedTerritories => 'Sporné území';

  @override
  String get sponsored => 'Sponzorované';

  @override
  String get places => 'Místa';

  @override
  String get noListsError =>
      'Hups, momentálně nejsou k dispozici žádné seznamy, zkuste to později';

  @override
  String get noInspirationsError =>
      'Hups, momentálně nejsou k dispozici žádné fotografie, zkuste to později';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Vaše nejčastěji navštěvované země:';

  @override
  String get update => 'Aktualizace';

  @override
  String get signup => 'Přihlásit se';

  @override
  String get loginWallSubtitle =>
      'Vytvořte si bezplatný účet pro plné znění Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Po zavření aplikace ztratíte všechny výběry.';

  @override
  String get createAccount => 'Vytvořit účet';

  @override
  String get continueWithoutAccount => 'Pokračovat bez účtu';

  @override
  String get inspirationPromotion =>
      'Inspirují se krásnou cestovatelou fotografií';

  @override
  String get saveStatsPromotion => 'Uložte si cestovní statistiky!';

  @override
  String get selectRegionsPromotion => 'Vybrat státy a provincie';

  @override
  String get experiencesPromotion => 'Sledujte zážitky po celém světě';

  @override
  String get missingListItem =>
      'Chybělo nám něco? Klepnutím zde nám pošlete e -mail a získejte přidání svého oblíbeného místa.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Chybějící položka z $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Navštívil jsem $amount $listName';
  }

  @override
  String get orderPoster => 'Plakát';

  @override
  String get shareMap => 'Sdílet mapu';

  @override
  String get posterLandingPageTitle => 'Získejte svůj plakát';

  @override
  String get posterNotAvailableError =>
      'Nákup plakátů není právě teď k dispozici. Prosím zkuste to znovu později.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping doprava';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## O našich vlastních tiskových mapách\nVytiskněte si svou personalizovanou mapu světa. Přizpůsobte si ji svými vlastními barvami a nechte je doručit přímo do vašeho domova.\n \n### Specifikace:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientace krajiny.\n- Mikro inkoust, kapičky pro přesné tisky, 8 bitové barvy, téměř kvalita tisku fotografií,\n- Saténový papír o tlustém papíru 0,22 mm\n\n### Podrobnosti o přepravě:\nPřeprava z Toronta v Kanadě na kdekoli na světě pomocí Canada Post. Počkejte prosím 2 až 4 týdny na dodání do většiny destinací. Všechny objednávky jsou odeslány v krabici kartonové trubice na odeslanou přepravu. Veškerá platba je řešena společností Apple Pay, nebo Stripe.\n\n\n### Zrušení/vrácení peněz:\nObjednávky jsou zpracovány okamžitě po předložení k nejrychlejšímu možnému obratu. Proto není k dispozici žádné náhrady/zrušení.';

  @override
  String get posterDescriptionMarkdown =>
      '## O našich vlastních tiskových mapách\nVytiskněte si svou personalizovanou mapu světa. Přizpůsobte si ji svými vlastními barvami a nechte je doručit přímo do vašeho domova.\n \n### Specifikace:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientace krajiny.\n- Mikro inkoust, kapičky pro přesné tisky, 8 bitové barvy, téměř kvalita tisku fotografií,\n- Saténový papír o tlustém papíru 0,22 mm\n\n### Podrobnosti o přepravě:\nPřeprava z Toronta v Kanadě na kdekoli na světě pomocí Canada Post. Počkejte prosím 2 až 4 týdny na dodání do většiny destinací. Všechny objednávky jsou odeslány v krabici kartonové trubice na odeslanou přepravu. Veškerá platba je řešena společností Google Pay nebo Stripe.\n\n\n### Zrušení/vrácení peněz:\nObjednávky jsou zpracovány okamžitě po předložení k nejrychlejšímu možnému obratu. Proto není k dispozici žádné náhrady/zrušení.';

  @override
  String get posterCustomizeTitle => 'Přizpůsobte plakát';

  @override
  String get enterShippingAddress => 'Zadejte dodací adresu';

  @override
  String get price => 'Cena';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + daň';
  }

  @override
  String get showSelections => 'Zobrazit výběr';

  @override
  String get posterNoRefunds =>
      'Po vytištění plakátu nejsou k dispozici žádné náhrady.';

  @override
  String get posterReviewOrder => 'Zkontrolujte svou objednávku';

  @override
  String get email => 'E-mailem';

  @override
  String get emailEmptyError => 'Zadejte prosím svůj e -mail';

  @override
  String get fullName => 'Celé jméno';

  @override
  String get fullNameEmptyError => 'Prosím zadejte své celé jméno';

  @override
  String get streetAddressEmptyError => 'Zadejte adresu své ulice';

  @override
  String get cityEmptyError => 'Zadejte prosím své město';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Zadejte prosím své $fieldName';
  }

  @override
  String get country => 'Země';

  @override
  String get countryEmptyError => 'Zadejte prosím svou zemi';

  @override
  String get posterReviewOrderTitle => 'Zkontrolujte svou objednávku';

  @override
  String get buyNow => 'Kup nyní';

  @override
  String get secureCheckoutDisclaimer =>
      'Zabezpečená pokladna poskytovaná Stripe';

  @override
  String get total => 'Celkový';

  @override
  String get tax => 'Daň';

  @override
  String get subtotal => 'Subtotál';

  @override
  String get posterProductName => 'Na vlastní návštěvě mapového plakátu';

  @override
  String get shipping => 'Lodní doprava';

  @override
  String get posterOrderReceivedTitle => 'objednávka byla obdržena';

  @override
  String get posterOrderReceivedSubtitle => 'Obdrželi jsme vaši objednávku!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Podívejte se na svůj e -mail, kde najdete další aktualizace. \nPovolte, aby váš plakát dorazil až 4 týden. \nPokud máte nějaké dotazy, pošlete nám e -mail na adresu [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Stav objednávky potištěného plakátu';

  @override
  String get moreInfo => 'Více informací';

  @override
  String get logoutConfirm => 'Chtěli byste se odhlásit z aplikace?';

  @override
  String get emailNotAvailable => 'Ten e -mail byl odebrán.';

  @override
  String get alphabetical => 'Abecední';

  @override
  String get firstTimeLiveTutorial =>
      'Zadáním své domovské země a města přizpůsobíte své zkušenosti s aplikací.';

  @override
  String get firstTimeBeenTutorial =>
      'Výběr místa, kde jste byli, vám umožní zobrazit mapu všech zemí, ve kterých jste byli, a zobrazit osobní statistiky.';

  @override
  String get progressTooltipGoal =>
      'Vaše cestovní cíle jsou založeny na počtu zemí, které \"Chcete\" procestovat, v porovnání se zeměmi, kde jste \"Byli\".';

  @override
  String get progressTooltipRank =>
      'Toto číslo ukazuje, jak si stojíte v porovnání s cestovateli po celém světě.  Svou pozici můžete zvýšit tím, že procestujete více zemí.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Tento graf je založen na počtu zemí, které jste navštívili, v porovnání s celkovým počtem zemí světa.';

  @override
  String get sortBy => 'Seřadit podle';

  @override
  String get updateWishlist => 'Aktualizovat seznam přání';

  @override
  String get mapInfo =>
      'Kliknutím na zemi vyberte, zda jste byli, chcete nebo žijete. Můžete také kliknout na ikonu, kterou najdete v levém horním rohu pro zobrazení seznamu.';

  @override
  String get oneTimePurchase => 'Vše je možné zakoupit jednorázově!';

  @override
  String get contact => 'Kontakt';

  @override
  String get contactUs => 'Kontaktujte nás';

  @override
  String get noCitiesSelected => 'Zatím jste nevybrali žádné město...';

  @override
  String get updateTravelGoal => 'Aktualizujte cíl cestování';

  @override
  String get travelGoalComplete =>
      'Gratulujeme! \n\nyou jste dokončili svůj cestovní cíl! \n\ntap tlačítko + pro přidání dalších zemí.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'E -mail není spojen s účtem $email. Chtěli byste to teď vytvořit?';
  }

  @override
  String get tryAgain => 'Zkuste to znovu';

  @override
  String get itineraries => 'Cestovní plány';

  @override
  String get itinerary => 'Cestovní plán';

  @override
  String get place => 'Místo';

  @override
  String get itinerariesDescription =>
      'Toto jsou místa, o která jste projevili zájem.\nTento průvodce vám pomůže naplánovat vaši příští dovolenou.';

  @override
  String get addMore => 'Přidat další';

  @override
  String get interests => 'Zájmy';

  @override
  String get selection => 'Výběr';

  @override
  String get goal => 'Fotbalová branka';

  @override
  String get noItineraries => 'Žádné Itineráře';

  @override
  String get noItinerariesExplanation =>
      'Prosím, přidejte nějaká místa, inspirace nebo zážitky, aby se vaše itineráře generovaly automaticky.';

  @override
  String get clusterPins => 'Seskupit piny';

  @override
  String get toggleRegions => 'Zobrazit regiony při zoomování';

  @override
  String get mapProjection => 'Mapa - projekce';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Ekvirektangulární';

  @override
  String get yourTravellerType => 'Váš typ cestovatele:';

  @override
  String get yourHotelPreferences => 'Vaše preference hotelu:';

  @override
  String get budget => 'Levné';

  @override
  String get midScale => 'Střední kategorie';

  @override
  String get luxury => 'Luxusní';

  @override
  String get noTravellerType =>
      'Přidejte položky na svůj seznam přání a zjistěte, jaký jste cestovatel.';

  @override
  String get unlockLived => 'Odemkněte Prožitky';

  @override
  String get unlockLivedDescription => 'Vyberte, kde jste dříve žili, na mapě!';

  @override
  String get futureFeaturesDescription => '...a všechny budoucí funkce';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Vaše nejčastěji navštěvovaná země:';

  @override
  String get departureDate => 'Datum odjezdu';

  @override
  String get returnDate => 'Datum návratu';

  @override
  String get hotels => 'Hotely';

  @override
  String get food => 'Jídlo';

  @override
  String get travelDates => 'Data cestování';

  @override
  String get posterForMe => 'Pro mě';

  @override
  String get posterSendGift => 'Poslat dárek';

  @override
  String get addSelections => 'Přidat výběry';

  @override
  String get posterType => 'Typ plakátu';

  @override
  String get help => 'Pomoc';

  @override
  String get tutorialMap => 'Klepnutím na zemi vyberte: byl, chtěl a žil.';

  @override
  String get tutorialMapList =>
      'Klepnutím na ikonu seznamu (levý horní roh) vyberte podle seznamu.';

  @override
  String get tutorialCountryDetails =>
      'Klepněte na zemi a poté na \"více\" pro výběr podle regionu.';

  @override
  String get tutorialItems =>
      'Posunutím přepínače vyberte, jak chcete položky vybrat.';

  @override
  String get tutorialInspirations =>
      'Přejetím prstem doprava nebo doleva se přesunete na další kartu.';

  @override
  String get lifetime => 'Doživotní';

  @override
  String get chooseYourPlan => 'Vyberte si svůj plán';

  @override
  String get requestARefund => 'Požádat o vrácení peněz';

  @override
  String get noPurchasesFound => 'Nebyly nalezeny žádné nákupy.';

  @override
  String get noProductsAvailable => 'Žádné dostupné produkty';

  @override
  String get posterLandingAppBar => 'Přineste si své příběhy domů';

  @override
  String get posterLandingSubHeading => 'Vaše cestování, váš příběh';

  @override
  String get posterLandingSubDescription =>
      'Vaše cesty jsou víc než výlety, jsou to příběhy, vzpomínky a milníky. Proměňte tyto nezapomenutelné okamžiky na personalizovanou mapu světa, která je stejně jedinečná jako vaše dobrodružství.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Mapa vašich úspěchů: Zvýrazněte každý cíl, od vašeho prvního velkého výletu až po vaše nejodvážnější dobrodružství.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Oslavte každou cestu: Prožijte své každodenní cesty znovu s nádherně vytvořeným plakátem, který má inspirovat.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Dárek, který budou pokladem: Překvapte spolucestovatele vlastní mapou znázorňující jejich cestu, která je ideální pro narozeniny, milníky nebo jen tak.';

  @override
  String get posterLandingHowItWorks => 'Jak to funguje!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Přizpůsobte si svůj design: Vyberte si barvy, styly a označte své cesty (nebo jejich!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Náhled vaší mapy: Podívejte se, jak ožívá před vaší objednávkou.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Bezpečná platba: Rychlá a bezpečná s Apple Pay nebo Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Bezpečná platba: Rychlá a bezpečná s Google Pay nebo Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Připraveno k vystavení: Doručíme vám jej přímo k vašim dveřím (nebo jejich dveřím).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Zkušenosti spolucestujících';

  @override
  String get posterLandingCustomerReview1 =>
      'Tato mapa je skvělý způsob, jak sledovat všude, kde jsem cestoval, a plánovat naše budoucí cesty. Kvalita je solidní a vypadá to úžasně, když mi visí v kanceláři. Dokonce jsem jednu dostal pro svého bratra a on nemohl, přestat mluvit o tom, jak je to skvělé! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Při práci na plavbě jsem procestoval více než 150 přístavů. Tato mapa je skvělým doplňkem mého obývacího pokoje jako vzpomínka na všechna léta strávená na moři. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Skvělý dárek ke Dni matek. Moje máma byla nadšená! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Vytiskl jsem mapu míst, která jsem chtěl navštívit se svým gf. Byl to skvělý vánoční dárek. Také vysoká kvalita. Brad J.';

  @override
  String get posterLandingSpecifications => 'Specifikace';

  @override
  String get posterLandingSpecification1 =>
      '• Rozměry: 16\" x 20\" (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '• Orientace: Na šířku';

  @override
  String get posterLandingSpecification3 =>
      '• Kvalita tisku: Mikroinkoust, kapičky pro přesné tisky.  8bitové barvy, téměř kvalita tisku fotografií.';

  @override
  String get posterLandingSpecification4 =>
      '• Papír: saténový papír o tloušťce 0,22 mm';

  @override
  String get posterLandingShippingHeader => 'Podrobnosti o dopravě';

  @override
  String get posterLandingShipping1 =>
      '• Odeslání z Toronta v Kanadě kamkoli na světě pomocí Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Doručení do většiny destinací počítejte 2–4 týdny.';

  @override
  String get posterLandingShipping3 =>
      '• Všechny objednávky jsou zabaleny v kartonové krabici na dodací adresu, kterou zadáte.';

  @override
  String get posterLandingCancellationHeader => 'Zrušení/vrácení peněz:';

  @override
  String get posterLandingCancellationBody =>
      'Vrácení peněz je možné před odesláním plakátu do tiskárny, což může trvat až 24 hodin.  Po zpracování vaší objednávky není možné vrácení peněz/zrušení.  Po vytištění objednávky obdržíte e-mail.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
