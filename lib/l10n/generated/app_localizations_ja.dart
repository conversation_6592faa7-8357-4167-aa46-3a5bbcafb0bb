// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => '言語';

  @override
  String get pickEmailApp => 'メールアプリを選択してください';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return '私は$amountヶ国を訪れました！あなたはいくつ訪れましたか？ www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return '私は$amount都市を訪れました！あなたはいくつ訪れましたか？ www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return '私は$amount $listNameを訪れました！あなたはいくつ訪れましたか？ www.visitedapp.com';
  }

  @override
  String get clear => '消去';

  @override
  String get been => '行った';

  @override
  String get want => '行きたい';

  @override
  String get live => '住んでいる';

  @override
  String get lived => '住んでいました';

  @override
  String get water => '海';

  @override
  String get land => '陸地';

  @override
  String get borders => '国境';

  @override
  String get labels => 'ラベル';

  @override
  String get legend => '凡例';

  @override
  String get inspiration => 'インスピレーション';

  @override
  String get inspirations => 'インスピレーション';

  @override
  String get delete => '削除';

  @override
  String get unlockVisitedUpsellTitle => 'もっと見たいですか？';

  @override
  String get unlockVisitedUpsellSubtitle => 'すべての機能のロックを解除してVisitedをフルに活用する';

  @override
  String get checkTheDetails => '詳細をチェック';

  @override
  String get moreInspirationsComingSoon => '画像を追加予定です。またチェックしてください！';

  @override
  String get unlockPremiumFeatures => 'プレミアム機能のロックを解除';

  @override
  String get purchased => '購入しました！';

  @override
  String get buy => '購入する';

  @override
  String get restorePurchases => '購入を復元';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'よろしいですか？';

  @override
  String get deleteInspirationConfirmMessage =>
      'このカードは永久に削除されます。この画像は元には戻りません。';

  @override
  String get cancel => 'キャンセル';

  @override
  String get map => '地図';

  @override
  String get progress => '現状';

  @override
  String get myTravelGoal => '旅行の目標';

  @override
  String goalRemaining(int remaining) {
    return 'あと$remaining カ国！';
  }

  @override
  String get top => '上位';

  @override
  String get ofTheWorld => '世界の国の';

  @override
  String get countries => 'カ国';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return '$countryから訪れる人気の国';
  }

  @override
  String get login => 'ログイン';

  @override
  String get logout => 'ロフアウト';

  @override
  String get enterYourEmail => 'メールアドレスを入力';

  @override
  String get privacyPolicy => 'プライバシーポリシー';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-japanese/';

  @override
  String get termsOfUse => '利用規約';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-japanese/';

  @override
  String get errorTitle => 'エラー';

  @override
  String get enterValidEmail => '正しいメールアドレスを入力してください';

  @override
  String get settings => '設定';

  @override
  String get whereDoYouLive => 'どこに住んでいますか？';

  @override
  String get whereHaveYouBeen => 'どこに行ったことがありますか？';

  @override
  String get whereDoYouFlyFrom => 'どこから飛びますか？';

  @override
  String get next => '次へ';

  @override
  String get missingAirports =>
      'お探しの場所が見つかりませんか？ ****************************へお知らせください';

  @override
  String get missingAirportsEmailTitle => '空港が見つかりません！';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => 'Visitedへようこそ';

  @override
  String get welcomeSubtitle => '人生の旅が始まります';

  @override
  String get getStarted => 'はじめる';

  @override
  String get privacyAgreement => 'プライバシー契約';

  @override
  String get privacyAgreementSubtitle => 'Visitedの利用を続ける前に次の事項への同意をお願いします。';

  @override
  String get privacyAgreementTermsMarkdown =>
      'あなたはこの枠にチェックをすることで、Arriving in High Heelsのプライバシーポリシー(https://www.arrivinginhighheels.com/privacy-policy)および利用規約(https://www.arrivinginhighheels.com/terms-of-use)に目を通し、内容に同意したものとみなされます。';

  @override
  String get privacyAgreementOptIn =>
      '私は、Arriving in High Heelsから、セール、プロモーション、オファー、ニュースレターの告知など、私の興味に関わるであろう製品、アプリ、サービスに関する情報やオファーを含む電子メールを受信することに同意します。私は、プライバシーポリシーに記載のとおり、電子メール内の「登録解除」のリンクをクリックすることで、いつでも本同意を取り消すことができます。';

  @override
  String get submit => '送信';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired => 'Visitedの利用を続けるためには次の事項両方への同意が必要です。';

  @override
  String get deleteAccount => 'アカウントを削除';

  @override
  String get removeAdsUpsell => '広告を';

  @override
  String get deleteAccountWarning =>
      'アカウントを削除すると私たちのサーバーからあなたに関する情報がすべて消去されます。このプロセスは取り消しできません。';

  @override
  String get about => '基本情報';

  @override
  String get popularity => '人気';

  @override
  String get regions => '地域';

  @override
  String get population => '人口';

  @override
  String get size => '面積';

  @override
  String get coverage => '訪問エリア';

  @override
  String get percentOfCountryVisited => 'この国の％を訪れました';

  @override
  String get visited => '訪問済';

  @override
  String get notes => 'メモ';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'カスタマイズ';

  @override
  String get onlyCountSovereign => '主権国家のみカウント';

  @override
  String get countUkSeparately => '英国の国々を個別にカウント';

  @override
  String get showLegend => '凡例を表示';

  @override
  String get showLivedPin => '住んだことのあるピンを表示';

  @override
  String get useMyColours => 'マイカラーを適用';

  @override
  String get mapColors => 'マップの色';

  @override
  String get traveller => '旅人';

  @override
  String get nightTraveller => '夜の旅人';

  @override
  String get original => 'オリジナル';

  @override
  String get explorer => '冒険家';

  @override
  String get weekender => '週末旅行者';

  @override
  String get naturalist => 'ナチュラリスト';

  @override
  String get historian => '歴史家';

  @override
  String get thrillSeeker => 'チャレンジャー';

  @override
  String get culturalBuff => 'カルチャーマニア';

  @override
  String get myColors => 'マイカラー';

  @override
  String get experiences => '体験';

  @override
  String get done => '完了';

  @override
  String get experiencesInstructions => '＋をタップしてはじめる';

  @override
  String get continueText => '続ける';

  @override
  String get experiencesDescription => '旅行先で何をしたいですか？';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-%e6%97%85%e8%a1%8c%e3%82%a2%e3%83%97%e3%83%aa/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'マイ旅行マップ';

  @override
  String percentOfWorldSeen(int percentage) {
    return '世界の$percentage%を訪れました';
  }

  @override
  String get requiresOnline =>
      'Visitedは有効なネットワーク接続を必要とします。設定を開いて、Wi-Fiまたはモバイル通信がオン、機内モードがオフになっていることを確認してください。';

  @override
  String get list => 'リスト';

  @override
  String get more => 'さらに見る';

  @override
  String get myCountrySelections => 'マイセレクション';

  @override
  String get cities => '都市';

  @override
  String get citiesInstructions => '国をタップしてから都市を選んでください';

  @override
  String get missingCitiesEmailTitle => '都市が見つかりません！';

  @override
  String get lists => 'リスト';

  @override
  String get disputedTerritories => '係争地域';

  @override
  String get sponsored => 'スポンサー';

  @override
  String get places => '場所';

  @override
  String get noListsError => 'まだリストがありません。後日またお試しください。';

  @override
  String get noInspirationsError => 'まだ画像がありません。後日またお試しください。';

  @override
  String get mostFrequentlyVisitedCountries => '最も頻繁に訪問する国：';

  @override
  String get update => '更新';

  @override
  String get signup => 'サインアップ';

  @override
  String get loginWallSubtitle => 'Visited のフルバージョンを体験するための無料アカウントを作成します';

  @override
  String get loseAllSelectionsWarning => 'アプリを閉じた後、すべての選択が失われます。';

  @override
  String get createAccount => 'アカウントの作成';

  @override
  String get continueWithoutAccount => 'アカウントなしで続行する';

  @override
  String get inspirationPromotion => '美しい旅行写真にインスピレーションを得る';

  @override
  String get saveStatsPromotion => 'あなたの旅行統計を保存!';

  @override
  String get selectRegionsPromotion => '州と都道府県の選択';

  @override
  String get experiencesPromotion => '世界中のトラック体験';

  @override
  String get missingListItem =>
      '私たちは何かを逃しましたか？ここをタップしてメールを送信してお気に入りの場所を追加してください。';

  @override
  String missingListItemEmailTitle(String list) {
    return '$listからの不足しているアイテム';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return '私は$amount $listNameを訪問しました';
  }

  @override
  String get orderPoster => 'ポスター';

  @override
  String get shareMap => 'マップを共有します';

  @override
  String get posterLandingPageTitle => 'ポスターを手に入れよう';

  @override
  String get posterNotAvailableError => 'ポスターの購入は現在入手できません。後でもう一度やり直してください。';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping 配送';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## カスタムプリントマップについて\nパーソナライズされた世界地図を印刷します。独自の色でカスタマイズし、家にまっすぐに配達してください。\n \n### 仕様：\n- 16\" x 20\" (40.64cm x 50.8cm)\n- 風景の向き。\n- マイクロインク、正確なプリント用の液滴、8ビット色、ほぼ写真のプリント品質、\n- 0.22mmの厚いサテン紙\n\n### 出荷の詳細：\nカナダのトロントからカナダポストを使用して世界中のどこからでも配送。ほとんどの目的地への配達には2〜4週間かかります。すべての注文は、送信された配送先住所に段ボールチューブボックスに巻き上げられます。\n支払いはどのように処理されますか？すべての支払いは、Apple Pay、Google Pay、またはStripeによって処理されます。\n\n\n### キャンセル/払い戻し：\n注文は、可能な限り最速のターンアラウンドのために提出された直後に処理されます。したがって、利用可能な払い戻し/キャンセルはありません。';

  @override
  String get posterDescriptionMarkdown =>
      '## カスタムプリントマップについて\nパーソナライズされた世界地図を印刷します。独自の色でカスタマイズし、家にまっすぐに配達してください。\n \n### 仕様：\n- 16\" x 20\" (40.64cm x 50.8cm)\n- 風景の向き。\n- マイクロインク、正確なプリント用の液滴、8ビット色、ほぼ写真のプリント品質、\n- 0.22mmの厚いサテン紙\n\n### 出荷の詳細：\nカナダのトロントからカナダポストを使用して世界中のどこからでも配送。ほとんどの目的地への配達には2〜4週間かかります。すべての注文は、送信された配送先住所に段ボールチューブボックスに巻き上げられます。\n支払いはどのように処理されますか？すべての支払いは、Apple Pay、Google Pay、またはStripeによって処理されます。\n\n\n### キャンセル/払い戻し：\n注文は、可能な限り最速のターンアラウンドのために提出された直後に処理されます。したがって、利用可能な払い戻し/キャンセルはありません。';

  @override
  String get posterCustomizeTitle => 'ポスターをカスタマイズします';

  @override
  String get enterShippingAddress => '配送先住所を入力します';

  @override
  String get price => '価格';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice +税';
  }

  @override
  String get showSelections => '選択を表示します';

  @override
  String get posterNoRefunds => 'ポスターが印刷された後、払い戻しはありません。';

  @override
  String get posterReviewOrder => 'ご注文を確認してください';

  @override
  String get email => 'Eメール';

  @override
  String get emailEmptyError => 'あなたのメールアドレスを入力してください';

  @override
  String get fullName => 'フルネーム';

  @override
  String get fullNameEmptyError => 'フルネームを入力してください';

  @override
  String get streetAddressEmptyError => 'あなたの通りの住所を入力してください';

  @override
  String get cityEmptyError => 'あなたの街に入ってください';

  @override
  String fieldEmptyError(Object fieldName) {
    return '$fieldName を入力してください';
  }

  @override
  String get country => '国';

  @override
  String get countryEmptyError => 'あなたの国に入ってください';

  @override
  String get posterReviewOrderTitle => 'ご注文を確認してください';

  @override
  String get buyNow => '今買う';

  @override
  String get secureCheckoutDisclaimer => 'Stripeが提供する安全なチェックアウト';

  @override
  String get total => '合計';

  @override
  String get tax => '税';

  @override
  String get subtotal => '小計';

  @override
  String get posterProductName => 'カスタム訪問マップポスター';

  @override
  String get shipping => '運送';

  @override
  String get posterOrderReceivedTitle => 'ご注文承りました';

  @override
  String get posterOrderReceivedSubtitle => 'ご注文を受け取りました！';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'その他の更新については、メールを確認してください。\nポスターが到着するまで最大4週間許可してください。\nご質問がある場合は、[<EMAIL>]（<EMAIL>）までメールでお問い合わせください。';

  @override
  String get posterOrderReceivedEmailSubject => '印刷されたポスター注文ステータス';

  @override
  String get moreInfo => '詳しくは';

  @override
  String get logoutConfirm => 'アプリからログアウトしますか？';

  @override
  String get emailNotAvailable => 'そのメールが取得されました。';

  @override
  String get alphabetical => 'アルファベット順';

  @override
  String get firstTimeLiveTutorial => 'お住まいの国や都市名を入力すると、アプリの体験がパーソナライズされます。';

  @override
  String get firstTimeBeenTutorial =>
      '行ったことのある国を選択すると、行ったことのあるすべての国の地図を表示し、個人の統計情報を見ることができます。';

  @override
  String get progressTooltipGoal =>
      'あなたの旅行目標は、あなたが「行きたい」国の数と、「行った」国の数に基づいています。';

  @override
  String get progressTooltipRank =>
      'この数字は、あなたが世界中の旅行者と比べてどのようなランクに位置しているかを示しています。 より多くの国に旅行することで、順位を上げることができます。';

  @override
  String get progressTooltipPercentageOfWorld =>
      'このグラフは、あなたが行ったことのある国の数と、世界の総国数を比較したものです。';

  @override
  String get sortBy => '並び替え';

  @override
  String get updateWishlist => 'ウィッシュリストの更新';

  @override
  String get mapInfo =>
      '国名をクリックすると、行った、行きたい、住んでいるを選択できます。また、左上のアイコンをクリックするとリスト表示されます。';

  @override
  String get oneTimePurchase => 'すべて1回限りの購入です。';

  @override
  String get contact => 'お問い合わせ';

  @override
  String get contactUs => 'お問い合わせ';

  @override
  String get noCitiesSelected => 'まだ都市を選択していません...';

  @override
  String get updateTravelGoal => '旅行目標を更新します';

  @override
  String get travelGoalComplete =>
      'おめでとうございます！\n\nyouが旅行の目標を達成しました！ \n\ntap +ボタンを追加して、さらに国を追加します。';

  @override
  String loginEmailNotFoundError(String email) {
    return '電子メール$emailに関連付けられたアカウントはありません。今すぐ作成しますか？';
  }

  @override
  String get tryAgain => '再チャレンジ';

  @override
  String get itineraries => '旅行プラン';

  @override
  String get itinerary => '旅行プラン';

  @override
  String get place => '場所';

  @override
  String get itinerariesDescription => 'あなたが興味を示した場所です\n次の休暇のプランニングにお役立てください。';

  @override
  String get addMore => 'もっと追加する';

  @override
  String get interests => '興味';

  @override
  String get selection => '選択';

  @override
  String get goal => 'ゴール';

  @override
  String get noItineraries => 'イチニェラリ無し';

  @override
  String get noItinerariesExplanation =>
      'いくつかの場所、インスピレーション、または体験を追加して、自動的にイチネラリが生成されるのを見てください。';

  @override
  String get clusterPins => 'クラスター地図マーカー';

  @override
  String get toggleRegions => 'ズームイン時に地域を表示';

  @override
  String get mapProjection => '地図投影';

  @override
  String get mercator => 'メルカトル';

  @override
  String get equirectangular => '正積円筒';

  @override
  String get yourTravellerType => 'あなたの旅行者タイプ:';

  @override
  String get yourHotelPreferences => 'あなたのホテルの好み:';

  @override
  String get budget => '予算';

  @override
  String get midScale => '中間スケール';

  @override
  String get luxury => '高級';

  @override
  String get noTravellerType => 'バケットリストにアイテムを追加して、どんなタイプの旅行者かを発見してください。';

  @override
  String get unlockLived => '生活をアンロック';

  @override
  String get unlockLivedDescription => '地図上で以前に住んだことのある場所を選択してください！';

  @override
  String get futureFeaturesDescription => '...およびすべての将来の機能';

  @override
  String get yourMostFrequentlyVisitedCountry => '最も頻繁に訪れる国:';

  @override
  String get departureDate => '出発日';

  @override
  String get returnDate => '帰国日';

  @override
  String get hotels => 'ホテル';

  @override
  String get food => '食べ物';

  @override
  String get travelDates => '旅行日程';

  @override
  String get posterForMe => '自分用';

  @override
  String get posterSendGift => 'ギフトを送る';

  @override
  String get addSelections => '選択を追加';

  @override
  String get posterType => 'ポスタータイプ';

  @override
  String get help => 'ヘルプ';

  @override
  String get tutorialMap => '国をタップして選択します：以前に住んでいた、住んでいた、住んでいた。';

  @override
  String get tutorialMapList => 'リストアイコン（左上隅）をタップしてリストから選択します。';

  @override
  String get tutorialCountryDetails => '国をタップし、「詳細」をタップして地域別に選択します。';

  @override
  String get tutorialItems => 'トグルをスライドして、項目の選択方法を選択します。';

  @override
  String get tutorialInspirations => '右または左にスワイプして次のカードに移動します。';

  @override
  String get lifetime => '生涯';

  @override
  String get chooseYourPlan => 'プランを選択してください';

  @override
  String get requestARefund => '返金をリクエストする';

  @override
  String get noPurchasesFound => '購入が見つかりません。';

  @override
  String get noProductsAvailable => '利用可能な製品はありません';

  @override
  String get posterLandingAppBar => 'ストーリーを持ち帰ろう';

  @override
  String get posterLandingSubHeading => 'あなたの旅、あなたの物語';

  @override
  String get posterLandingSubDescription =>
      '旅は単なる旅行ではありません。物語、思い出、そして節目となる出来事です。忘れられない瞬間を、冒険と同じくらいユニークな、あなただけのオリジナル世界地図に刻みましょう。';

  @override
  String get posterLandingPromoBullet1 =>
      '• 達成の軌跡マップ：初めての大旅行から、最も大胆な冒険まで、すべての目的地をハイライト表示します。';

  @override
  String get posterLandingPromoBullet2 =>
      '• すべての旅を称える：美しくデザインされた、インスピレーションあふれるポスターで、日々の旅を追体験しましょう。';

  @override
  String get posterLandingPromoBullet3 =>
      '• 大切な人に贈るギフト：旅の軌跡を描いたカスタムマップで、旅仲間を驚かせましょう。誕生日や節目、あるいは特別な日など、どんな時でもぴったりです。';

  @override
  String get posterLandingHowItWorks => '使い方！';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. デザインをカスタマイズ：色やスタイルを選び、あなたの旅（または相手の旅！）をマークします。';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. マップをプレビュー：注文前にマップの仕上がりを確認できます。';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. 安全な決済：Apple PayまたはStripeで、迅速かつ安全にお支払いいただけます。';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. 安全な決済：Google PayまたはStripeで、迅速かつ安全にお支払いいただけます。';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. 展示準備完了: お客様 (または相手方) の玄関先まで直接発送いたします。';

  @override
  String get posterLandingCustomerReviewsHeader => '旅行者の体験談';

  @override
  String get posterLandingCustomerReview1 =>
      '「この地図は、これまで旅した場所を記録し、今後の旅行を計画するのにとても便利です。 品質もしっかりしていて、オフィスに飾っています。 弟にもプレゼントしたのですが、あまりのかっこよさに話が止まりませんでした。」 - ジョンC.';

  @override
  String get posterLandingCustomerReview2 =>
      '「クルーズの仕事をしながら、150以上の港を旅してきました。この地図は、私のリビングルームの素晴らしいアクセントになっています。- ベティ K.';

  @override
  String get posterLandingCustomerReview3 =>
      '「母の日に最高の贈り物です。母はとても感動していました。サマンサ・W.';

  @override
  String get posterLandingCustomerReview4 =>
      '「彼女と一緒に行きたい場所の地図を印刷しました。素晴らしいクリスマスプレゼントでした。クオリティも高いです。ブラッドJ.';

  @override
  String get posterLandingSpecifications => '仕様';

  @override
  String get posterLandingSpecification1 => '- 寸法 16「 x 20」 (40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '- 向き 横向き';

  @override
  String get posterLandingSpecification3 =>
      '- 印刷品質： マイクロ インク、精密な印刷物のための滴。 8ビットカラー、ほとんど写真印刷品質。';

  @override
  String get posterLandingSpecification4 => '- ペーパー： 0.22mm の厚いサテンのペーパー';

  @override
  String get posterLandingShippingHeader => '発送詳細';

  @override
  String get posterLandingShipping1 => '- カナダ・トロントからカナダポストを利用して世界中どこへでも発送。';

  @override
  String get posterLandingShipping2 => '- 配送には2～4週間かかります。';

  @override
  String get posterLandingShipping3 =>
      '- すべての注文は、送信された配送先住所に段ボールのチューブボックスでロールアップされます。';

  @override
  String get posterLandingCancellationHeader => 'キャンセル/払い戻し';

  @override
  String get posterLandingCancellationBody =>
      '払い戻しは、ポスターが印刷所に送られる前であれば可能です。 注文が処理された後は、返金/キャンセルはできません。 ご注文のポスターが印刷されましたら、Eメールにてお知らせいたします。';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
