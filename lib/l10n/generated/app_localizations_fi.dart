// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Finnish (`fi`).
class AppLocalizationsFi extends AppLocalizations {
  AppLocalizationsFi([String locale = 'fi']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Kieli';

  @override
  String get pickEmailApp => 'Valitse sähköpostisovelluksesi';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Olen käynyt $amount maassa! Monessako sinä olet käynyt? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Olen käynyt $amount kaupungissa! Monessako sinä olet käynyt? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Olen käynyt $amount $listName kohteessa! Monessako sinä olet käynyt? www.visitedapp.com';
  }

  @override
  String get clear => 'Tyhjennä';

  @override
  String get been => 'Käyty';

  @override
  String get want => 'Toivelista';

  @override
  String get live => 'Asuttu';

  @override
  String get lived => 'Elänyt';

  @override
  String get water => 'Vesi';

  @override
  String get land => 'Maa';

  @override
  String get borders => 'Rajat';

  @override
  String get labels => 'Merkit';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inspiraatio';

  @override
  String get inspirations => 'Inspiraatiot';

  @override
  String get delete => 'Poista';

  @override
  String get unlockVisitedUpsellTitle => 'Haluatko nähdä lisää?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Avaa kaikki ominaisuudet ja nauti Visited-sovelluksesta kokonaisuudessaan';

  @override
  String get checkTheDetails => 'Tarkista tiedot';

  @override
  String get moreInspirationsComingSoon =>
      'Me teemme töitä lisätäksemme uusia kuvia. Palaa pian tälle sivulle!';

  @override
  String get unlockPremiumFeatures => 'Avaa premium-ominaisuudet';

  @override
  String get purchased => 'Ostettu!';

  @override
  String get buy => 'Osta';

  @override
  String get restorePurchases => 'Palauta osto';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Oletko varma?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Tämän kortin poistaminen on peruuttamaton ratkaisu. Et voi palauttaa kuvaa jälkeenpäin.';

  @override
  String get cancel => 'Peru';

  @override
  String get map => 'Kartta';

  @override
  String get progress => 'Edistyminen';

  @override
  String get myTravelGoal => 'Minun matkatavoitteeni';

  @override
  String goalRemaining(int remaining) {
    return '$remaining jäljellä!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'maailmasta!';

  @override
  String get countries => 'maata';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Vierailluimmat maat kohteesta $country:';
  }

  @override
  String get login => 'Kirjaudu sisään';

  @override
  String get logout => 'Kirjaudu ulos';

  @override
  String get enterYourEmail => 'Anna sähköpostisi';

  @override
  String get privacyPolicy => 'Tietosuojakäytännöt';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-finnish/';

  @override
  String get termsOfUse => 'Käyttöehdot';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-finnish/';

  @override
  String get errorTitle => 'Hupsista!';

  @override
  String get enterValidEmail => 'Anna toimiva sähköposti';

  @override
  String get settings => 'Asetukset';

  @override
  String get whereDoYouLive => 'Missä sinä asut?';

  @override
  String get whereHaveYouBeen => 'Missä olet käynyt?';

  @override
  String get whereDoYouFlyFrom => 'Mistä paikasta lennät?';

  @override
  String get next => 'Seuraava';

  @override
  String get missingAirports =>
      'Etkö löydä etsimääsi? Lähetä meille sähkö<NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Puuttuvia lentokenttiä!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Tervetuloa Visited-sovellukseen';

  @override
  String get welcomeSubtitle => 'Elämäsi mahtavin seikkailu odottaa sinua!';

  @override
  String get getStarted => 'Aloita';

  @override
  String get privacyAgreement => 'Yksityisyysehdot';

  @override
  String get privacyAgreementSubtitle =>
      'Hyväksy seuraavat ehdot ennen kuin voit jatkaa Visited-sovelluksen käyttöä.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Hyväksymällä tämän kohdan ilmoitat, että olet lukenut ja hyväksyt Arriving in High Heelsin ehdot [Tietosuojakäytännöt](https://www.arrivinginhighheels.com/privacy-policy) ja [Käyttöehdot](https://www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Hyväksyn sähköiset viestit, jotka sisältävät tietoa ja tarjouksia minua mahdollisesti kiinnostavista tuotteista, sovelluksista ja palveluista, Arriving in High Heelsiltä. Viestit voivat esimerkiksi olla ilmoituksia alennuksista, promootioita, tarjouksia ja uutiskirjeitä. Voin perua luvan milloin tahansa Tietosuojakäytännöissä annettujen ohjeiden mukaisesti tai painamalla \"peru tilaus\" -linkkiä sähköisessä viestissä.';

  @override
  String get submit => 'Lähetä';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Sinun täytyy hyväksyä sekä ehdot että valinnat, että voit jatkaa Visited-sovelluksen käyttöä.';

  @override
  String get deleteAccount => 'Poista tili';

  @override
  String get removeAdsUpsell =>
      'Haluatko jäädä paitsi mainoksista ja perua sähköpostitse lähetettävän markkinoinnin?';

  @override
  String get deleteAccountWarning =>
      'Tilisi poistaminen poistaa kaikki tietosi servereiltämme. \n Tätä prosessia ei voida perua.';

  @override
  String get about => 'Tietoa';

  @override
  String get popularity => 'Suosio';

  @override
  String get regions => 'Alueet';

  @override
  String get population => 'Asukasluku';

  @override
  String get size => 'Koko';

  @override
  String get coverage => 'Kattavuus';

  @override
  String get percentOfCountryVisited => '% maasta vierailtu';

  @override
  String get visited => 'vierailtu';

  @override
  String get notes => 'Huomiot';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Kustomoi';

  @override
  String get onlyCountSovereign => 'Laske vain itsenäiset valtiot';

  @override
  String get countUkSeparately => 'Laske Brittien maat erikseen';

  @override
  String get showLegend => 'Näytä legenda';

  @override
  String get showLivedPin => 'Näytä \"asuttu\"-merkinnät';

  @override
  String get useMyColours => 'Käytä värejäni';

  @override
  String get mapColors => 'Kartan värit';

  @override
  String get traveller => 'Matkailija';

  @override
  String get nightTraveller => 'Yömatkailija';

  @override
  String get original => 'Alkuperäinen';

  @override
  String get explorer => 'Tutkija';

  @override
  String get weekender => 'Viikonloppureissaaja';

  @override
  String get naturalist => 'Luonnontieteilijä';

  @override
  String get historian => 'Historioitsija';

  @override
  String get thrillSeeker => 'Hurjapää';

  @override
  String get culturalBuff => 'Kulttuuriharrastaja';

  @override
  String get myColors => 'Värini';

  @override
  String get experiences => 'Kokemukset';

  @override
  String get done => 'Valmis';

  @override
  String get experiencesInstructions => 'Paina painiketta + aloittaaksesi!';

  @override
  String get continueText => 'Jatka';

  @override
  String get experiencesDescription => 'Mitä tykkäät tehdä matkustaessasi?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Minun matkakarttani';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Olen nähnyt $percentage% maailmasta';
  }

  @override
  String get requiresOnline =>
      'Olemme pahoillamme, Visited vaatii aktiivisen verkkoyhteyden. Avaa asetukset ja varmista, että joko WiFi tai matkapuhelimen data on käytössä eikä lentokonetila ole päällä.';

  @override
  String get list => 'Lista';

  @override
  String get more => 'Lisää';

  @override
  String get myCountrySelections => 'Valitsemani maat';

  @override
  String get cities => 'Kaupungit';

  @override
  String get citiesInstructions =>
      'Paina mitä vain maata aloittaaksesi kaupunkien valinnan.';

  @override
  String get missingCitiesEmailTitle => 'Puuttuvia kaupunkeja!';

  @override
  String get lists => 'Luettelot';

  @override
  String get disputedTerritories => 'Kiistanalaiset alueet';

  @override
  String get sponsored => 'Sponsoroitu';

  @override
  String get places => 'Paikat';

  @override
  String get noListsError =>
      'Hupsis, tällä hetkellä listoja ei ole saatavilla. Kokeile hetken päästä uudestaan.';

  @override
  String get noInspirationsError =>
      'Hupsis, tällä hetkellä kuvia ei ole saatavilla. Kokeile hetken päästä uudestaan.';

  @override
  String get mostFrequentlyVisitedCountries => 'Useimmin vierailemasi maat:';

  @override
  String get update => 'Päivittää';

  @override
  String get signup => 'Kirjaudu';

  @override
  String get loginWallSubtitle =>
      'Luo ilmainen tili Visited:n täyden version kokemiseksi';

  @override
  String get loseAllSelectionsWarning =>
      'Menetät kaikki valintasi sovelluksen sulkemisen jälkeen.';

  @override
  String get createAccount => 'Luo tili';

  @override
  String get continueWithoutAccount => 'Jatka ilman tiliä';

  @override
  String get inspirationPromotion => 'Inspiroidu kauniista matkavalokuvista';

  @override
  String get saveStatsPromotion => 'Tallenna matkatilastosi!';

  @override
  String get selectRegionsPromotion => 'Valitse osavaltiot ja maakunnat';

  @override
  String get experiencesPromotion => 'Seuraa kokemuksia ympäri maailmaa';

  @override
  String get missingListItem =>
      'Kaipaisimmeko jotain? Napauta tätä lähettääksesi meille sähköpostia saadaksesi suosikkipaikkasi.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Puuttuu kohde $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Olen käynyt $amount $listName';
  }

  @override
  String get orderPoster => 'Juliste';

  @override
  String get shareMap => 'Jakaa kartta';

  @override
  String get posterLandingPageTitle => 'Hanki julisteesi';

  @override
  String get posterNotAvailableError =>
      'Julisteiden ostaminen ei ole saatavilla juuri nyt. Yritä uudelleen myöhemmin.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping toimitus';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Mukautetuista tulostuskartoista\nTulosta henkilökohtainen maailmankartta. Mukauta sitä omilla väreillä ja pyydä se toimittamaan suoraan kotiisi.\n \n### Tekniset tiedot:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Maisemasuuntaus.\n- Mikromuste, pisaroita tarkkoihin tulosteisiin, 8 -bittinen väri, melkein valokuvan tulostus laatu,\n- 0,22 mm paksu satiinipaperi\n\n### Rahti tiedot:\nToimitus Torontosta, Kanadasta mihin tahansa maailmaan Kanada Post -sovelluksella. Anna 2–4 ​​viikkoa toimittaa useimpiin kohteisiin. Kaikki tilaukset lähetetään painetta jätettyyn toimitusosoitteeseen. Kaikki maksu hoitaa Apple Pay, tai Stripe.\n\n\n### Peruutus/palautus:\nTilaukset käsitellään heti sen jälkeen, kun ne on toimitettu nopeimmasta mahdollisesta käännöksestä. Siksi palautusta/peruutusta ei ole saatavana.';

  @override
  String get posterDescriptionMarkdown =>
      '## Mukautetuista tulostuskartoista\nTulosta henkilökohtainen maailmankartta. Mukauta sitä omilla väreillä ja pyydä se toimittamaan suoraan kotiisi.\n \n### Tekniset tiedot:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Maisemasuuntaus.\n- Mikromuste, pisaroita tarkkoihin tulosteisiin, 8 -bittinen väri, melkein valokuvan tulostus laatu,\n- 0,22 mm paksu satiinipaperi\n\n### Rahti tiedot:\nToimitus Torontosta, Kanadasta mihin tahansa maailmaan Kanada Post -sovelluksella. Anna 2–4 ​​viikkoa toimittaa useimpiin kohteisiin. Kaikki tilaukset lähetetään painetta jätettyyn toimitusosoitteeseen. Kaikki maksu hoitaa Google Pay tai Stripe.\n\n\n### Peruutus/palautus:\nTilaukset käsitellään heti sen jälkeen, kun ne on toimitettu nopeimmasta mahdollisesta käännöksestä. Siksi palautusta/peruutusta ei ole saatavana.';

  @override
  String get posterCustomizeTitle => 'Räätälöidä juliste';

  @override
  String get enterShippingAddress => 'Syötä lähetysosoite';

  @override
  String get price => 'Hinta';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + vero';
  }

  @override
  String get showSelections => 'Näytä valinta';

  @override
  String get posterNoRefunds =>
      'Palautuksia ei ole saatavana, kun juliste on tulostettu.';

  @override
  String get posterReviewOrder => 'Tarkista tilauksesi';

  @override
  String get email => 'Sähköposti';

  @override
  String get emailEmptyError => 'Ole hyvä ja syötä sähköpostiosoitteesi';

  @override
  String get fullName => 'Koko nimi';

  @override
  String get fullNameEmptyError => 'Anna koko nimesi, ole hyvä';

  @override
  String get streetAddressEmptyError => 'Anna katuosoitteesi';

  @override
  String get cityEmptyError => 'Anna kaupunkisi';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Anna $fieldName';
  }

  @override
  String get country => 'Maa';

  @override
  String get countryEmptyError => 'Anna maasi';

  @override
  String get posterReviewOrderTitle => 'Tarkista tilauksesi';

  @override
  String get buyNow => 'Osta nyt';

  @override
  String get secureCheckoutDisclaimer => 'Suojattu kassa';

  @override
  String get total => 'Kaikki yhteensä';

  @override
  String get tax => 'Verottaa';

  @override
  String get subtotal => 'Välisumma';

  @override
  String get posterProductName => 'Mukautetut karttajuliste';

  @override
  String get shipping => 'laivaus';

  @override
  String get posterOrderReceivedTitle => 'tilaus vastaanotettu';

  @override
  String get posterOrderReceivedSubtitle => 'Saimme tilauksesi!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Tarkista sähköpostiosoitteesi lisää päivityksiä. \nSalli jopa 4 viikko julisteesi saapumiseen. \nJos sinulla on kysyttävää, lähetä meille sähköpostia osoitteeseen [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Painettu julisteiden tilaustila';

  @override
  String get moreInfo => 'Lisää tietoa';

  @override
  String get logoutConfirm => 'Haluatko kirjautua ulos sovelluksesta?';

  @override
  String get emailNotAvailable => 'Tuo sähköposti on otettu.';

  @override
  String get alphabetical => 'Aakkosellinen';

  @override
  String get firstTimeLiveTutorial =>
      'Kotimaasi ja -kaupunkisi ilmoittaminen tekee sovelluskokemuksestasi henkilökohtaisemman.';

  @override
  String get firstTimeBeenTutorial =>
      'Valitsemalla, missä olet käynyt, voit tarkastella karttaa kaikista maista, joissa olet käynyt, ja nähdä henkilökohtaiset tilastot.';

  @override
  String get progressTooltipGoal =>
      'Matkatavoitteesi perustuvat siihen, kuinka monta maata \"haluat\" matkustaa verrattuna maihin, joissa olet \"ollut\".';

  @override
  String get progressTooltipRank =>
      'Tämä luku näyttää, miten sijoitut verrattuna maailman matkailijoihin.  Voit nostaa sijoitustasi matkustamalla useampaan maahan.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Tämä kaavio perustuu niiden maiden lukumäärään, joissa olet käynyt, verrattuna maailman kaikkien maiden lukumäärään.';

  @override
  String get sortBy => 'Lajittelu';

  @override
  String get updateWishlist => 'Päivitä toivomuslista';

  @override
  String get mapInfo =>
      'Valitse ollut, haluamasi tai asuva maa klikkaamalla maata. Voit myös napsauttaa vasemmassa yläkulmassa olevaa kuvaketta luettelonäkymään.';

  @override
  String get oneTimePurchase => 'Kaikki on kertaostos!';

  @override
  String get contact => 'Ota yhteyttä';

  @override
  String get contactUs => 'Ota yhteyttä';

  @override
  String get noCitiesSelected => 'Et ole vielä valinnut yhtään kaupunkia...';

  @override
  String get updateTravelGoal => 'Opdater rejsemål';

  @override
  String get travelGoalComplete =>
      'Tillykke! \n\nYou har afsluttet dit rejsemål! \n\ntap + -knappen for at tilføje flere lande.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Sähköposti $email ei liitetä tiliä. Haluatko luoda sen nyt?';
  }

  @override
  String get tryAgain => 'Yritä uudelleen';

  @override
  String get itineraries => 'Matkasuunnitelmat';

  @override
  String get itinerary => 'Matkasuunnitelma';

  @override
  String get place => 'Paikka';

  @override
  String get itinerariesDescription =>
      'Nämä ovat paikkoja, joista olet ilmaissut kiinnostuksesi.\nKäytä tätä opasta apuna seuraavan lomasi suunnittelussa.';

  @override
  String get addMore => 'Lisää lisää';

  @override
  String get interests => 'Kiinnostuksen kohteet';

  @override
  String get selection => 'Valinta';

  @override
  String get goal => 'Päämäärä';

  @override
  String get noItineraries => 'Ei Reittejä';

  @override
  String get noItinerariesExplanation =>
      'Lisääthän joitakin paikkoja, inspiraatioita tai kokemuksia nähdäksesi matkasuunnitelmasi generoituvan automaattisesti.';

  @override
  String get clusterPins => 'Ryhmitä nastat';

  @override
  String get toggleRegions => 'Näytä alueet zoomaamalla';

  @override
  String get mapProjection => 'Karttaprojektio';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Samakartta';

  @override
  String get yourTravellerType => 'Matkailijatyyppisi:';

  @override
  String get yourHotelPreferences => 'Hotellimieltymyksesi:';

  @override
  String get budget => 'Edullinen';

  @override
  String get midScale => 'Keskitaso';

  @override
  String get luxury => 'Ylellinen';

  @override
  String get noTravellerType =>
      'Lisää kohteita toivelistallesi selvittääksesi, millainen matkailija olet.';

  @override
  String get unlockLived => 'Avaa Elänyt';

  @override
  String get unlockLivedDescription =>
      'Valitse kartalta, missä olet asunut aiemmin!';

  @override
  String get futureFeaturesDescription => '...ja kaikki tulevat ominaisuudet';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Useimmin vierailemasi maasi:';

  @override
  String get departureDate => 'Lähtöpäivä';

  @override
  String get returnDate => 'Paluupäivä';

  @override
  String get hotels => 'Hotellit';

  @override
  String get food => 'Ruoka';

  @override
  String get travelDates => 'Matkustuspäivät';

  @override
  String get posterForMe => 'Minulle';

  @override
  String get posterSendGift => 'Lähetä lahja';

  @override
  String get addSelections => 'Lisää valinnat';

  @override
  String get posterType => 'Julistetyyppi';

  @override
  String get help => 'Auttaa';

  @override
  String get tutorialMap =>
      'Napauta maata valitaksesi: ollut, halunnut ja asunut.';

  @override
  String get tutorialMapList =>
      'Napauta luettelokuvaketta (vasemmassa yläkulmassa) valitaksesi luettelon mukaan.';

  @override
  String get tutorialCountryDetails =>
      'Napauta maata ja sitten \"Lisää\" valitaksesi alueen mukaan.';

  @override
  String get tutorialItems =>
      'Liu\'uta kytkintä valitaksesi kohteiden valintatavan.';

  @override
  String get tutorialInspirations =>
      'Siirry seuraavaan korttiin pyyhkäisemällä oikealle tai vasemmalle.';

  @override
  String get lifetime => 'Elinikäinen';

  @override
  String get chooseYourPlan => 'Valitse suunnitelmasi';

  @override
  String get requestARefund => 'Pyydä hyvitystä';

  @override
  String get noPurchasesFound => 'Ostoja ei löytynyt.';

  @override
  String get noProductsAvailable => 'Ei saatavilla olevia tuotteita';

  @override
  String get posterLandingAppBar => 'Tuo tarinasi kotiin';

  @override
  String get posterLandingSubHeading => 'Sinun matkasi, sinun tarinasi';

  @override
  String get posterLandingSubDescription =>
      'Matkasi ovat enemmän kuin matkoja, ne ovat tarinoita, muistoja ja virstanpylväitä. Tee niistä unohtumattomista hetkistä yksilöllinen maailmankartta, joka on yhtä ainutlaatuinen kuin seikkailusi.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Kartta saavutuksistasi: Korosta jokaista kohdetta ensimmäisestä suuresta matkastasi rohkeimpaan seikkailuusi.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Juhli jokaista matkaa: koe matkasi uudelleen päivittäin kauniisti muotoillun julisteen avulla, joka on suunniteltu inspiroimaan.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Lahja, jonka heillä on aarre: Yllätä matkatoveri mukautetulla kartalla, joka esittelee heidän matkansa. Se on täydellinen syntymäpäiville, virstanpylväille tai vain siksi.';

  @override
  String get posterLandingHowItWorks => 'Kuinka se toimii!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Mukauta suunnitteluasi: Valitse värit, tyylit ja merkitse matkasi (tai heidän!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Esikatsele karttaasi: Näe sen heräävän eloon ennen tilausta.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Turvallinen maksaminen: Nopea ja turvallinen Apple Payn tai Stripen avulla.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Turvallinen maksaminen: Nopea ja turvallinen Google Payn tai Stripen avulla.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Valmis näyttöön: Toimitamme sen suoraan kotiovellesi (tai heidän kotiovellesi).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Kokemuksia matkakumppaneilta';

  @override
  String get posterLandingCustomerReview1 =>
      'Tämä kartta on loistava tapa seurata kaikkialla, missä olen matkustanut, ja suunnitella tulevia matkojamme. Laatu on vakaa ja näyttää upealta roikkumassa toimistossani. Sain jopa sellaisen veljelleni, eikä hän voinut lopettaa puhumista siitä, kuinka siistiä se on! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Olen matkustanut yli 150 satamaan työskennellessäni risteilyllä. Tämä kartta on loistava lisä olohuoneeseeni muistoksi kaikille merellä vietetyille vuosille. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Upea lahja äitienpäiväksi. Äitini oli erittäin liikuttunut! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Tulostin kartan paikoista, joissa halusin vierailla gf:ni kanssa. Se oli loistava joululahja. Myös korkea laatu. Brad J.';

  @override
  String get posterLandingSpecifications => 'Tekniset tiedot';

  @override
  String get posterLandingSpecification1 =>
      '• Mitat: 16\" x 20\" (40,64cm x 50,8cm)';

  @override
  String get posterLandingSpecification2 => '• Suunta: Vaaka';

  @override
  String get posterLandingSpecification3 =>
      '• Tulostuslaatu: Mikromuste, pisarat tarkkoja tulosteita varten.  8-bittinen väri, lähes valokuvatulostuslaatu.';

  @override
  String get posterLandingSpecification4 =>
      '• Paperi: 0,22 mm paksu satiinipaperi';

  @override
  String get posterLandingShippingHeader => 'Toimitustiedot';

  @override
  String get posterLandingShipping1 =>
      '• Toimitus Torontosta, Kanadasta kaikkialle maailmaan käyttämällä Kanadan postia.';

  @override
  String get posterLandingShipping2 =>
      '• Toimitusaika useimpiin kohteisiin 2–4 viikkoa.';

  @override
  String get posterLandingShipping3 =>
      '• Kaikki tilaukset kääritään pahviputkilaatikkoon antamaasi toimitusosoitteeseen.';

  @override
  String get posterLandingCancellationHeader => 'Peruutus/hyvitys:';

  @override
  String get posterLandingCancellationBody =>
      'Hyvitykset ovat saatavilla ennen kuin juliste on lähetetty tulostimelle, mikä voi kestää jopa 24 tuntia.  Kun tilauksesi on käsitelty, hyvitystä/peruuttamista ei ole saatavilla.  Saat sähköpostin, kun tilauksesi on tulostettu.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';
}
