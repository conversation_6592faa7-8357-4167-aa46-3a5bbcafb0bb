{"@@last_modified": "2021-08-18 22:56:33.341513", "@@locale": "en_CA", "appName": "Visited", "language": "Language", "pickEmailApp": "Pick your email app", "numberOfCountriesShareMessage": "I have visited {amount} countries!  How many have you been to? www.visitedapp.com", "@numberOfCountriesShareMessage": {"description": "Message added when sharing your map", "placeholders": {"amount": {"description": "number of countries", "example": "15", "type": "int"}}}, "numberOfCitiesShareMessage": "I have visited {amount} cities!  How many have you been to? www.visitedapp.com", "@numberOfCitiesShareMessage": {"description": "Message added when sharing your city map", "placeholders": {"amount": {"description": "number of cities", "example": "15", "type": "int"}}}, "numberOfItemsInTodoListShareMessage": "I have visited {amount} {listName}!  How many have you been to? www.visitedapp.com", "@numberOfItemsInTodoListShareMessage": {"description": "Message added when sharing your city map", "placeholders": {"amount": {"description": "number of item", "example": "15", "type": "int"}, "listName": {"description": "name of the Todo List", "example": "Wonders of the World", "type": "String"}}}, "clear": "Clear", "been": "Been", "want": "Want", "live": "Live", "lived": "Lived", "water": "Water", "land": "Land", "borders": "Borders", "labels": "Labels", "legend": "Legend", "inspiration": "Inspiration", "inspirations": "Inspirations", "delete": "Delete", "unlockVisitedUpsellTitle": "Want to see more?", "unlockVisitedUpsellSubtitle": "Unlock all features and enjoy Visited in it’s full strength", "checkTheDetails": "Check the Details", "moreInspirationsComingSoon": "We are working on getting more images.  Check back soon!", "unlockPremiumFeatures": "Unlock premium features", "purchased": "Purchased!", "buy": "Buy", "restorePurchases": "Restore Purchase", "ok": "Ok", "areYouSure": "Are you sure?", "deleteInspirationConfirmMessage": "Deleting this card is permanent.  There is no way to recover this image.", "cancel": "Cancel", "map": "Map", "progress": "Progress", "myTravelGoal": "My Travel Goal", "goalRemaining": "{remaining} more to go!", "@goalRemaining": {"description": "Shows on the dashboard how many countries remaining to visit.", "placeholders": {"remaining": {"description": "number of countries", "example": "15", "type": "int"}}}, "top": "TOP", "ofTheWorld": "of the world!", "countries": "countries", "topPlacesVisitedFromCountry": "Top Countries Visited from {country}:", "@topPlacesVisitedFromCountry": {"placeholders": {"country": {"description": "name of a country", "example": "Canada", "type": "String"}}}, "login": "Log In", "logout": "Log Out", "enterYourEmail": "Enter your email", "privacyPolicy": "Privacy Policy", "privatePolicyUrl": "https://www.arrivinginhighheels.com/privacy-policy", "termsOfUse": "Terms of Use", "termsOfUserUrl": "https://www.arrivinginhighheels.com/terms-of-use", "errorTitle": "Whoops!", "enterValidEmail": "Please enter a valid email", "settings": "Settings", "whereDoYouLive": "Where do you live?", "whereHaveYouBeen": "Where have you been?", "whereDoYouFlyFrom": "Where do you fly out of?", "next": "Next", "missingAirports": "Don't see what you are looking for? Send us an <NAME_EMAIL>", "missingAirportsEmailTitle": "Missing Airports!", "supportEmailAddress": "<EMAIL>", "welcomeTitle": "Welcome to Visited", "welcomeSubtitle": "The adventure of a lifetime awaits", "getStarted": "Get Started", "privacyAgreement": "Privacy Agreement", "privacyAgreementSubtitle": "Please agree to the following items before continuing to use Visited.", "privacyAgreementTermsMarkdown": "By checking this box, you acknowledge that you have read and agree to be bound by Arriving in High Heels’  [Privacy Policy](https://www.arrivinginhighheels.com/privacy-policy) and [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use).", "privacyAgreementOptIn": "I agree to receive electronic messages from Arriving in High Heels containing information and offers with respect to products, applications and services that may be of interest to me, including notification of sales, promotions, offers and newsletters. I may withdraw this consent at any time as described in the Privacy Policy or by clicking on the “unsubscribe” link in the electronic messages.", "submit": "Submit", "companyName": "Arriving In High Heels Corporation", "companyAddress": "31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6", "privacyAgreementRequired": "You must agree to both our terms and opt in order to continue using Visited.", "deleteAccount": "Delete Account", "removeAdsUpsell": "Do you wish to opt out of ads and unsubscribe from email marketing instead?", "deleteAccountWarning": "Deleting your account will remove all of your information from our servers.\nThis process cannot be undone.", "about": "About", "popularity": "Popularity", "regions": "Regions", "population": "Population", "size": "Size", "coverage": "Coverage", "percentOfCountryVisited": "% of country visited", "visited": "visited", "notes": "Notes", "kmSquared": "km²", "customize": "Customize", "onlyCountSovereign": "Only Count Sovereign Countries", "countUkSeparately": "Count U.K. Countries separately", "showLegend": "Show Legend", "showLivedPin": "Show Lived Pin", "useMyColours": "Use My Colors", "mapColors": "Map Colors", "traveller": "The Traveller", "nightTraveller": "The Night Traveller", "original": "The Original", "explorer": "The Explorer", "weekender": "The Weekender", "naturalist": "The Naturalist", "historian": "The Historian", "thrillSeeker": "The Thrill Seeker", "culturalBuff": "The Cultural Buff", "myColors": "My Colors", "experiences": "Experiences", "done": "Done", "experiencesInstructions": "Tap the + button to get started!", "continueText": "Continue", "experiencesDescription": "What do you like to do when you travel?", "visitedWebsiteShortLink": "https://www.visitedapp.com", "sharingHashtag": "#Visited", "myTravelMap": "My Travel Map", "percentOfWorldSeen": "I've seen {percentage}% of the world", "@percentOfWorldSeen": {"description": "Message sent when sharing your map", "placeholders": {"percentage": {"description": "a percentage point", "example": "25.5", "type": "int"}}}, "requiresOnline": "Sorry, Visited requires an active network connection.  Please open your settings app and ensure that either \\r Wi-Fi or Cellular data is enabled and Airplane Mode is disabled", "list": "List", "more": "More", "myCountrySelections": "My Country Selections", "cities": "Cities", "citiesInstructions": "Tap on any country to start selecting cities.", "missingCitiesEmailTitle": "Missing Cities!", "Places": "Places", "disputedTerritories": "Disputed Territories", "places": "Places", "noListsError": "Op<PERSON>, no lists available at this time, please try a bit later", "noInspirationsError": "<PERSON><PERSON>, no photos are available right now, please try a bit later", "sponsored": "Sponsored", "lists": "Lists", "update": "Update", "mostFrequentlyVisitedCountries": "Your most frequently visited countries:", "loginWallSubtitle": "Create a free account to experience the full version of Visited", "loseAllSelectionsWarning": "You will lose all your selections after closing the app.", "createAccount": "Create Account", "continueWithoutAccount": "Continue without an Account", "inspirationPromotion": "Get inspired with beautiful travel photography", "saveStatsPromotion": "Save Your Travel Stats!", "selectRegionsPromotion": "Select States and Provinces", "experiencesPromotion": "Track Experiences all around the World", "missingListItem": "Did we miss something?  Tap here to send us an email to get your favorite place added.", "missingListItemEmailTitle": "Missing Item from {list}", "@missingListItemEmailTitle": {"description": "Subject of email when requesting a missing item", "placeholders": {"list": {"description": "name of a Todo List", "example": "Wonders of the world", "type": "String"}}}, "signup": "Sign Up", "listShareMessage": "I have visited {amount} {listName}", "orderPoster": "Poster", "shareMap": "Share Map", "posterLandingPageTitle": "Get Your Poster", "posterNotAvailableError": "Poster purchasing is not available right now.  Please try again later.", "posterPricePlusShipping": "{price} + {shipping} shipping", "posterDescriptionMarkdownApple": "## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Apple Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.", "posterDescriptionMarkdown": "## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Google Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.", "posterCustomizeTitle": "Customize Poster", "enterShippingAddress": "Enter Shipping Address", "price": "Price", "formattedPlusTax": "{formattedPrice} + tax", "showSelections": "Show Selection", "posterNoRefunds": "No refunds are available after your poster has been printed.", "posterReviewOrder": "Review Your Order", "email": "Email", "emailEmptyError": "Please enter your email", "fullName": "Full Name", "fullNameEmptyError": "Please enter your full name", "streetAddressEmptyError": "Please enter your street address", "cityEmptyError": "Please enter your city", "fieldEmptyError": "Please enter your {fieldName}", "country": "Country", "countryEmptyError": "Please enter your country", "posterReviewOrderTitle": "Review Your Order", "buyNow": "Buy Now", "secureCheckoutDisclaimer": "Secure checkout provided by Stripe", "total": "Total", "tax": "Tax", "subtotal": "Subtotal", "posterProductName": "Custom Visited Map Poster", "shipping": "Shipping", "posterOrderReceivedTitle": "Order Received", "posterOrderReceivedSubtitle": "We received your order!", "posterOrderReceivedInstructionsMarkdown": "Check your email for more updates.\nPlease allow up to 4 week for your poster to arrive.\nIf you have any questions, please email us at [<EMAIL>](<EMAIL>)", "posterOrderReceivedEmailSubject": "Printed Poster Order Status", "moreInfo": "More Info", "logoutConfirm": "Would you like to log out of Visited?", "emailNotAvailable": "That email has been taken.", "alphabetical": "Alphabetical", "firstTimeLiveTutorial": "Providing your home country and city will personalize your app experience.", "firstTimeBeenTutorial": "Selecting where you have been allows you to view your map of all the countries you have been to and see personal stats.", "progressTooltipGoal": "Your travel goals are based on the number of countries you \"Want\" to travel compared to countries where you have \"Been\".", "progressTooltipRank": "This number shows how you rank compared to travellers around the world.  You can increase your rank by travelling to more countries.", "progressTooltipPercentageOfWorld": "This graph is based on number of countries you have been to compared to total countries of the world.", "sortBy": "Sort By", "updateWishlist": "Update Wish List", "mapInfo": "Click on the country to select been, want or live. You can also click on the icon found in the top left corner for the list view.", "oneTimePurchase": "Everything is a one time purchase!", "contact": "Contact", "contactUs": "Contact Us", "noCitiesSelected": "You have not selected any cities, yet...", "updateTravelGoal": "Update Travel Goal", "travelGoalComplete": "Congratulations!\n\nYou have completed your travel goal! \n\nTap the + button to add more countries.", "loginEmailNotFoundError": "There is no account associated the email {email}.  Would you like to create it now?", "@loginEmailNotFoundError": {"description": "Presents an error if the user tries to log in with an email that does not exist", "placeholders": {"email": {"description": "The email address the user requested", "example": "<EMAIL>", "type": "String"}}}, "tryAgain": "Try Again", "itineraries": "Itineraries", "itinerary": "Itinerary", "place": "Place", "itinerariesDescription": "These are places you've expressed interest in.\nUse this guide to help plan your next vacation.", "addMore": "Add More", "interests": "Interests", "selection": "Selection", "goal": "Goal", "noItineraries": "No Itineraries", "noItinerariesExplanation": "Please add some places, inspirations or experiences to see your itineraries automatically generate.", "clusterPins": "Cluster Pins", "toggleRegions": "Show Regions", "mapProjection": "Map Projection", "mercator": "Mercator", "equirectangular": "Equirectangular", "yourTravellerType": "Your Traveller Type:", "yourHotelPreferences": "Your Hotel Preferences:", "budget": "Budget", "midScale": "Mid Scale", "luxury": "Luxury", "noTravellerType": "Add items to your bucket list to discover what type of traveller you are.", "unlockLived": "Unlock Lived", "unlockLivedDescription": "Select where you have previously lived on the map!", "futureFeaturesDescription": "...and all future features", "departureDate": "Departure Date", "returnDate": "Return Date", "hotels": "Hotels", "food": "Food", "travelDates": "Travel Dates", "posterForMe": "For Me", "posterSendGift": "Send a Gift", "addSelections": "Add Selections", "posterType": "Poster Type", "help": "Help", "tutorialMap": "Tap on a country to select: been, want and lived.", "tutorialMapList": "Tap on list icon (top left corner) to select by list.", "tutorialCountryDetails": "Tap on the country and then “more” to select by region.", "tutorialItems": "Slide the toggle to choose how you want to select items.", "tutorialInspirations": "Swipe right or left to move to next card.", "yourMostFrequentlyVisitedCountry": "Your Most Frequently Visited Country:", "lifetime": "Lifetime", "chooseYourPlan": "Choose Your Plan", "requestARefund": "Request a Refund", "noPurchasesFound": "No purchases found.", "noProductsAvailable": "No Products Available", "posterLandingAppBar": "Bring Your Stories Home", "posterLandingSubHeading": "Your Travel, Your Story", "posterLandingSubDescription": "Your travels are more than trips, they're a stories, memories, and milestones. Turn those unforgettable moments into a personalized world map that's as unique as your adventures.", "posterLandingPromoBullet1": "• A Map of Your Achievements: Highlight every destination, from your first big trip to your most daring adventure.", "posterLandingPromoBullet2": "• Celebrate Every Journey: Relive your travels daily with a beautifully crafted postere designed to inspire.", "posterLandingPromoBullet3": "• A Gift They'll Treasure:  Surpise a fellow traveler with a custom map showcasing their journey, perfect for birthdays, milestones or just because.", "posterLandingHowItWorks": "How it Works!", "posterLandingHowItWorksStep1": "1. Customize Your Design:  Choose colours, styles and mark your travels (or theirs!)", "posterLandingHowItWorksStep2": "2. Preview Your Map:  See it come to life before your order.", "posterLandingHowItWorksStep3_iOS": "3. Safe Payment: Fast And secure with Apple Pay or Stripe.", "posterLandingHowItWorksStep3_android": "3. Safe Payment: Fast And secure with Google Pay or Stripe.", "posterLandingHowItWorksStep4": "4. Ready for Display: We'll ship it straight to your door (or theirs).", "posterLandingCustomerReviewsHeader": "Experiences from Fellow Travelers", "posterLandingCustomerReview1": "\"This map is a great way to keep track of everywhere I've traveled and plan our future trips.  The quality is solid and it looks awesome hanging in my office.  I even got one for my brother and he couldn't, stop talking about how cool it is!\" - <PERSON>.", "posterLandingCustomerReview2": "\"I have travelled to over 150 ports while working on cruise. This map is a great addition to my living room as a memory to all the years at sea.\" - <PERSON>. ", "posterLandingCustomerReview3": "\"Great gift for mother's day. My mom was super touched!\" <PERSON>.", "posterLandingCustomerReview4": "\"Printed a map of places I wanted to visit with my gf. It was a great Christmas gift. High quality too.\" <PERSON>.", "posterLandingSpecifications": "Specifications", "posterLandingSpecification1": "• Dimensions: 16\" x 20\" (40.64cm x 50.8cm)", "posterLandingSpecification2": "• Orientation: Landscape", "posterLandingSpecification3": "• Print quality: Micro-ink, droplets for precise prints. 8-bit colour, almost photographic print quality.", "posterLandingSpecification4": "• Paper: 0.22mm thick satin paper", "posterLandingShippingHeader": "Shipping Details", "posterLandingShipping1": "• Shipping from Toronto, Canada to anywhere in the world using Canada Post.", "posterLandingShipping2": "• Allow 2-4 weeks for delivery to most destinations.", "posterLandingShipping3": "• All orders are rolled up in a cardboard tube box to the shipping address you submit.", "posterLandingCancellationHeader": "Cancellation/Refund:", "posterLandingCancellationBody": "Refunds are available before your poster has been sent to the printer, which can take up to 24 hours.  After your order has been processed, no refund/cancellation is available.  You will receive an email when your order has been printed.", "unsubscribe": "Unsubscribe", "unsubscribeConfirmMessage": "Are you sure you want to unsubscribe? You'll miss out on exclusive deals and updates!"}