// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $AreasTable extends Areas with TableInfo<$AreasTable, AreaDto> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AreasTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _isoCodeMeta =
      const VerificationMeta('isoCode');
  @override
  late final GeneratedColumn<String> isoCode = GeneratedColumn<String>(
      'iso_code', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 12),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _parentIdMeta =
      const VerificationMeta('parentId');
  @override
  late final GeneratedColumn<int> parentId = GeneratedColumn<int>(
      'parent_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _continentIdMeta =
      const VerificationMeta('continentId');
  @override
  late final GeneratedColumn<int> continentId = GeneratedColumn<int>(
      'continent_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _jsonMeta = const VerificationMeta('json');
  @override
  late final GeneratedColumn<String> json = GeneratedColumn<String>(
      'json', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns =>
      [id, isoCode, parentId, continentId, json];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'areas';
  @override
  VerificationContext validateIntegrity(Insertable<AreaDto> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('iso_code')) {
      context.handle(_isoCodeMeta,
          isoCode.isAcceptableOrUnknown(data['iso_code']!, _isoCodeMeta));
    } else if (isInserting) {
      context.missing(_isoCodeMeta);
    }
    if (data.containsKey('parent_id')) {
      context.handle(_parentIdMeta,
          parentId.isAcceptableOrUnknown(data['parent_id']!, _parentIdMeta));
    }
    if (data.containsKey('continent_id')) {
      context.handle(
          _continentIdMeta,
          continentId.isAcceptableOrUnknown(
              data['continent_id']!, _continentIdMeta));
    }
    if (data.containsKey('json')) {
      context.handle(
          _jsonMeta, json.isAcceptableOrUnknown(data['json']!, _jsonMeta));
    } else if (isInserting) {
      context.missing(_jsonMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id, isoCode};
  @override
  AreaDto map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AreaDto(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      isoCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}iso_code'])!,
      parentId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}parent_id']),
      continentId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}continent_id']),
      json: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}json'])!,
    );
  }

  @override
  $AreasTable createAlias(String alias) {
    return $AreasTable(attachedDatabase, alias);
  }
}

class AreaDto extends DataClass implements Insertable<AreaDto> {
  final int id;
  final String isoCode;
  final int? parentId;
  final int? continentId;
  final String json;
  const AreaDto(
      {required this.id,
      required this.isoCode,
      this.parentId,
      this.continentId,
      required this.json});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['iso_code'] = Variable<String>(isoCode);
    if (!nullToAbsent || parentId != null) {
      map['parent_id'] = Variable<int>(parentId);
    }
    if (!nullToAbsent || continentId != null) {
      map['continent_id'] = Variable<int>(continentId);
    }
    map['json'] = Variable<String>(json);
    return map;
  }

  AreasCompanion toCompanion(bool nullToAbsent) {
    return AreasCompanion(
      id: Value(id),
      isoCode: Value(isoCode),
      parentId: parentId == null && nullToAbsent
          ? const Value.absent()
          : Value(parentId),
      continentId: continentId == null && nullToAbsent
          ? const Value.absent()
          : Value(continentId),
      json: Value(json),
    );
  }

  factory AreaDto.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AreaDto(
      id: serializer.fromJson<int>(json['id']),
      isoCode: serializer.fromJson<String>(json['isoCode']),
      parentId: serializer.fromJson<int?>(json['parentId']),
      continentId: serializer.fromJson<int?>(json['continentId']),
      json: serializer.fromJson<String>(json['json']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'isoCode': serializer.toJson<String>(isoCode),
      'parentId': serializer.toJson<int?>(parentId),
      'continentId': serializer.toJson<int?>(continentId),
      'json': serializer.toJson<String>(json),
    };
  }

  AreaDto copyWith(
          {int? id,
          String? isoCode,
          Value<int?> parentId = const Value.absent(),
          Value<int?> continentId = const Value.absent(),
          String? json}) =>
      AreaDto(
        id: id ?? this.id,
        isoCode: isoCode ?? this.isoCode,
        parentId: parentId.present ? parentId.value : this.parentId,
        continentId: continentId.present ? continentId.value : this.continentId,
        json: json ?? this.json,
      );
  AreaDto copyWithCompanion(AreasCompanion data) {
    return AreaDto(
      id: data.id.present ? data.id.value : this.id,
      isoCode: data.isoCode.present ? data.isoCode.value : this.isoCode,
      parentId: data.parentId.present ? data.parentId.value : this.parentId,
      continentId:
          data.continentId.present ? data.continentId.value : this.continentId,
      json: data.json.present ? data.json.value : this.json,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AreaDto(')
          ..write('id: $id, ')
          ..write('isoCode: $isoCode, ')
          ..write('parentId: $parentId, ')
          ..write('continentId: $continentId, ')
          ..write('json: $json')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, isoCode, parentId, continentId, json);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AreaDto &&
          other.id == this.id &&
          other.isoCode == this.isoCode &&
          other.parentId == this.parentId &&
          other.continentId == this.continentId &&
          other.json == this.json);
}

class AreasCompanion extends UpdateCompanion<AreaDto> {
  final Value<int> id;
  final Value<String> isoCode;
  final Value<int?> parentId;
  final Value<int?> continentId;
  final Value<String> json;
  final Value<int> rowid;
  const AreasCompanion({
    this.id = const Value.absent(),
    this.isoCode = const Value.absent(),
    this.parentId = const Value.absent(),
    this.continentId = const Value.absent(),
    this.json = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AreasCompanion.insert({
    required int id,
    required String isoCode,
    this.parentId = const Value.absent(),
    this.continentId = const Value.absent(),
    required String json,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        isoCode = Value(isoCode),
        json = Value(json);
  static Insertable<AreaDto> custom({
    Expression<int>? id,
    Expression<String>? isoCode,
    Expression<int>? parentId,
    Expression<int>? continentId,
    Expression<String>? json,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (isoCode != null) 'iso_code': isoCode,
      if (parentId != null) 'parent_id': parentId,
      if (continentId != null) 'continent_id': continentId,
      if (json != null) 'json': json,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AreasCompanion copyWith(
      {Value<int>? id,
      Value<String>? isoCode,
      Value<int?>? parentId,
      Value<int?>? continentId,
      Value<String>? json,
      Value<int>? rowid}) {
    return AreasCompanion(
      id: id ?? this.id,
      isoCode: isoCode ?? this.isoCode,
      parentId: parentId ?? this.parentId,
      continentId: continentId ?? this.continentId,
      json: json ?? this.json,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (isoCode.present) {
      map['iso_code'] = Variable<String>(isoCode.value);
    }
    if (parentId.present) {
      map['parent_id'] = Variable<int>(parentId.value);
    }
    if (continentId.present) {
      map['continent_id'] = Variable<int>(continentId.value);
    }
    if (json.present) {
      map['json'] = Variable<String>(json.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AreasCompanion(')
          ..write('id: $id, ')
          ..write('isoCode: $isoCode, ')
          ..write('parentId: $parentId, ')
          ..write('continentId: $continentId, ')
          ..write('json: $json, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ContinentsTable extends Continents
    with TableInfo<$ContinentsTable, ContinentDto> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ContinentsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _translationsMeta =
      const VerificationMeta('translations');
  @override
  late final GeneratedColumn<String> translations = GeneratedColumn<String>(
      'translations', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [id, name, translations];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'continents';
  @override
  VerificationContext validateIntegrity(Insertable<ContinentDto> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('translations')) {
      context.handle(
          _translationsMeta,
          translations.isAcceptableOrUnknown(
              data['translations']!, _translationsMeta));
    } else if (isInserting) {
      context.missing(_translationsMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ContinentDto map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ContinentDto(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      translations: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}translations'])!,
    );
  }

  @override
  $ContinentsTable createAlias(String alias) {
    return $ContinentsTable(attachedDatabase, alias);
  }
}

class ContinentDto extends DataClass implements Insertable<ContinentDto> {
  final int id;
  final String name;
  final String translations;
  const ContinentDto(
      {required this.id, required this.name, required this.translations});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    map['translations'] = Variable<String>(translations);
    return map;
  }

  ContinentsCompanion toCompanion(bool nullToAbsent) {
    return ContinentsCompanion(
      id: Value(id),
      name: Value(name),
      translations: Value(translations),
    );
  }

  factory ContinentDto.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ContinentDto(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      translations: serializer.fromJson<String>(json['translations']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
      'translations': serializer.toJson<String>(translations),
    };
  }

  ContinentDto copyWith({int? id, String? name, String? translations}) =>
      ContinentDto(
        id: id ?? this.id,
        name: name ?? this.name,
        translations: translations ?? this.translations,
      );
  ContinentDto copyWithCompanion(ContinentsCompanion data) {
    return ContinentDto(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      translations: data.translations.present
          ? data.translations.value
          : this.translations,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ContinentDto(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('translations: $translations')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, name, translations);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ContinentDto &&
          other.id == this.id &&
          other.name == this.name &&
          other.translations == this.translations);
}

class ContinentsCompanion extends UpdateCompanion<ContinentDto> {
  final Value<int> id;
  final Value<String> name;
  final Value<String> translations;
  const ContinentsCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.translations = const Value.absent(),
  });
  ContinentsCompanion.insert({
    this.id = const Value.absent(),
    required String name,
    required String translations,
  })  : name = Value(name),
        translations = Value(translations);
  static Insertable<ContinentDto> custom({
    Expression<int>? id,
    Expression<String>? name,
    Expression<String>? translations,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (translations != null) 'translations': translations,
    });
  }

  ContinentsCompanion copyWith(
      {Value<int>? id, Value<String>? name, Value<String>? translations}) {
    return ContinentsCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      translations: translations ?? this.translations,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (translations.present) {
      map['translations'] = Variable<String>(translations.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ContinentsCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('translations: $translations')
          ..write(')'))
        .toString();
  }
}

class $LastModifiedTable extends LastModified
    with TableInfo<$LastModifiedTable, LastModifiedDto> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LastModifiedTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 16),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _lastModifiedMeta =
      const VerificationMeta('lastModified');
  @override
  late final GeneratedColumn<DateTime> lastModified = GeneratedColumn<DateTime>(
      'last_modified', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [id, type, lastModified];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'last_modified';
  @override
  VerificationContext validateIntegrity(Insertable<LastModifiedDto> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('last_modified')) {
      context.handle(
          _lastModifiedMeta,
          lastModified.isAcceptableOrUnknown(
              data['last_modified']!, _lastModifiedMeta));
    } else if (isInserting) {
      context.missing(_lastModifiedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  LastModifiedDto map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LastModifiedDto(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      lastModified: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}last_modified'])!,
    );
  }

  @override
  $LastModifiedTable createAlias(String alias) {
    return $LastModifiedTable(attachedDatabase, alias);
  }
}

class LastModifiedDto extends DataClass implements Insertable<LastModifiedDto> {
  final int id;
  final String type;
  final DateTime lastModified;
  const LastModifiedDto(
      {required this.id, required this.type, required this.lastModified});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['type'] = Variable<String>(type);
    map['last_modified'] = Variable<DateTime>(lastModified);
    return map;
  }

  LastModifiedCompanion toCompanion(bool nullToAbsent) {
    return LastModifiedCompanion(
      id: Value(id),
      type: Value(type),
      lastModified: Value(lastModified),
    );
  }

  factory LastModifiedDto.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LastModifiedDto(
      id: serializer.fromJson<int>(json['id']),
      type: serializer.fromJson<String>(json['type']),
      lastModified: serializer.fromJson<DateTime>(json['lastModified']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'type': serializer.toJson<String>(type),
      'lastModified': serializer.toJson<DateTime>(lastModified),
    };
  }

  LastModifiedDto copyWith({int? id, String? type, DateTime? lastModified}) =>
      LastModifiedDto(
        id: id ?? this.id,
        type: type ?? this.type,
        lastModified: lastModified ?? this.lastModified,
      );
  LastModifiedDto copyWithCompanion(LastModifiedCompanion data) {
    return LastModifiedDto(
      id: data.id.present ? data.id.value : this.id,
      type: data.type.present ? data.type.value : this.type,
      lastModified: data.lastModified.present
          ? data.lastModified.value
          : this.lastModified,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LastModifiedDto(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('lastModified: $lastModified')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, type, lastModified);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LastModifiedDto &&
          other.id == this.id &&
          other.type == this.type &&
          other.lastModified == this.lastModified);
}

class LastModifiedCompanion extends UpdateCompanion<LastModifiedDto> {
  final Value<int> id;
  final Value<String> type;
  final Value<DateTime> lastModified;
  const LastModifiedCompanion({
    this.id = const Value.absent(),
    this.type = const Value.absent(),
    this.lastModified = const Value.absent(),
  });
  LastModifiedCompanion.insert({
    this.id = const Value.absent(),
    required String type,
    required DateTime lastModified,
  })  : type = Value(type),
        lastModified = Value(lastModified);
  static Insertable<LastModifiedDto> custom({
    Expression<int>? id,
    Expression<String>? type,
    Expression<DateTime>? lastModified,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (type != null) 'type': type,
      if (lastModified != null) 'last_modified': lastModified,
    });
  }

  LastModifiedCompanion copyWith(
      {Value<int>? id, Value<String>? type, Value<DateTime>? lastModified}) {
    return LastModifiedCompanion(
      id: id ?? this.id,
      type: type ?? this.type,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (lastModified.present) {
      map['last_modified'] = Variable<DateTime>(lastModified.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LastModifiedCompanion(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('lastModified: $lastModified')
          ..write(')'))
        .toString();
  }
}

class $AreaSelectionsTable extends AreaSelections
    with TableInfo<$AreaSelectionsTable, AreaSelectionDto> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AreaSelectionsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _areaIdMeta = const VerificationMeta('areaId');
  @override
  late final GeneratedColumn<int> areaId = GeneratedColumn<int>(
      'area_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _isoCodeMeta =
      const VerificationMeta('isoCode');
  @override
  late final GeneratedColumn<String> isoCode = GeneratedColumn<String>(
      'iso_code', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 16),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 16),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _syncedMeta = const VerificationMeta('synced');
  @override
  late final GeneratedColumn<bool> synced = GeneratedColumn<bool>(
      'synced', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("synced" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [areaId, isoCode, type, synced];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'area_selections';
  @override
  VerificationContext validateIntegrity(Insertable<AreaSelectionDto> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('area_id')) {
      context.handle(_areaIdMeta,
          areaId.isAcceptableOrUnknown(data['area_id']!, _areaIdMeta));
    } else if (isInserting) {
      context.missing(_areaIdMeta);
    }
    if (data.containsKey('iso_code')) {
      context.handle(_isoCodeMeta,
          isoCode.isAcceptableOrUnknown(data['iso_code']!, _isoCodeMeta));
    } else if (isInserting) {
      context.missing(_isoCodeMeta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('synced')) {
      context.handle(_syncedMeta,
          synced.isAcceptableOrUnknown(data['synced']!, _syncedMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {areaId, isoCode};
  @override
  AreaSelectionDto map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AreaSelectionDto(
      areaId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}area_id'])!,
      isoCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}iso_code'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      synced: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}synced'])!,
    );
  }

  @override
  $AreaSelectionsTable createAlias(String alias) {
    return $AreaSelectionsTable(attachedDatabase, alias);
  }
}

class AreaSelectionDto extends DataClass
    implements Insertable<AreaSelectionDto> {
  final int areaId;
  final String isoCode;
  final String type;
  final bool synced;
  const AreaSelectionDto(
      {required this.areaId,
      required this.isoCode,
      required this.type,
      required this.synced});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['area_id'] = Variable<int>(areaId);
    map['iso_code'] = Variable<String>(isoCode);
    map['type'] = Variable<String>(type);
    map['synced'] = Variable<bool>(synced);
    return map;
  }

  AreaSelectionsCompanion toCompanion(bool nullToAbsent) {
    return AreaSelectionsCompanion(
      areaId: Value(areaId),
      isoCode: Value(isoCode),
      type: Value(type),
      synced: Value(synced),
    );
  }

  factory AreaSelectionDto.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AreaSelectionDto(
      areaId: serializer.fromJson<int>(json['areaId']),
      isoCode: serializer.fromJson<String>(json['isoCode']),
      type: serializer.fromJson<String>(json['type']),
      synced: serializer.fromJson<bool>(json['synced']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'areaId': serializer.toJson<int>(areaId),
      'isoCode': serializer.toJson<String>(isoCode),
      'type': serializer.toJson<String>(type),
      'synced': serializer.toJson<bool>(synced),
    };
  }

  AreaSelectionDto copyWith(
          {int? areaId, String? isoCode, String? type, bool? synced}) =>
      AreaSelectionDto(
        areaId: areaId ?? this.areaId,
        isoCode: isoCode ?? this.isoCode,
        type: type ?? this.type,
        synced: synced ?? this.synced,
      );
  AreaSelectionDto copyWithCompanion(AreaSelectionsCompanion data) {
    return AreaSelectionDto(
      areaId: data.areaId.present ? data.areaId.value : this.areaId,
      isoCode: data.isoCode.present ? data.isoCode.value : this.isoCode,
      type: data.type.present ? data.type.value : this.type,
      synced: data.synced.present ? data.synced.value : this.synced,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AreaSelectionDto(')
          ..write('areaId: $areaId, ')
          ..write('isoCode: $isoCode, ')
          ..write('type: $type, ')
          ..write('synced: $synced')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(areaId, isoCode, type, synced);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AreaSelectionDto &&
          other.areaId == this.areaId &&
          other.isoCode == this.isoCode &&
          other.type == this.type &&
          other.synced == this.synced);
}

class AreaSelectionsCompanion extends UpdateCompanion<AreaSelectionDto> {
  final Value<int> areaId;
  final Value<String> isoCode;
  final Value<String> type;
  final Value<bool> synced;
  final Value<int> rowid;
  const AreaSelectionsCompanion({
    this.areaId = const Value.absent(),
    this.isoCode = const Value.absent(),
    this.type = const Value.absent(),
    this.synced = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AreaSelectionsCompanion.insert({
    required int areaId,
    required String isoCode,
    required String type,
    this.synced = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : areaId = Value(areaId),
        isoCode = Value(isoCode),
        type = Value(type);
  static Insertable<AreaSelectionDto> custom({
    Expression<int>? areaId,
    Expression<String>? isoCode,
    Expression<String>? type,
    Expression<bool>? synced,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (areaId != null) 'area_id': areaId,
      if (isoCode != null) 'iso_code': isoCode,
      if (type != null) 'type': type,
      if (synced != null) 'synced': synced,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AreaSelectionsCompanion copyWith(
      {Value<int>? areaId,
      Value<String>? isoCode,
      Value<String>? type,
      Value<bool>? synced,
      Value<int>? rowid}) {
    return AreaSelectionsCompanion(
      areaId: areaId ?? this.areaId,
      isoCode: isoCode ?? this.isoCode,
      type: type ?? this.type,
      synced: synced ?? this.synced,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (areaId.present) {
      map['area_id'] = Variable<int>(areaId.value);
    }
    if (isoCode.present) {
      map['iso_code'] = Variable<String>(isoCode.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (synced.present) {
      map['synced'] = Variable<bool>(synced.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AreaSelectionsCompanion(')
          ..write('areaId: $areaId, ')
          ..write('isoCode: $isoCode, ')
          ..write('type: $type, ')
          ..write('synced: $synced, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $InspirationsTable extends Inspirations
    with TableInfo<$InspirationsTable, InspirationDto> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $InspirationsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 128),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _urlMeta = const VerificationMeta('url');
  @override
  late final GeneratedColumn<String> url = GeneratedColumn<String>(
      'url', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 200),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _areaIdMeta = const VerificationMeta('areaId');
  @override
  late final GeneratedColumn<int> areaId = GeneratedColumn<int>(
      'area_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _selectionMeta =
      const VerificationMeta('selection');
  @override
  late final GeneratedColumn<String> selection = GeneratedColumn<String>(
      'selection', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 10),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _syncedMeta = const VerificationMeta('synced');
  @override
  late final GeneratedColumn<bool> synced = GeneratedColumn<bool>(
      'synced', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("synced" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns =>
      [id, name, url, areaId, selection, synced];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'inspirations';
  @override
  VerificationContext validateIntegrity(Insertable<InspirationDto> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('url')) {
      context.handle(
          _urlMeta, url.isAcceptableOrUnknown(data['url']!, _urlMeta));
    } else if (isInserting) {
      context.missing(_urlMeta);
    }
    if (data.containsKey('area_id')) {
      context.handle(_areaIdMeta,
          areaId.isAcceptableOrUnknown(data['area_id']!, _areaIdMeta));
    } else if (isInserting) {
      context.missing(_areaIdMeta);
    }
    if (data.containsKey('selection')) {
      context.handle(_selectionMeta,
          selection.isAcceptableOrUnknown(data['selection']!, _selectionMeta));
    } else if (isInserting) {
      context.missing(_selectionMeta);
    }
    if (data.containsKey('synced')) {
      context.handle(_syncedMeta,
          synced.isAcceptableOrUnknown(data['synced']!, _syncedMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  InspirationDto map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return InspirationDto(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      url: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}url'])!,
      areaId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}area_id'])!,
      selection: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}selection'])!,
      synced: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}synced'])!,
    );
  }

  @override
  $InspirationsTable createAlias(String alias) {
    return $InspirationsTable(attachedDatabase, alias);
  }
}

class InspirationDto extends DataClass implements Insertable<InspirationDto> {
  final int id;
  final String name;
  final String url;
  final int areaId;
  final String selection;
  final bool synced;
  const InspirationDto(
      {required this.id,
      required this.name,
      required this.url,
      required this.areaId,
      required this.selection,
      required this.synced});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    map['url'] = Variable<String>(url);
    map['area_id'] = Variable<int>(areaId);
    map['selection'] = Variable<String>(selection);
    map['synced'] = Variable<bool>(synced);
    return map;
  }

  InspirationsCompanion toCompanion(bool nullToAbsent) {
    return InspirationsCompanion(
      id: Value(id),
      name: Value(name),
      url: Value(url),
      areaId: Value(areaId),
      selection: Value(selection),
      synced: Value(synced),
    );
  }

  factory InspirationDto.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return InspirationDto(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      url: serializer.fromJson<String>(json['url']),
      areaId: serializer.fromJson<int>(json['areaId']),
      selection: serializer.fromJson<String>(json['selection']),
      synced: serializer.fromJson<bool>(json['synced']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
      'url': serializer.toJson<String>(url),
      'areaId': serializer.toJson<int>(areaId),
      'selection': serializer.toJson<String>(selection),
      'synced': serializer.toJson<bool>(synced),
    };
  }

  InspirationDto copyWith(
          {int? id,
          String? name,
          String? url,
          int? areaId,
          String? selection,
          bool? synced}) =>
      InspirationDto(
        id: id ?? this.id,
        name: name ?? this.name,
        url: url ?? this.url,
        areaId: areaId ?? this.areaId,
        selection: selection ?? this.selection,
        synced: synced ?? this.synced,
      );
  InspirationDto copyWithCompanion(InspirationsCompanion data) {
    return InspirationDto(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      url: data.url.present ? data.url.value : this.url,
      areaId: data.areaId.present ? data.areaId.value : this.areaId,
      selection: data.selection.present ? data.selection.value : this.selection,
      synced: data.synced.present ? data.synced.value : this.synced,
    );
  }

  @override
  String toString() {
    return (StringBuffer('InspirationDto(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('url: $url, ')
          ..write('areaId: $areaId, ')
          ..write('selection: $selection, ')
          ..write('synced: $synced')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, name, url, areaId, selection, synced);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is InspirationDto &&
          other.id == this.id &&
          other.name == this.name &&
          other.url == this.url &&
          other.areaId == this.areaId &&
          other.selection == this.selection &&
          other.synced == this.synced);
}

class InspirationsCompanion extends UpdateCompanion<InspirationDto> {
  final Value<int> id;
  final Value<String> name;
  final Value<String> url;
  final Value<int> areaId;
  final Value<String> selection;
  final Value<bool> synced;
  const InspirationsCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.url = const Value.absent(),
    this.areaId = const Value.absent(),
    this.selection = const Value.absent(),
    this.synced = const Value.absent(),
  });
  InspirationsCompanion.insert({
    this.id = const Value.absent(),
    required String name,
    required String url,
    required int areaId,
    required String selection,
    this.synced = const Value.absent(),
  })  : name = Value(name),
        url = Value(url),
        areaId = Value(areaId),
        selection = Value(selection);
  static Insertable<InspirationDto> custom({
    Expression<int>? id,
    Expression<String>? name,
    Expression<String>? url,
    Expression<int>? areaId,
    Expression<String>? selection,
    Expression<bool>? synced,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (url != null) 'url': url,
      if (areaId != null) 'area_id': areaId,
      if (selection != null) 'selection': selection,
      if (synced != null) 'synced': synced,
    });
  }

  InspirationsCompanion copyWith(
      {Value<int>? id,
      Value<String>? name,
      Value<String>? url,
      Value<int>? areaId,
      Value<String>? selection,
      Value<bool>? synced}) {
    return InspirationsCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      areaId: areaId ?? this.areaId,
      selection: selection ?? this.selection,
      synced: synced ?? this.synced,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (url.present) {
      map['url'] = Variable<String>(url.value);
    }
    if (areaId.present) {
      map['area_id'] = Variable<int>(areaId.value);
    }
    if (selection.present) {
      map['selection'] = Variable<String>(selection.value);
    }
    if (synced.present) {
      map['synced'] = Variable<bool>(synced.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('InspirationsCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('url: $url, ')
          ..write('areaId: $areaId, ')
          ..write('selection: $selection, ')
          ..write('synced: $synced')
          ..write(')'))
        .toString();
  }
}

class $CitiesTable extends Cities with TableInfo<$CitiesTable, CityDto> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CitiesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 128),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _latMeta = const VerificationMeta('lat');
  @override
  late final GeneratedColumn<double> lat = GeneratedColumn<double>(
      'lat', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _longMeta = const VerificationMeta('long');
  @override
  late final GeneratedColumn<double> long = GeneratedColumn<double>(
      'long', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _areaIdMeta = const VerificationMeta('areaId');
  @override
  late final GeneratedColumn<int> areaId = GeneratedColumn<int>(
      'area_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _secondLevelAreaIdMeta =
      const VerificationMeta('secondLevelAreaId');
  @override
  late final GeneratedColumn<int> secondLevelAreaId = GeneratedColumn<int>(
      'second_level_area_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns =>
      [id, name, lat, long, areaId, secondLevelAreaId];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'cities';
  @override
  VerificationContext validateIntegrity(Insertable<CityDto> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('lat')) {
      context.handle(
          _latMeta, lat.isAcceptableOrUnknown(data['lat']!, _latMeta));
    } else if (isInserting) {
      context.missing(_latMeta);
    }
    if (data.containsKey('long')) {
      context.handle(
          _longMeta, long.isAcceptableOrUnknown(data['long']!, _longMeta));
    } else if (isInserting) {
      context.missing(_longMeta);
    }
    if (data.containsKey('area_id')) {
      context.handle(_areaIdMeta,
          areaId.isAcceptableOrUnknown(data['area_id']!, _areaIdMeta));
    } else if (isInserting) {
      context.missing(_areaIdMeta);
    }
    if (data.containsKey('second_level_area_id')) {
      context.handle(
          _secondLevelAreaIdMeta,
          secondLevelAreaId.isAcceptableOrUnknown(
              data['second_level_area_id']!, _secondLevelAreaIdMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CityDto map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CityDto(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      lat: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}lat'])!,
      long: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}long'])!,
      areaId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}area_id'])!,
      secondLevelAreaId: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}second_level_area_id']),
    );
  }

  @override
  $CitiesTable createAlias(String alias) {
    return $CitiesTable(attachedDatabase, alias);
  }
}

class CityDto extends DataClass implements Insertable<CityDto> {
  final int id;
  final String name;
  final double lat;
  final double long;
  final int areaId;
  final int? secondLevelAreaId;
  const CityDto(
      {required this.id,
      required this.name,
      required this.lat,
      required this.long,
      required this.areaId,
      this.secondLevelAreaId});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    map['lat'] = Variable<double>(lat);
    map['long'] = Variable<double>(long);
    map['area_id'] = Variable<int>(areaId);
    if (!nullToAbsent || secondLevelAreaId != null) {
      map['second_level_area_id'] = Variable<int>(secondLevelAreaId);
    }
    return map;
  }

  CitiesCompanion toCompanion(bool nullToAbsent) {
    return CitiesCompanion(
      id: Value(id),
      name: Value(name),
      lat: Value(lat),
      long: Value(long),
      areaId: Value(areaId),
      secondLevelAreaId: secondLevelAreaId == null && nullToAbsent
          ? const Value.absent()
          : Value(secondLevelAreaId),
    );
  }

  factory CityDto.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CityDto(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      lat: serializer.fromJson<double>(json['lat']),
      long: serializer.fromJson<double>(json['long']),
      areaId: serializer.fromJson<int>(json['areaId']),
      secondLevelAreaId: serializer.fromJson<int?>(json['secondLevelAreaId']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
      'lat': serializer.toJson<double>(lat),
      'long': serializer.toJson<double>(long),
      'areaId': serializer.toJson<int>(areaId),
      'secondLevelAreaId': serializer.toJson<int?>(secondLevelAreaId),
    };
  }

  CityDto copyWith(
          {int? id,
          String? name,
          double? lat,
          double? long,
          int? areaId,
          Value<int?> secondLevelAreaId = const Value.absent()}) =>
      CityDto(
        id: id ?? this.id,
        name: name ?? this.name,
        lat: lat ?? this.lat,
        long: long ?? this.long,
        areaId: areaId ?? this.areaId,
        secondLevelAreaId: secondLevelAreaId.present
            ? secondLevelAreaId.value
            : this.secondLevelAreaId,
      );
  CityDto copyWithCompanion(CitiesCompanion data) {
    return CityDto(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      lat: data.lat.present ? data.lat.value : this.lat,
      long: data.long.present ? data.long.value : this.long,
      areaId: data.areaId.present ? data.areaId.value : this.areaId,
      secondLevelAreaId: data.secondLevelAreaId.present
          ? data.secondLevelAreaId.value
          : this.secondLevelAreaId,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CityDto(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('lat: $lat, ')
          ..write('long: $long, ')
          ..write('areaId: $areaId, ')
          ..write('secondLevelAreaId: $secondLevelAreaId')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, name, lat, long, areaId, secondLevelAreaId);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CityDto &&
          other.id == this.id &&
          other.name == this.name &&
          other.lat == this.lat &&
          other.long == this.long &&
          other.areaId == this.areaId &&
          other.secondLevelAreaId == this.secondLevelAreaId);
}

class CitiesCompanion extends UpdateCompanion<CityDto> {
  final Value<int> id;
  final Value<String> name;
  final Value<double> lat;
  final Value<double> long;
  final Value<int> areaId;
  final Value<int?> secondLevelAreaId;
  const CitiesCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.lat = const Value.absent(),
    this.long = const Value.absent(),
    this.areaId = const Value.absent(),
    this.secondLevelAreaId = const Value.absent(),
  });
  CitiesCompanion.insert({
    this.id = const Value.absent(),
    required String name,
    required double lat,
    required double long,
    required int areaId,
    this.secondLevelAreaId = const Value.absent(),
  })  : name = Value(name),
        lat = Value(lat),
        long = Value(long),
        areaId = Value(areaId);
  static Insertable<CityDto> custom({
    Expression<int>? id,
    Expression<String>? name,
    Expression<double>? lat,
    Expression<double>? long,
    Expression<int>? areaId,
    Expression<int>? secondLevelAreaId,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (lat != null) 'lat': lat,
      if (long != null) 'long': long,
      if (areaId != null) 'area_id': areaId,
      if (secondLevelAreaId != null) 'second_level_area_id': secondLevelAreaId,
    });
  }

  CitiesCompanion copyWith(
      {Value<int>? id,
      Value<String>? name,
      Value<double>? lat,
      Value<double>? long,
      Value<int>? areaId,
      Value<int?>? secondLevelAreaId}) {
    return CitiesCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      lat: lat ?? this.lat,
      long: long ?? this.long,
      areaId: areaId ?? this.areaId,
      secondLevelAreaId: secondLevelAreaId ?? this.secondLevelAreaId,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (lat.present) {
      map['lat'] = Variable<double>(lat.value);
    }
    if (long.present) {
      map['long'] = Variable<double>(long.value);
    }
    if (areaId.present) {
      map['area_id'] = Variable<int>(areaId.value);
    }
    if (secondLevelAreaId.present) {
      map['second_level_area_id'] = Variable<int>(secondLevelAreaId.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CitiesCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('lat: $lat, ')
          ..write('long: $long, ')
          ..write('areaId: $areaId, ')
          ..write('secondLevelAreaId: $secondLevelAreaId')
          ..write(')'))
        .toString();
  }
}

class $CitySelectionsTable extends CitySelections
    with TableInfo<$CitySelectionsTable, CitySelection> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CitySelectionsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _selectionMeta =
      const VerificationMeta('selection');
  @override
  late final GeneratedColumn<String> selection = GeneratedColumn<String>(
      'selection', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(maxTextLength: 10),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _syncedMeta = const VerificationMeta('synced');
  @override
  late final GeneratedColumn<bool> synced = GeneratedColumn<bool>(
      'synced', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("synced" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [id, selection, synced];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'city_selections';
  @override
  VerificationContext validateIntegrity(Insertable<CitySelection> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('selection')) {
      context.handle(_selectionMeta,
          selection.isAcceptableOrUnknown(data['selection']!, _selectionMeta));
    } else if (isInserting) {
      context.missing(_selectionMeta);
    }
    if (data.containsKey('synced')) {
      context.handle(_syncedMeta,
          synced.isAcceptableOrUnknown(data['synced']!, _syncedMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CitySelection map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CitySelection(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      selection: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}selection'])!,
      synced: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}synced'])!,
    );
  }

  @override
  $CitySelectionsTable createAlias(String alias) {
    return $CitySelectionsTable(attachedDatabase, alias);
  }
}

class CitySelection extends DataClass implements Insertable<CitySelection> {
  final int id;
  final String selection;
  final bool synced;
  const CitySelection(
      {required this.id, required this.selection, required this.synced});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['selection'] = Variable<String>(selection);
    map['synced'] = Variable<bool>(synced);
    return map;
  }

  CitySelectionsCompanion toCompanion(bool nullToAbsent) {
    return CitySelectionsCompanion(
      id: Value(id),
      selection: Value(selection),
      synced: Value(synced),
    );
  }

  factory CitySelection.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CitySelection(
      id: serializer.fromJson<int>(json['id']),
      selection: serializer.fromJson<String>(json['selection']),
      synced: serializer.fromJson<bool>(json['synced']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'selection': serializer.toJson<String>(selection),
      'synced': serializer.toJson<bool>(synced),
    };
  }

  CitySelection copyWith({int? id, String? selection, bool? synced}) =>
      CitySelection(
        id: id ?? this.id,
        selection: selection ?? this.selection,
        synced: synced ?? this.synced,
      );
  CitySelection copyWithCompanion(CitySelectionsCompanion data) {
    return CitySelection(
      id: data.id.present ? data.id.value : this.id,
      selection: data.selection.present ? data.selection.value : this.selection,
      synced: data.synced.present ? data.synced.value : this.synced,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CitySelection(')
          ..write('id: $id, ')
          ..write('selection: $selection, ')
          ..write('synced: $synced')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, selection, synced);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CitySelection &&
          other.id == this.id &&
          other.selection == this.selection &&
          other.synced == this.synced);
}

class CitySelectionsCompanion extends UpdateCompanion<CitySelection> {
  final Value<int> id;
  final Value<String> selection;
  final Value<bool> synced;
  const CitySelectionsCompanion({
    this.id = const Value.absent(),
    this.selection = const Value.absent(),
    this.synced = const Value.absent(),
  });
  CitySelectionsCompanion.insert({
    this.id = const Value.absent(),
    required String selection,
    this.synced = const Value.absent(),
  }) : selection = Value(selection);
  static Insertable<CitySelection> custom({
    Expression<int>? id,
    Expression<String>? selection,
    Expression<bool>? synced,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (selection != null) 'selection': selection,
      if (synced != null) 'synced': synced,
    });
  }

  CitySelectionsCompanion copyWith(
      {Value<int>? id, Value<String>? selection, Value<bool>? synced}) {
    return CitySelectionsCompanion(
      id: id ?? this.id,
      selection: selection ?? this.selection,
      synced: synced ?? this.synced,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (selection.present) {
      map['selection'] = Variable<String>(selection.value);
    }
    if (synced.present) {
      map['synced'] = Variable<bool>(synced.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CitySelectionsCompanion(')
          ..write('id: $id, ')
          ..write('selection: $selection, ')
          ..write('synced: $synced')
          ..write(')'))
        .toString();
  }
}

abstract class _$VisitedDatabase extends GeneratedDatabase {
  _$VisitedDatabase(QueryExecutor e) : super(e);
  $VisitedDatabaseManager get managers => $VisitedDatabaseManager(this);
  late final $AreasTable areas = $AreasTable(this);
  late final $ContinentsTable continents = $ContinentsTable(this);
  late final $LastModifiedTable lastModified = $LastModifiedTable(this);
  late final $AreaSelectionsTable areaSelections = $AreaSelectionsTable(this);
  late final $InspirationsTable inspirations = $InspirationsTable(this);
  late final $CitiesTable cities = $CitiesTable(this);
  late final $CitySelectionsTable citySelections = $CitySelectionsTable(this);
  late final CityDatabase cityDatabase = CityDatabase(this as VisitedDatabase);
  Selectable<int> countInspirations() {
    return customSelect('SELECT COUNT(id) AS _c0 FROM inspirations',
        variables: [],
        readsFrom: {
          inspirations,
        }).map((QueryRow row) => row.read<int>('_c0'));
  }

  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        areas,
        continents,
        lastModified,
        areaSelections,
        inspirations,
        cities,
        citySelections
      ];
}

typedef $$AreasTableCreateCompanionBuilder = AreasCompanion Function({
  required int id,
  required String isoCode,
  Value<int?> parentId,
  Value<int?> continentId,
  required String json,
  Value<int> rowid,
});
typedef $$AreasTableUpdateCompanionBuilder = AreasCompanion Function({
  Value<int> id,
  Value<String> isoCode,
  Value<int?> parentId,
  Value<int?> continentId,
  Value<String> json,
  Value<int> rowid,
});

class $$AreasTableFilterComposer
    extends Composer<_$VisitedDatabase, $AreasTable> {
  $$AreasTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get isoCode => $composableBuilder(
      column: $table.isoCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get parentId => $composableBuilder(
      column: $table.parentId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get continentId => $composableBuilder(
      column: $table.continentId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get json => $composableBuilder(
      column: $table.json, builder: (column) => ColumnFilters(column));
}

class $$AreasTableOrderingComposer
    extends Composer<_$VisitedDatabase, $AreasTable> {
  $$AreasTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get isoCode => $composableBuilder(
      column: $table.isoCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get parentId => $composableBuilder(
      column: $table.parentId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get continentId => $composableBuilder(
      column: $table.continentId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get json => $composableBuilder(
      column: $table.json, builder: (column) => ColumnOrderings(column));
}

class $$AreasTableAnnotationComposer
    extends Composer<_$VisitedDatabase, $AreasTable> {
  $$AreasTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get isoCode =>
      $composableBuilder(column: $table.isoCode, builder: (column) => column);

  GeneratedColumn<int> get parentId =>
      $composableBuilder(column: $table.parentId, builder: (column) => column);

  GeneratedColumn<int> get continentId => $composableBuilder(
      column: $table.continentId, builder: (column) => column);

  GeneratedColumn<String> get json =>
      $composableBuilder(column: $table.json, builder: (column) => column);
}

class $$AreasTableTableManager extends RootTableManager<
    _$VisitedDatabase,
    $AreasTable,
    AreaDto,
    $$AreasTableFilterComposer,
    $$AreasTableOrderingComposer,
    $$AreasTableAnnotationComposer,
    $$AreasTableCreateCompanionBuilder,
    $$AreasTableUpdateCompanionBuilder,
    (AreaDto, BaseReferences<_$VisitedDatabase, $AreasTable, AreaDto>),
    AreaDto,
    PrefetchHooks Function()> {
  $$AreasTableTableManager(_$VisitedDatabase db, $AreasTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AreasTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AreasTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AreasTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> isoCode = const Value.absent(),
            Value<int?> parentId = const Value.absent(),
            Value<int?> continentId = const Value.absent(),
            Value<String> json = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AreasCompanion(
            id: id,
            isoCode: isoCode,
            parentId: parentId,
            continentId: continentId,
            json: json,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required int id,
            required String isoCode,
            Value<int?> parentId = const Value.absent(),
            Value<int?> continentId = const Value.absent(),
            required String json,
            Value<int> rowid = const Value.absent(),
          }) =>
              AreasCompanion.insert(
            id: id,
            isoCode: isoCode,
            parentId: parentId,
            continentId: continentId,
            json: json,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AreasTableProcessedTableManager = ProcessedTableManager<
    _$VisitedDatabase,
    $AreasTable,
    AreaDto,
    $$AreasTableFilterComposer,
    $$AreasTableOrderingComposer,
    $$AreasTableAnnotationComposer,
    $$AreasTableCreateCompanionBuilder,
    $$AreasTableUpdateCompanionBuilder,
    (AreaDto, BaseReferences<_$VisitedDatabase, $AreasTable, AreaDto>),
    AreaDto,
    PrefetchHooks Function()>;
typedef $$ContinentsTableCreateCompanionBuilder = ContinentsCompanion Function({
  Value<int> id,
  required String name,
  required String translations,
});
typedef $$ContinentsTableUpdateCompanionBuilder = ContinentsCompanion Function({
  Value<int> id,
  Value<String> name,
  Value<String> translations,
});

class $$ContinentsTableFilterComposer
    extends Composer<_$VisitedDatabase, $ContinentsTable> {
  $$ContinentsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get translations => $composableBuilder(
      column: $table.translations, builder: (column) => ColumnFilters(column));
}

class $$ContinentsTableOrderingComposer
    extends Composer<_$VisitedDatabase, $ContinentsTable> {
  $$ContinentsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get translations => $composableBuilder(
      column: $table.translations,
      builder: (column) => ColumnOrderings(column));
}

class $$ContinentsTableAnnotationComposer
    extends Composer<_$VisitedDatabase, $ContinentsTable> {
  $$ContinentsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get translations => $composableBuilder(
      column: $table.translations, builder: (column) => column);
}

class $$ContinentsTableTableManager extends RootTableManager<
    _$VisitedDatabase,
    $ContinentsTable,
    ContinentDto,
    $$ContinentsTableFilterComposer,
    $$ContinentsTableOrderingComposer,
    $$ContinentsTableAnnotationComposer,
    $$ContinentsTableCreateCompanionBuilder,
    $$ContinentsTableUpdateCompanionBuilder,
    (
      ContinentDto,
      BaseReferences<_$VisitedDatabase, $ContinentsTable, ContinentDto>
    ),
    ContinentDto,
    PrefetchHooks Function()> {
  $$ContinentsTableTableManager(_$VisitedDatabase db, $ContinentsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ContinentsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ContinentsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ContinentsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> translations = const Value.absent(),
          }) =>
              ContinentsCompanion(
            id: id,
            name: name,
            translations: translations,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String name,
            required String translations,
          }) =>
              ContinentsCompanion.insert(
            id: id,
            name: name,
            translations: translations,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ContinentsTableProcessedTableManager = ProcessedTableManager<
    _$VisitedDatabase,
    $ContinentsTable,
    ContinentDto,
    $$ContinentsTableFilterComposer,
    $$ContinentsTableOrderingComposer,
    $$ContinentsTableAnnotationComposer,
    $$ContinentsTableCreateCompanionBuilder,
    $$ContinentsTableUpdateCompanionBuilder,
    (
      ContinentDto,
      BaseReferences<_$VisitedDatabase, $ContinentsTable, ContinentDto>
    ),
    ContinentDto,
    PrefetchHooks Function()>;
typedef $$LastModifiedTableCreateCompanionBuilder = LastModifiedCompanion
    Function({
  Value<int> id,
  required String type,
  required DateTime lastModified,
});
typedef $$LastModifiedTableUpdateCompanionBuilder = LastModifiedCompanion
    Function({
  Value<int> id,
  Value<String> type,
  Value<DateTime> lastModified,
});

class $$LastModifiedTableFilterComposer
    extends Composer<_$VisitedDatabase, $LastModifiedTable> {
  $$LastModifiedTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastModified => $composableBuilder(
      column: $table.lastModified, builder: (column) => ColumnFilters(column));
}

class $$LastModifiedTableOrderingComposer
    extends Composer<_$VisitedDatabase, $LastModifiedTable> {
  $$LastModifiedTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastModified => $composableBuilder(
      column: $table.lastModified,
      builder: (column) => ColumnOrderings(column));
}

class $$LastModifiedTableAnnotationComposer
    extends Composer<_$VisitedDatabase, $LastModifiedTable> {
  $$LastModifiedTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<DateTime> get lastModified => $composableBuilder(
      column: $table.lastModified, builder: (column) => column);
}

class $$LastModifiedTableTableManager extends RootTableManager<
    _$VisitedDatabase,
    $LastModifiedTable,
    LastModifiedDto,
    $$LastModifiedTableFilterComposer,
    $$LastModifiedTableOrderingComposer,
    $$LastModifiedTableAnnotationComposer,
    $$LastModifiedTableCreateCompanionBuilder,
    $$LastModifiedTableUpdateCompanionBuilder,
    (
      LastModifiedDto,
      BaseReferences<_$VisitedDatabase, $LastModifiedTable, LastModifiedDto>
    ),
    LastModifiedDto,
    PrefetchHooks Function()> {
  $$LastModifiedTableTableManager(
      _$VisitedDatabase db, $LastModifiedTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$LastModifiedTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$LastModifiedTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$LastModifiedTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<DateTime> lastModified = const Value.absent(),
          }) =>
              LastModifiedCompanion(
            id: id,
            type: type,
            lastModified: lastModified,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String type,
            required DateTime lastModified,
          }) =>
              LastModifiedCompanion.insert(
            id: id,
            type: type,
            lastModified: lastModified,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$LastModifiedTableProcessedTableManager = ProcessedTableManager<
    _$VisitedDatabase,
    $LastModifiedTable,
    LastModifiedDto,
    $$LastModifiedTableFilterComposer,
    $$LastModifiedTableOrderingComposer,
    $$LastModifiedTableAnnotationComposer,
    $$LastModifiedTableCreateCompanionBuilder,
    $$LastModifiedTableUpdateCompanionBuilder,
    (
      LastModifiedDto,
      BaseReferences<_$VisitedDatabase, $LastModifiedTable, LastModifiedDto>
    ),
    LastModifiedDto,
    PrefetchHooks Function()>;
typedef $$AreaSelectionsTableCreateCompanionBuilder = AreaSelectionsCompanion
    Function({
  required int areaId,
  required String isoCode,
  required String type,
  Value<bool> synced,
  Value<int> rowid,
});
typedef $$AreaSelectionsTableUpdateCompanionBuilder = AreaSelectionsCompanion
    Function({
  Value<int> areaId,
  Value<String> isoCode,
  Value<String> type,
  Value<bool> synced,
  Value<int> rowid,
});

class $$AreaSelectionsTableFilterComposer
    extends Composer<_$VisitedDatabase, $AreaSelectionsTable> {
  $$AreaSelectionsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get areaId => $composableBuilder(
      column: $table.areaId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get isoCode => $composableBuilder(
      column: $table.isoCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get synced => $composableBuilder(
      column: $table.synced, builder: (column) => ColumnFilters(column));
}

class $$AreaSelectionsTableOrderingComposer
    extends Composer<_$VisitedDatabase, $AreaSelectionsTable> {
  $$AreaSelectionsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get areaId => $composableBuilder(
      column: $table.areaId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get isoCode => $composableBuilder(
      column: $table.isoCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get synced => $composableBuilder(
      column: $table.synced, builder: (column) => ColumnOrderings(column));
}

class $$AreaSelectionsTableAnnotationComposer
    extends Composer<_$VisitedDatabase, $AreaSelectionsTable> {
  $$AreaSelectionsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get areaId =>
      $composableBuilder(column: $table.areaId, builder: (column) => column);

  GeneratedColumn<String> get isoCode =>
      $composableBuilder(column: $table.isoCode, builder: (column) => column);

  GeneratedColumn<String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<bool> get synced =>
      $composableBuilder(column: $table.synced, builder: (column) => column);
}

class $$AreaSelectionsTableTableManager extends RootTableManager<
    _$VisitedDatabase,
    $AreaSelectionsTable,
    AreaSelectionDto,
    $$AreaSelectionsTableFilterComposer,
    $$AreaSelectionsTableOrderingComposer,
    $$AreaSelectionsTableAnnotationComposer,
    $$AreaSelectionsTableCreateCompanionBuilder,
    $$AreaSelectionsTableUpdateCompanionBuilder,
    (
      AreaSelectionDto,
      BaseReferences<_$VisitedDatabase, $AreaSelectionsTable, AreaSelectionDto>
    ),
    AreaSelectionDto,
    PrefetchHooks Function()> {
  $$AreaSelectionsTableTableManager(
      _$VisitedDatabase db, $AreaSelectionsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AreaSelectionsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AreaSelectionsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AreaSelectionsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> areaId = const Value.absent(),
            Value<String> isoCode = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<bool> synced = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AreaSelectionsCompanion(
            areaId: areaId,
            isoCode: isoCode,
            type: type,
            synced: synced,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required int areaId,
            required String isoCode,
            required String type,
            Value<bool> synced = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AreaSelectionsCompanion.insert(
            areaId: areaId,
            isoCode: isoCode,
            type: type,
            synced: synced,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AreaSelectionsTableProcessedTableManager = ProcessedTableManager<
    _$VisitedDatabase,
    $AreaSelectionsTable,
    AreaSelectionDto,
    $$AreaSelectionsTableFilterComposer,
    $$AreaSelectionsTableOrderingComposer,
    $$AreaSelectionsTableAnnotationComposer,
    $$AreaSelectionsTableCreateCompanionBuilder,
    $$AreaSelectionsTableUpdateCompanionBuilder,
    (
      AreaSelectionDto,
      BaseReferences<_$VisitedDatabase, $AreaSelectionsTable, AreaSelectionDto>
    ),
    AreaSelectionDto,
    PrefetchHooks Function()>;
typedef $$InspirationsTableCreateCompanionBuilder = InspirationsCompanion
    Function({
  Value<int> id,
  required String name,
  required String url,
  required int areaId,
  required String selection,
  Value<bool> synced,
});
typedef $$InspirationsTableUpdateCompanionBuilder = InspirationsCompanion
    Function({
  Value<int> id,
  Value<String> name,
  Value<String> url,
  Value<int> areaId,
  Value<String> selection,
  Value<bool> synced,
});

class $$InspirationsTableFilterComposer
    extends Composer<_$VisitedDatabase, $InspirationsTable> {
  $$InspirationsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get url => $composableBuilder(
      column: $table.url, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get areaId => $composableBuilder(
      column: $table.areaId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get selection => $composableBuilder(
      column: $table.selection, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get synced => $composableBuilder(
      column: $table.synced, builder: (column) => ColumnFilters(column));
}

class $$InspirationsTableOrderingComposer
    extends Composer<_$VisitedDatabase, $InspirationsTable> {
  $$InspirationsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get url => $composableBuilder(
      column: $table.url, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get areaId => $composableBuilder(
      column: $table.areaId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get selection => $composableBuilder(
      column: $table.selection, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get synced => $composableBuilder(
      column: $table.synced, builder: (column) => ColumnOrderings(column));
}

class $$InspirationsTableAnnotationComposer
    extends Composer<_$VisitedDatabase, $InspirationsTable> {
  $$InspirationsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get url =>
      $composableBuilder(column: $table.url, builder: (column) => column);

  GeneratedColumn<int> get areaId =>
      $composableBuilder(column: $table.areaId, builder: (column) => column);

  GeneratedColumn<String> get selection =>
      $composableBuilder(column: $table.selection, builder: (column) => column);

  GeneratedColumn<bool> get synced =>
      $composableBuilder(column: $table.synced, builder: (column) => column);
}

class $$InspirationsTableTableManager extends RootTableManager<
    _$VisitedDatabase,
    $InspirationsTable,
    InspirationDto,
    $$InspirationsTableFilterComposer,
    $$InspirationsTableOrderingComposer,
    $$InspirationsTableAnnotationComposer,
    $$InspirationsTableCreateCompanionBuilder,
    $$InspirationsTableUpdateCompanionBuilder,
    (
      InspirationDto,
      BaseReferences<_$VisitedDatabase, $InspirationsTable, InspirationDto>
    ),
    InspirationDto,
    PrefetchHooks Function()> {
  $$InspirationsTableTableManager(
      _$VisitedDatabase db, $InspirationsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$InspirationsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$InspirationsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$InspirationsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> url = const Value.absent(),
            Value<int> areaId = const Value.absent(),
            Value<String> selection = const Value.absent(),
            Value<bool> synced = const Value.absent(),
          }) =>
              InspirationsCompanion(
            id: id,
            name: name,
            url: url,
            areaId: areaId,
            selection: selection,
            synced: synced,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String name,
            required String url,
            required int areaId,
            required String selection,
            Value<bool> synced = const Value.absent(),
          }) =>
              InspirationsCompanion.insert(
            id: id,
            name: name,
            url: url,
            areaId: areaId,
            selection: selection,
            synced: synced,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$InspirationsTableProcessedTableManager = ProcessedTableManager<
    _$VisitedDatabase,
    $InspirationsTable,
    InspirationDto,
    $$InspirationsTableFilterComposer,
    $$InspirationsTableOrderingComposer,
    $$InspirationsTableAnnotationComposer,
    $$InspirationsTableCreateCompanionBuilder,
    $$InspirationsTableUpdateCompanionBuilder,
    (
      InspirationDto,
      BaseReferences<_$VisitedDatabase, $InspirationsTable, InspirationDto>
    ),
    InspirationDto,
    PrefetchHooks Function()>;
typedef $$CitiesTableCreateCompanionBuilder = CitiesCompanion Function({
  Value<int> id,
  required String name,
  required double lat,
  required double long,
  required int areaId,
  Value<int?> secondLevelAreaId,
});
typedef $$CitiesTableUpdateCompanionBuilder = CitiesCompanion Function({
  Value<int> id,
  Value<String> name,
  Value<double> lat,
  Value<double> long,
  Value<int> areaId,
  Value<int?> secondLevelAreaId,
});

class $$CitiesTableFilterComposer
    extends Composer<_$VisitedDatabase, $CitiesTable> {
  $$CitiesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get lat => $composableBuilder(
      column: $table.lat, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get long => $composableBuilder(
      column: $table.long, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get areaId => $composableBuilder(
      column: $table.areaId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get secondLevelAreaId => $composableBuilder(
      column: $table.secondLevelAreaId,
      builder: (column) => ColumnFilters(column));
}

class $$CitiesTableOrderingComposer
    extends Composer<_$VisitedDatabase, $CitiesTable> {
  $$CitiesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get lat => $composableBuilder(
      column: $table.lat, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get long => $composableBuilder(
      column: $table.long, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get areaId => $composableBuilder(
      column: $table.areaId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get secondLevelAreaId => $composableBuilder(
      column: $table.secondLevelAreaId,
      builder: (column) => ColumnOrderings(column));
}

class $$CitiesTableAnnotationComposer
    extends Composer<_$VisitedDatabase, $CitiesTable> {
  $$CitiesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<double> get lat =>
      $composableBuilder(column: $table.lat, builder: (column) => column);

  GeneratedColumn<double> get long =>
      $composableBuilder(column: $table.long, builder: (column) => column);

  GeneratedColumn<int> get areaId =>
      $composableBuilder(column: $table.areaId, builder: (column) => column);

  GeneratedColumn<int> get secondLevelAreaId => $composableBuilder(
      column: $table.secondLevelAreaId, builder: (column) => column);
}

class $$CitiesTableTableManager extends RootTableManager<
    _$VisitedDatabase,
    $CitiesTable,
    CityDto,
    $$CitiesTableFilterComposer,
    $$CitiesTableOrderingComposer,
    $$CitiesTableAnnotationComposer,
    $$CitiesTableCreateCompanionBuilder,
    $$CitiesTableUpdateCompanionBuilder,
    (CityDto, BaseReferences<_$VisitedDatabase, $CitiesTable, CityDto>),
    CityDto,
    PrefetchHooks Function()> {
  $$CitiesTableTableManager(_$VisitedDatabase db, $CitiesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CitiesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CitiesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CitiesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<double> lat = const Value.absent(),
            Value<double> long = const Value.absent(),
            Value<int> areaId = const Value.absent(),
            Value<int?> secondLevelAreaId = const Value.absent(),
          }) =>
              CitiesCompanion(
            id: id,
            name: name,
            lat: lat,
            long: long,
            areaId: areaId,
            secondLevelAreaId: secondLevelAreaId,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String name,
            required double lat,
            required double long,
            required int areaId,
            Value<int?> secondLevelAreaId = const Value.absent(),
          }) =>
              CitiesCompanion.insert(
            id: id,
            name: name,
            lat: lat,
            long: long,
            areaId: areaId,
            secondLevelAreaId: secondLevelAreaId,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CitiesTableProcessedTableManager = ProcessedTableManager<
    _$VisitedDatabase,
    $CitiesTable,
    CityDto,
    $$CitiesTableFilterComposer,
    $$CitiesTableOrderingComposer,
    $$CitiesTableAnnotationComposer,
    $$CitiesTableCreateCompanionBuilder,
    $$CitiesTableUpdateCompanionBuilder,
    (CityDto, BaseReferences<_$VisitedDatabase, $CitiesTable, CityDto>),
    CityDto,
    PrefetchHooks Function()>;
typedef $$CitySelectionsTableCreateCompanionBuilder = CitySelectionsCompanion
    Function({
  Value<int> id,
  required String selection,
  Value<bool> synced,
});
typedef $$CitySelectionsTableUpdateCompanionBuilder = CitySelectionsCompanion
    Function({
  Value<int> id,
  Value<String> selection,
  Value<bool> synced,
});

class $$CitySelectionsTableFilterComposer
    extends Composer<_$VisitedDatabase, $CitySelectionsTable> {
  $$CitySelectionsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get selection => $composableBuilder(
      column: $table.selection, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get synced => $composableBuilder(
      column: $table.synced, builder: (column) => ColumnFilters(column));
}

class $$CitySelectionsTableOrderingComposer
    extends Composer<_$VisitedDatabase, $CitySelectionsTable> {
  $$CitySelectionsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get selection => $composableBuilder(
      column: $table.selection, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get synced => $composableBuilder(
      column: $table.synced, builder: (column) => ColumnOrderings(column));
}

class $$CitySelectionsTableAnnotationComposer
    extends Composer<_$VisitedDatabase, $CitySelectionsTable> {
  $$CitySelectionsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get selection =>
      $composableBuilder(column: $table.selection, builder: (column) => column);

  GeneratedColumn<bool> get synced =>
      $composableBuilder(column: $table.synced, builder: (column) => column);
}

class $$CitySelectionsTableTableManager extends RootTableManager<
    _$VisitedDatabase,
    $CitySelectionsTable,
    CitySelection,
    $$CitySelectionsTableFilterComposer,
    $$CitySelectionsTableOrderingComposer,
    $$CitySelectionsTableAnnotationComposer,
    $$CitySelectionsTableCreateCompanionBuilder,
    $$CitySelectionsTableUpdateCompanionBuilder,
    (
      CitySelection,
      BaseReferences<_$VisitedDatabase, $CitySelectionsTable, CitySelection>
    ),
    CitySelection,
    PrefetchHooks Function()> {
  $$CitySelectionsTableTableManager(
      _$VisitedDatabase db, $CitySelectionsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CitySelectionsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CitySelectionsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CitySelectionsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> selection = const Value.absent(),
            Value<bool> synced = const Value.absent(),
          }) =>
              CitySelectionsCompanion(
            id: id,
            selection: selection,
            synced: synced,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String selection,
            Value<bool> synced = const Value.absent(),
          }) =>
              CitySelectionsCompanion.insert(
            id: id,
            selection: selection,
            synced: synced,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CitySelectionsTableProcessedTableManager = ProcessedTableManager<
    _$VisitedDatabase,
    $CitySelectionsTable,
    CitySelection,
    $$CitySelectionsTableFilterComposer,
    $$CitySelectionsTableOrderingComposer,
    $$CitySelectionsTableAnnotationComposer,
    $$CitySelectionsTableCreateCompanionBuilder,
    $$CitySelectionsTableUpdateCompanionBuilder,
    (
      CitySelection,
      BaseReferences<_$VisitedDatabase, $CitySelectionsTable, CitySelection>
    ),
    CitySelection,
    PrefetchHooks Function()>;

class $VisitedDatabaseManager {
  final _$VisitedDatabase _db;
  $VisitedDatabaseManager(this._db);
  $$AreasTableTableManager get areas =>
      $$AreasTableTableManager(_db, _db.areas);
  $$ContinentsTableTableManager get continents =>
      $$ContinentsTableTableManager(_db, _db.continents);
  $$LastModifiedTableTableManager get lastModified =>
      $$LastModifiedTableTableManager(_db, _db.lastModified);
  $$AreaSelectionsTableTableManager get areaSelections =>
      $$AreaSelectionsTableTableManager(_db, _db.areaSelections);
  $$InspirationsTableTableManager get inspirations =>
      $$InspirationsTableTableManager(_db, _db.inspirations);
  $$CitiesTableTableManager get cities =>
      $$CitiesTableTableManager(_db, _db.cities);
  $$CitySelectionsTableTableManager get citySelections =>
      $$CitySelectionsTableTableManager(_db, _db.citySelections);
}
