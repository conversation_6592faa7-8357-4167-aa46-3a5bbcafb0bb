class User {
  final String email;
  final bool authenticated;
  final bool unsubscribed;

  const User({
    required this.email,
    required this.authenticated,
    this.unsubscribed = false,
  });

  User.fromJson(Map json)
    : email = json['email'],
      unsubscribed = json['unsubscribed'] ?? false,
      authenticated = true;

  @override
  String toString() {
    return 'User{email: $email}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User && runtimeType == other.runtimeType && email == other.email;

  @override
  int get hashCode => email.hashCode;
}
