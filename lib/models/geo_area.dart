import '../generic_widgets/selectable_item.dart';
import '../logger.dart';
import 'etags.dart';
import 'geo_bounds.dart';
import 'label.dart';

class GeoArea implements SelectableItem, Comparable<GeoArea> {
  const GeoArea({
    required this.id,
    required this.name,
    required this.isoCode,
    this.flagName,
    required this.bounds,
    this.viewBounds,
    this.labels,
    this.sovereign = false,
    this.etags,
    this.parentId,
  });

  @override
  final int id;
  @override
  final String name;
  final String isoCode;
  final String? flagName;
  final GeoBounds bounds;
  final GeoBounds? viewBounds;
  final Set<Label>? labels;
  final bool sovereign;
  final Etags? etags;
  final int? parentId;


  factory GeoArea.fromJson(Map json, {String? languageCode, int? parentId}) {
    try {
      final id = json['id'];
      final iso = json['iso'];

      if (languageCode != null && languageCode.contains('-')){
        final elements = languageCode.split('-');
        languageCode = languageCode.contains('zh') ? _parseChineseLanguageCode(elements) : elements.first;
      }

      final name = json['translations']?[languageCode] ?? json['name'];


      final flagName = json['flagName'];
      final sovereign = json['sovereign'];
      final bounds = GeoBounds.fromJson(json['bounds']);
      final viewBounds = json.containsKey('viewBounds')
          ? GeoBounds.fromJson(json['viewBounds'])
          : null;

      final labels =
          json['labels'].map<Label>((e) => Label.fromJson(e)).toSet();
      final etags = Etags.fromJson(json['etags']);

      return GeoArea(
        id: id,
        name: name,
        isoCode: iso,
        flagName: flagName,
        sovereign: sovereign,
        bounds: bounds,
        viewBounds: viewBounds,
        labels: labels,
        etags: etags,
        parentId: parentId ?? json['parentId'],
      );
    } catch (e, stacktrace) {
      log(e);
      log(stacktrace);
      rethrow;
    }
  }

  static String _parseChineseLanguageCode(List<String> elements) {
    final script = elements[1];
    if (script == 'Hant' || script == 'TW') {
      return 'zh-Hant';
    }

    return 'zh-Hans';
  }

  Map toJson() {
    return {
      'id': id,
      'iso': isoCode,
      'name': name,
      'flagName': flagName,
      'sovereign': sovereign,
      'bounds': bounds.toJson(),
      if (viewBounds != null) 'viewBounds': viewBounds?.toJson(),
      if (labels != null)
        'labels': labels?.map((label) => label.toJson()).toList(),
      if (etags != null) 'etags': etags?.toJson(),
      'parentId': parentId
    };
  }

  bool get isCountry => parentId == null;

  bool get hasSubdivisions => etags?.subdivisions != null;

  bool get hasExperiences => etags?.experiences != null;

  bool get hasDetails => etags?.info != null;

  GeoBounds get renderingBounds => viewBounds ?? bounds;

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GeoArea && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  List<String> get searchKeywords => [name, isoCode, _acronym];

  String get _acronym => name
      .split(' ')
      .where((element) => element.contains(RegExp('[A-Z]')))
      .fold('', (previousValue, element) => '$previousValue${element[0]}');

  @override
  int compareTo(GeoArea other) => name.compareTo(other.name);
}
