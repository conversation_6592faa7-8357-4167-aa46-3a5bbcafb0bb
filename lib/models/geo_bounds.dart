import 'dart:ui';

import '../logger.dart';
import 'polygon.dart';

import 'coordinate.dart';

class GeoBounds {
  static const wholePlanet = GeoBounds(
    northWest: Coordinate(
      latitude: 90,
      longitude: -180,
    ),
    southEast: Coordinate(
      latitude: -90,
      longitude: 180,
    ),
  );

  final Coordinate northWest;
  final Coordinate southEast;

  const GeoBounds({
    required this.northWest,
    required this.southEast,
  });

  static GeoBounds get empty => const GeoBounds(
      northWest: Coordinate(latitude: 0, longitude: 0),
      southEast: Coordinate(latitude: 0, longitude: 0));

  factory GeoBounds.fromJson(List json) {
    if (json.length < 4) {
      log('Invalid number of values to create a GeoBounds.  Expected 4, but received ${json.length}');
      return GeoBounds.empty;
    }

    return GeoBounds(
      northWest: Coordinate.fromList(json.sublist(0, 2)),
      southEast: Coordinate.fromList(json.sublist(2)),
    );
  }

  Coordinate get centre => Coordinate(
      latitude: (northWest.latitude + southEast.latitude / 2),
      longitude: (northWest.longitude + southEast.longitude) / 2);

  Coordinate get southWest =>
      Coordinate(latitude: southEast.latitude, longitude: northWest.longitude);
  Coordinate get northEast =>
      Coordinate(latitude: northWest.latitude, longitude: southEast.longitude);

  double calculateRatio() {
    final size = calculateSize();
    return size.aspectRatio;
  }

  Size calculateSize() {
    final maxLong = Coordinate(
        latitude: northWest.latitude, longitude: southEast.longitude);
    final maxLat = Coordinate(
        latitude: southEast.latitude, longitude: northWest.longitude);

    final maxWidth = maxLong.screenPoint;
    final maxHeight = maxLat.screenPoint;

    final origin = northWest.screenPoint;
    final width = maxWidth.x - origin.x;
    final height = (maxHeight.y - origin.y) / 2;

    return Size(width.toDouble(), height);
  }

  Polygon toPolygon() => Polygon(
        coordinates: [
          northWest,
          northEast,
          southEast,
          southWest,
        ],
        bounds: this,
      );

  List toJson() => [
        northWest.longitude,
        northWest.latitude,
        southEast.longitude,
        southEast.latitude,
      ];
}
