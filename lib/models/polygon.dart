import 'dart:math';

import 'coordinate.dart';
import 'geo_bounds.dart';

class Polygon {
  final GeoBounds bounds;
  final List<Coordinate> coordinates;

  const Polygon({
    required this.bounds,
    required this.coordinates,
  });

  factory Polygon.fromJson(Map json) {
    final bounds = GeoBounds.fromJson(json['b']);
    final List rawCoordinates = json['c'];

    final coordinates = <Coordinate>[];
    for (var i = 0; i < rawCoordinates.length - 1; i += 2) {
      final coordinate = Coordinate(
        longitude: rawCoordinates[i],
        latitude: rawCoordinates[i + 1],
      );
      coordinates.add(coordinate);
    }

    return Polygon(
      bounds: bounds,
      coordinates: List.unmodifiable(coordinates),
    );
  }

  Map toJson() {
    return {
      'b': bounds.toJson(),
      'c': [
        for (final c in coordinates) ...[c.longitude, c.latitude]
      ]
    };
  }

  bool contains(Coordinate test) {
    final testX = test.longitude;
    final testY = test.latitude;

    // Point in Polygon algorithm
    var results = false;

    for (var x = 0; x < coordinates.length - 1; x++) {
      final i = coordinates[x];
      final iX = i.longitude;
      final iY = i.latitude;

      final j = coordinates[x + 1];
      final jX = j.longitude;
      final jY = j.latitude;

      if ((iY > testY) != (jY > testY) &&
          (testX < (jX - iX) * (testY - iY) / (jY - iY) + iX)) {
        results = !results;
      }
    }

    return results;
  }
}

extension PolygonList on Iterable<Polygon> {
  GeoBounds calculateBounds() {
    final points = map((e) => [
          e.bounds.northWest.screenPoint,
          e.bounds.southEast.screenPoint
        ]).expand((e) => e);

    final allY = points.map((e) => e.y);
    final allX = points.map((e) => e.x);

    final minX = allX.reduce(min);
    final minY = allY.reduce(min);

    final maxX = allX.reduce(max);
    final maxY = allY.reduce(max);

    final topLeft = Coordinate.fromRelativePosition(Point(minX, minY));
    final bottomRight = Coordinate.fromRelativePosition(Point(maxX, maxY));

    return GeoBounds(
      northWest: topLeft,
      southEast: bottomRight,
    );
  }
}
