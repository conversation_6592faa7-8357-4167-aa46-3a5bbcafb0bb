{"project_info": {"project_number": "122594309874", "firebase_url": "https://fir-visited.firebaseio.com", "project_id": "firebase-visited", "storage_bucket": "firebase-visited.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:122594309874:android:89fc74a3f76ee240", "android_client_info": {"package_name": "com.arrivinginhighheels.visited"}}, "oauth_client": [{"client_id": "122594309874-h0iv5agkkqfgklni2fbimiq64mtv5a7u.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.arrivinginhighheels.visited", "certificate_hash": "4990f3baee75a3a7f5208c347211ccae38d5c10f"}}, {"client_id": "122594309874-sug1ja8lqiha0n64s7vvse57jpgm772f.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.arrivinginhighheels.visited", "certificate_hash": "c92d0bbde01828dcd4ed5bdb8c2b7b16e3262430"}}, {"client_id": "122594309874-ugqjv76dbqsqd94ovico4i90aco3k9me.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAUjAp_gNQl0sOVilrOUhaJJH6h-otqgVQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "122594309874-a0lr347cok5hrp6ilnn32ok7snlct1p4.apps.googleusercontent.com", "client_type": 3}, {"client_id": "122594309874-61ikvpeseugvl7s0m9er4d686janbtql.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.HighHeels.Travelist-DEV"}}]}}, "admob_app_id": "ca-app-pub-9385283247860729~8599994224"}, {"client_info": {"mobilesdk_app_id": "1:122594309874:android:81157dbe3b2c260a", "android_client_info": {"package_name": "com.arrivinginhighheels.visited.dev"}}, "oauth_client": [{"client_id": "122594309874-ugqjv76dbqsqd94ovico4i90aco3k9me.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAUjAp_gNQl0sOVilrOUhaJJH6h-otqgVQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "122594309874-a0lr347cok5hrp6ilnn32ok7snlct1p4.apps.googleusercontent.com", "client_type": 3}, {"client_id": "122594309874-61ikvpeseugvl7s0m9er4d686janbtql.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.HighHeels.Travelist-DEV"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:122594309874:android:86ff814e32810618", "android_client_info": {"package_name": "com.highheels.visited.dev"}}, "oauth_client": [{"client_id": "122594309874-7v8bn8jsu7vkp8iv2gis326mrk6qh9k8.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.highheels.visited.dev", "certificate_hash": "c92d0bbde01828dcd4ed5bdb8c2b7b16e3262430"}}, {"client_id": "122594309874-ritt159sqa86k1lmftqa4cp4fmt7vnq2.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.highheels.visited.dev", "certificate_hash": "34b1590911563a8d5a1b691a403578fa89573424"}}, {"client_id": "122594309874-ugqjv76dbqsqd94ovico4i90aco3k9me.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAUjAp_gNQl0sOVilrOUhaJJH6h-otqgVQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "122594309874-a0lr347cok5hrp6ilnn32ok7snlct1p4.apps.googleusercontent.com", "client_type": 3}, {"client_id": "122594309874-61ikvpeseugvl7s0m9er4d686janbtql.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.HighHeels.Travelist-DEV"}}]}}}], "configuration_version": "1"}