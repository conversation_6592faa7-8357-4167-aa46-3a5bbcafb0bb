import 'dart:convert';
import 'dart:io';

import 'package:utf/utf.dart';

void main(List<String> arguments) {
  final lookup = _buildLookup();
  final files = allFiles();

  for (final entity in files) {
    transform(lookup, entity.path);
  }
}

void transform(Map<String, String> lookup, String arbFile) {
  final locale = arbFile.split('/').last.split('.').first;
  final file = File(arbFile);
  final Map arb = jsonDecode(file.readAsStringSync());
  final legacy = _buildTranslations('strings/$locale.strings');

  for (final lookupPair in lookup.entries) {
    final updatedKey = lookupPair.key;
    final legacyKey = lookupPair.value;
    if ((arb[updatedKey] as String)?.isNotEmpty ?? false) {
      continue;
    }

    arb[updatedKey] = legacy[legacyKey];
  }

  file.writeAsStringSync(jsonEncode(arb));
}

List<FileSystemEntity> allFiles() {
  final directory = Directory('../../lib/l10n');
  final files = directory.listSync();
  return files;
}

Map<String, String> _buildLookup() {
  final english = _buildTranslations('strings/app_en.strings');
  final arbFile = File(
      '/Users/<USER>/Development/visited/visited-flutter/lib/l10n/en-US.arb');
  final Map arb = jsonDecode(arbFile.readAsStringSync());

  arb.removeWhere((key, value) => key.startsWith('@'));
  print(arb);

  final lookupTable = <String, String>{};
  final missing = <String>[];
  for (final entity in arb.entries) {
    final match = english.entries.firstWhere(
        (legacy) =>
            legacy.value.toLowerCase() == entity.value.toLowerCase() ||
            legacy.key.toLowerCase() == entity.value.toLowerCase(),
        orElse: () => null);

    if (match == null) {
      missing.add(entity.key);
      continue;
    }

    lookupTable[entity.key] = match.key;
  }

  return lookupTable;
}

Map<String, String> _buildTranslations(String filePath) {
  final file = File(filePath);

  var bytes = file.readAsBytesSync();
  var decoder = Utf16BytesToCodeUnitsDecoder(bytes);
  var string = String.fromCharCodes(decoder.decodeRest());

  final lines =
      string.split('\n').where((element) => element.startsWith('"')).toList();

  final lookup = <String, String>{};
  for (final line in lines) {
    final divided =
        line.trim().replaceRange(0, 1, '').replaceAll('";', '').split('" = "');

    lookup[divided.first] = divided.last;
  }

  return lookup;
}
